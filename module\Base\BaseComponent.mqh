//+------------------------------------------------------------------+
//|                                                BaseComponent.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef BASE_COMPONENT_MQH
#define BASE_COMPONENT_MQH

#include "../../../mql4_module/mql4-lib/Lang/String.mqh"
#include "../../../mql4_module/mql4-lib/Collection/Vector.mqh"
#include "../../../mql4_module/mql4-lib/Collection/HashMap.mqh"
#include "../../../mql4_module/mql4-lib/Collection/OrderedIntMap.mqh"

//+------------------------------------------------------------------+
//| Base Error Descriptor Structure                                  |
//+------------------------------------------------------------------+
struct BaseErrorDescriptor
{
    int         errorCode;      // Error code
    string      errorMessage;   // Error message
};

//+------------------------------------------------------------------+
//| Base Error Handler Class                                         |
//+------------------------------------------------------------------+
class BaseErrorHandler
{
private:
    static const string    ERROR_FORMAT;     // Error message format

    string                 m_componentName;  // Component name
    datetime               m_errorTimestamp; // Error timestamp
    BaseErrorDescriptor    m_lastError;     // Last error descriptor

public:
    //--- Constructor and Destructor
                      BaseErrorHandler(string componentName = "BaseComponent");
    virtual          ~BaseErrorHandler();

    // Error setting and clearing
    void            SetComponentName(string componentName) { m_componentName = componentName; }
    void            SetError(int code, string message);
    void            ClearError();
    
    // Error status checking
    bool            HasError() const { return m_lastError.errorCode != 0; }
    
    // Error information retrieval
    string          GetLastComponentName() const { return m_componentName; }
    int             GetLastError() const { return m_lastError.errorCode; }
    string          GetLastErrorMessage() const { return m_lastError.errorMessage; }
    datetime        GetLastErrorTimestamp() const { return m_errorTimestamp; }

    // Error handling
    void            HandleError(int errorCode, string errorMessage);

    //--- Virtual method to be implemented by derived classes
    virtual void    OnError();
};

const string BaseErrorHandler::ERROR_FORMAT = "ERROR [%s]: %s (Code: %d)";    // Error message format : %s=component name, %s=error message, %d=error code

//+------------------------------------------------------------------+
//| Base Error Handler Constructor                                   |
//+------------------------------------------------------------------+
BaseErrorHandler::BaseErrorHandler(string componentName = "BaseComponent")
{
    m_componentName = componentName;
    m_errorTimestamp = 0;
    m_lastError.errorCode = 0;
    m_lastError.errorMessage = "";
}

//+------------------------------------------------------------------+
//| Base Error Handler Destructor                                    |
//+------------------------------------------------------------------+
BaseErrorHandler::~BaseErrorHandler()
{
    // No cleanup required
}

//+------------------------------------------------------------------+
//| Base Error Handler Implementation                                |
//+------------------------------------------------------------------+
void BaseErrorHandler::SetError(int code, string message)
{
    m_lastError.errorCode = code;
    m_lastError.errorMessage = message;
}

//+------------------------------------------------------------------+
//| Clear error information                                          |
//+------------------------------------------------------------------+
void BaseErrorHandler::ClearError()
{
    m_lastError.errorCode = 0;
    m_lastError.errorMessage = "";
}

//+------------------------------------------------------------------+
//| Handle error by logging and alerting                             |
//+------------------------------------------------------------------+
void BaseErrorHandler::HandleError(int errorCode, string errorMessage)
{
    SetError(errorCode, errorMessage);
    m_errorTimestamp = TimeCurrent();
    OnError();
}

//+------------------------------------------------------------------+
//| Base Error Handler OnError Method //XXX dynamic extension        |
//+------------------------------------------------------------------+
void BaseErrorHandler::OnError()
{
    string errorMsg = StringFormat(ERROR_FORMAT, 
                                    m_componentName, 
                                    m_lastError.errorMessage, 
                                    m_lastError.errorCode
                                    );
    Alert(errorMsg);
    Print(errorMsg);
}

//+------------------------------------------------------------------+
//| BaseComponent Class                                              |
//| Base class providing common functionality for all components    |
//| in the EA_Wizard framework                                       |
//+------------------------------------------------------------------+
class BaseComponent
{
private:
    //XXX CODE_ERRORS and g_lockdownError should be encapsulated together, also available for derived classes
    static const BaseErrorDescriptor CODE_ERRORS[]; // Component specific error codes
    static bool                     g_lockdownError;          // Lockdown error handling

    static HashMap<int, string>     g_errorMap;                 // Global error map
    static OrderedIntMap            g_errorCodeCounts;          // Global error code counts
    static bool                     g_hasDuplicateErrorCode;    // Check for duplicate error codes

    string            m_name;              // Component name
    bool              m_initialized;       // Initialization status
    bool              m_enabled;           // Component enabled status
    datetime          m_lastUpdate;        // Last update timestamp
    BaseErrorHandler  m_errorHandler;      // Error handler
    
    //--- Static methods for error handling
    static void       StatErrorCodes();
    static bool       HasDuplicateErrorCode();
    static void       DuplicateErrorCodes(int& codes[]);
    static string     ErrorCodesToString();
    
    protected:
    //--- Protected methods for derived classes
    void              HandleError(int code, string message);
    void              ClearError();
    bool              ValidateParameters();
    //--- Static methods for error handling
    static void       AppendError(int code, string message);
    static void       AppendError(const BaseErrorDescriptor& error[]);
    static string     GetErrorDescription(int errorCode);
    virtual void      SetLockDownError(bool lockdown = true) { g_lockdownError = lockdown; }
    virtual bool      IsErrorLockedDown() { return g_lockdownError; }

public:
    //--- Constructor and Destructor
                      BaseComponent(string name = "BaseComponent");
    virtual          ~BaseComponent();
    
    //--- Initialization lifecycle methods
    virtual bool      Initialize();
    virtual bool      Validate();
    virtual void      Reset();
    virtual void      Cleanup();
    
    //--- Status and information methods
    string            GetName() const { return m_name; }
    bool              IsInitialized() const { return m_initialized; }
    bool              IsEnabled() const { return m_enabled; }
    datetime          GetLastUpdate() const { return m_lastUpdate; }
    
    //--- Error handling methods
    int               GetLastError() const { return m_errorHandler.GetLastError(); }
    string            GetLastErrorMessage() const { return m_errorHandler.GetLastErrorMessage(); }
    bool              HasError() const { return m_errorHandler.HasError() != 0; }
    
    //--- Control methods
    void              Enable() { m_enabled = true; }
    void              Disable() { m_enabled = false; }
    void              SetEnabled(bool enabled) { m_enabled = enabled; }
    
    //--- Update methods
    virtual bool      Update();
    void              UpdateTimestamp() { m_lastUpdate = TimeCurrent(); }
    
    //--- Virtual methods to be implemented by derived classes
    virtual bool      OnInitialize() { return true; }
    virtual bool      OnValidate() { return true; }
    virtual void      OnReset() {}
    virtual void      OnCleanup() {}
    virtual bool      OnUpdate() { return true; }
};

const BaseErrorDescriptor BaseComponent::CODE_ERRORS[] =   // Component specific error codes
{
    // Base component errors
    {10000, "No error"},
    {10001, "Component duplicate initialize"},
    {10002, "Parameter validation failed"},
    {10003, "Component initialization failed"},
    {10004, "Component validation failed"},
    {10005, "Component update failed"},
    {10006, "Duplicate error code"},
    {10007, "Component not initialized to validate"},

    // Add more error codes as needed
    {99998, "Not Existing this error code"},
    {99999, "Unknown error"}
};
HashMap<int, string> BaseComponent::g_errorMap();       // Global error map
OrderedIntMap BaseComponent::g_errorCodeCounts();      // Global error code counts
bool BaseComponent::g_hasDuplicateErrorCode = false;    // Check for duplicate error codes
bool BaseComponent::g_lockdownError = false;          // Lockdown error handling

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
BaseComponent::BaseComponent(string name = "BaseComponent")
{
    m_name = name;
    m_initialized = false;
    m_enabled = true;
    m_lastUpdate = 0;
    m_errorHandler.SetComponentName(m_name);

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
BaseComponent::~BaseComponent()
{
    Cleanup();
}

//+------------------------------------------------------------------+
//| Initialize component                                             |
//+------------------------------------------------------------------+
bool BaseComponent::Initialize()
{
    ClearError();
    StatErrorCodes();

    if (HasDuplicateErrorCode())
    {
        HandleError(10006, GetErrorDescription(10006));
        Print(ErrorCodesToString());
        return false;
    }
    
    if (m_initialized)
    {
        HandleError(10001, GetErrorDescription(10001));
        return false;
    }
    
    if (!ValidateParameters())
    {
        if(GetLastError() == 0)
            HandleError(10002, GetErrorDescription(10002));
        return false;
    }
    
    if (!OnInitialize())
    {
        if(GetLastError() == 0)
            HandleError(10003, GetErrorDescription(10003));
        return false;
    }
    
    m_initialized = true;
    UpdateTimestamp();
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate component parameters                                    |
//+------------------------------------------------------------------+
bool BaseComponent::Validate()
{
    ClearError();

    if (!m_initialized)
    {
        HandleError(10007, GetErrorDescription(10007));
        return false;
    }
    
    if (!OnValidate())
    {
        if(GetLastError() == 0)
            HandleError(10004, GetErrorDescription(10004));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Reset component to initial state                                |
//+------------------------------------------------------------------+
void BaseComponent::Reset()
{
    ClearError();
    m_initialized = false;
    m_lastUpdate = 0;
    OnReset();
}

//+------------------------------------------------------------------+
//| Cleanup component resources                                     |
//+------------------------------------------------------------------+
void BaseComponent::Cleanup()
{
    OnCleanup();
    m_initialized = false;
    m_enabled = false;
}

//+------------------------------------------------------------------+
//| Update component                                                 |
//+------------------------------------------------------------------+
bool BaseComponent::Update()
{
    if (!m_initialized || !m_enabled)
    {
        return false;
    }
    
    ClearError();
    
    if (!OnUpdate())
    {
        if(GetLastError() == 0)
            HandleError(10005, GetErrorDescription(10005));
        return false;
    }
    
    UpdateTimestamp();
    return true;
}

//+------------------------------------------------------------------+
//| Handle error information                                         |
//+------------------------------------------------------------------+
void BaseComponent::HandleError(int code, string message)
{
    m_errorHandler.HandleError(code, message);
}

//+------------------------------------------------------------------+
//| Clear error information                                          |
//+------------------------------------------------------------------+
void BaseComponent::ClearError()
{
    m_errorHandler.ClearError();
}

//+------------------------------------------------------------------+
//| Validate parameters (base implementation)                       |
//+------------------------------------------------------------------+
bool BaseComponent::ValidateParameters()
{
    // Base implementation - always returns true
    // Derived classes should override this method
    return true;
}

//+------------------------------------------------------------------+
//| Add error to global error descriptors                            |
//+------------------------------------------------------------------+
void BaseComponent::AppendError(int code, string message)
{
    g_errorMap.setIfNotExist(code, message);
    g_errorCodeCounts.increment(code);
}

//+------------------------------------------------------------------+
//| Add error to global error descriptors                            |
//+------------------------------------------------------------------+
void BaseComponent::AppendError(const BaseErrorDescriptor& error[])
{
    for(int i = 0; i < ArraySize(error); i++)
    {
        AppendError(error[i].errorCode, error[i].errorMessage);
    }

}

//+------------------------------------------------------------------+
//| Print duplicate error codes                                      |
//+------------------------------------------------------------------+
void BaseComponent::DuplicateErrorCodes(int& codes[])
{   
    Vector<int>* stack = new Vector<int>();

    foreachm(int, code, string, msg, g_errorMap)
    { 
        int index = 0;
        if(!g_errorCodeCounts.hasKey(code, index))
        {
            continue;
        }

        int count = g_errorCodeCounts.value(index);
        if(count > 1)
        {
            stack.push(code);
        }
    }

    stack.toArray(codes);
}

//+------------------------------------------------------------------+
//| Print error codes info                                           |
//+------------------------------------------------------------------+
string BaseComponent::ErrorCodesToString()
{
    string msg = "";

    if(!HasDuplicateErrorCode())
    {
        Vector<string>* codeStack = new Vector<string>();
        Vector<string>* codeMsgStack = new Vector<string>();
        foreachm(int, code, string, codeMsg, g_errorMap)
        {
            codeStack.push((string)code);
            codeMsgStack.push(codeMsg);
        }

        string codes[];
        string codeMsgs[];
        codeStack.toArray(codes);
        codeMsgStack.toArray(codeMsgs);

        msg = StringFormat("Total error code indexs: %d \r\n", g_errorMap.size()); 
        msg += StringFormat("%s", StringPairJoin(codes, codeMsgs, " - ", "\r\n"));
    }
    else
    {
        int codes[];
        DuplicateErrorCodes(codes);
    
        string covertedCodes[];
        for(int i = 0; i < ArraySize(codes); i++)
        {
            covertedCodes[i] = (string)codes[i];
        }
    
        msg = StringFormat("Total duplicate error code indexs: %d, \r\n", ArraySize(codes));
        msg += StringFormat("Duplicate error codes: %s" , StringJoin(covertedCodes, ", "));
    }

    return msg;
}

//+------------------------------------------------------------------+
//| Stat error code                                                  |
//+------------------------------------------------------------------+
void BaseComponent::StatErrorCodes()
{
    int duplicateCodes[];
    DuplicateErrorCodes(duplicateCodes);

    if(ArraySize(duplicateCodes) > 0)
    {
        g_hasDuplicateErrorCode = true;
    }
}

//+------------------------------------------------------------------+
//| Check for duplicate error codes                                  |
//+------------------------------------------------------------------+
bool BaseComponent::HasDuplicateErrorCode()
{
    return g_hasDuplicateErrorCode;
}

//+------------------------------------------------------------------+
//| Get error description based on error code                        |
//+------------------------------------------------------------------+
string BaseComponent::GetErrorDescription(int errorCode)
{
    if(g_errorMap.contains(errorCode))
    {
        return g_errorMap[errorCode];
    }

    return g_errorMap[99998];
}

#endif // BASE_COMPONENT_MQH


//+------------------------------------------------------------------+
//| Module: History/Filter.mqh                                       |
//| This file is part of the mql4-lib project:                       |
//|     https://github.com/dingmaotu/mql4-lib                        |
//|                                                                  |
//| Copyright 2017 Li Ding <<EMAIL>>                       |
//|                                                                  |
//| Licensed under the Apache License, Version 2.0 (the "License");  |
//| you may not use this file except in compliance with the License. |
//| You may obtain a copy of the License at                          |
//|                                                                  |
//|     http://www.apache.org/licenses/LICENSE-2.0                   |
//|                                                                  |
//| Unless required by applicable law or agreed to in writing,       |
//| software distributed under the License is distributed on an      |
//| "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,     |
//| either express or implied.                                       |
//| See the License for the specific language governing permissions  |
//| and limitations under the License.                               |
//+------------------------------------------------------------------+
#property strict
//+------------------------------------------------------------------+
//| Filter interface                                                 |
//| A filter divides the continuous time series data into            |
//| non-overlapped ranges of three types: trending long, trending    |
//| short or ranging                                                 |
//+------------------------------------------------------------------+
interface Filter
  {
   bool isLong();
   bool isShort();
   bool isRanging();
   bool isTrending();
  };
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
class FilterAdapter: public Filter
  {
public:
   virtual bool isLong() {return false;}
   virtual bool isShort() {return false;}
   virtual bool isTrending() {return isLong() || isShort();}
   virtual bool isRanging() {return !isTrending();}
  };
//+------------------------------------------------------------------+

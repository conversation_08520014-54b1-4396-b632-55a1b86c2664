//+------------------------------------------------------------------+
//|                                TradingPipelineContainerManager.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineContainer.mqh"
#include "TradingEvent.mqh"
#include "../mql4-lib/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 簡化的交易流水線容器管理器                                       |
//| 使用統一的 TradingPipelineContainer 替代原有的複雜層次結構      |
//+------------------------------------------------------------------+
class TradingPipelineContainerManager
{
private:
    string m_name;                                      // 管理器名稱
    string m_type;                                      // 管理器類型
    HashMap<int, TradingPipelineContainer*> m_eventContainers;  // 事件類型到容器的映射
    bool m_owned;                                       // 是否擁有容器
    bool m_isEnabled;                                   // 是否啟用
    int m_maxContainers;                                // 最大容器數量
    PipelineResult* m_last_result;                      // 執行結果

public:
    // 構造函數
    TradingPipelineContainerManager(string name = "TradingPipelineContainerManager",
                                   string type = "ContainerManager",
                                   bool owned = false,
                                   int maxContainers = 10)
        : m_name(name),
          m_type(type),
          m_eventContainers(NULL, owned),
          m_owned(owned),
          m_isEnabled(true),
          m_maxContainers(maxContainers),
          m_last_result(new PipelineResult(false, "容器管理器尚未執行", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~TradingPipelineContainerManager()
    {
        Clear();
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    // 設置容器到指定事件類型
    bool SetContainer(ENUM_TRADING_EVENT event, TradingPipelineContainer* container)
    {
        if(container == NULL)
        {
            m_last_result = new PipelineResult(false, "容器指針不能為NULL", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已達到最大數量（如果是新事件類型）
        if(!m_eventContainers.contains((int)event) && m_eventContainers.size() >= m_maxContainers)
        {
            m_last_result = new PipelineResult(false, "已達到最大容器數量限制", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_eventContainers.set((int)event, container);
        m_last_result = new PipelineResult(true, "容器設置成功", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 移除容器
    bool RemoveContainer(TradingPipelineContainer* container)
    {
        if(container == NULL)
        {
            return false;
        }

        // 遍歷所有事件容器，找到匹配的容器並移除
        foreachm(int, eventKey, TradingPipelineContainer*, storedContainer, m_eventContainers)
        {
            if(storedContainer == container)
            {
                return m_eventContainers.remove(eventKey);
            }
        }
        return false;
    }

    // 按名稱移除容器
    bool RemoveContainer(string name)
    {
        // 遍歷所有事件容器，找到匹配名稱的容器並移除
        foreachm(int, eventKey, TradingPipelineContainer*, container, m_eventContainers)
        {
            if(container != NULL && container.GetName() == name)
            {
                return m_eventContainers.remove(eventKey);
            }
        }
        return false;
    }

    // 按事件類型獲取容器
    TradingPipelineContainer* GetContainer(ENUM_TRADING_EVENT event)
    {
        return m_eventContainers.get((int)event, NULL);
    }

    // 按事件類型執行容器
    bool Execute(ENUM_TRADING_EVENT event)
    {
        if(!m_isEnabled)
        {
            return false;
        }

        TradingPipelineContainer* container = m_eventContainers.get((int)event, NULL);
        if(container != NULL && container.IsEnabled())
        {
            container.Execute();
        }

        if(!container.IsExecuted())
        {
            m_last_result = new PipelineResult(false, "有容器執行未完成", GetName(), ERROR_LEVEL_INFO);
            return false;
        }
        if(!container.GetResult().IsSuccess())
        {
            m_last_result = new PipelineResult(false, "有容器執行失敗", GetName(), ERROR_LEVEL_INFO);
            return false;
        }
        m_last_result = new PipelineResult(true, "所有容器執行完成", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 按事件類型重置容器
    void Restore(ENUM_TRADING_EVENT event)
    {
        TradingPipelineContainer* container = m_eventContainers.get((int)event, NULL);
        if(container != NULL)
        {
            container.Restore();
        }
    }

    // 清理所有容器
    void Clear()
    {
        int count = m_eventContainers.size();
        m_eventContainers.clear();
        m_last_result = new PipelineResult(true, StringFormat("已清理 %d 個容器", count), GetName(), ERROR_LEVEL_INFO);
    }

    // 獲取容器數量
    int GetContainerCount() const
    {
        return m_eventContainers.size();
    }

    // 獲取最大容器數量
    int GetMaxContainers() const
    {
        return m_maxContainers;
    }

    // 獲取所有事件類型
    int GetAllEvents(ENUM_TRADING_EVENT &events[])
    {
        int count = m_eventContainers.size();
        ArrayResize(events, count);

        int index = 0;
        foreachm(int, eventKey, TradingPipelineContainer*, container, m_eventContainers)
        {
            events[index] = (ENUM_TRADING_EVENT)eventKey;
            index++;
        }

        return count;
    }

    // 獲取所有容器
    int GetAllContainers(TradingPipelineContainer* &containers[])
    {
        int count = m_eventContainers.size();
        ArrayResize(containers, count);

        int index = 0;
        foreachm(int, eventKey, TradingPipelineContainer*, container, m_eventContainers)
        {
            containers[index] = container;
            index++;
        }

        return count;
    }

    // 獲取管理器名稱
    string GetName() const
    {
        return m_name;
    }

    // 獲取管理器類型
    string GetType() const
    {
        return m_type;
    }

    // 設置啟用狀態
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
    }

    // 檢查是否啟用
    bool IsEnabled() const
    {
        return m_isEnabled;
    }

    // 檢查是否有空位置
    bool HasEmptySlot() const
    {
        return m_eventContainers.size() < m_maxContainers;
    }

    // 檢查是否為空
    bool IsEmpty() const
    {
        return m_eventContainers.size() == 0;
    }

    // 檢查是否已滿
    bool IsFull() const
    {
        return m_eventContainers.size() >= m_maxContainers;
    }

    // 獲取管理器狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat(
            "管理器名稱: %s\n"
            "類型: %s\n"
            "狀態: %s\n"
            "已執行: %s\n"
            "容器數量: %d/%d",
            m_name,
            m_type,
            m_isEnabled ? "啟用" : "禁用",
            m_eventContainers.size(),
            m_maxContainers
        );
        return info;
    }

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }
};

# PipelineAdvance_v1 架構文檔

## 概述

PipelineAdvance_v1 是一個簡化的流水線處理架構，專為外匯交易系統設計。它採用組合模式和模板方法模式，提供了靈活、可擴展的流水線管理解決方案。

## 設計原則

### 1. KISS 原則 (Keep It Simple, Stupid)
- 簡化類別層次結構
- 移除不必要的抽象層
- 專注核心功能

### 2. SOLID 原則

#### 單一職責原則 (SRP)
- **ITradingPipeline**：定義流水線基本契約
- **TradingPipeline**：提供基本實現和模板方法
- **CompositePipeline**：專責組合模式實現
- **PipelineGroup**：專責流水線組管理
- **PipelineResult**：專責結果封裝

#### 開放/封閉原則 (OCP)
- 對擴展開放：可以輕鬆添加新的流水線類型
- 對修改封閉：核心介面和基類保持穩定

#### 里氏替換原則 (LSP)
- 所有 ITradingPipeline 實現都可以互相替換
- CompositePipeline 可以作為 TradingPipeline 使用

#### 介面隔離原則 (ISP)
- ITradingPipeline 介面精簡，只包含必要方法
- 避免強迫實現不需要的方法

#### 依賴倒置原則 (DIP)
- CompositePipeline 依賴於 ITradingPipeline 抽象
- PipelineGroup 依賴於 CompositePipeline 抽象

## 架構層次

```
┌─────────────────────────────────────┐
│           應用層 (Application)        │
│  ┌─────────────────────────────────┐ │
│  │        客戶端代碼               │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│           組織層 (Organization)      │
│  ┌─────────────────────────────────┐ │
│  │       PipelineGroup             │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│           組合層 (Composition)       │
│  ┌─────────────────────────────────┐ │
│  │     CompositePipeline           │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│           抽象層 (Abstraction)       │
│  ┌─────────────────────────────────┐ │
│  │      TradingPipeline            │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│           介面層 (Interface)         │
│  ┌─────────────────────────────────┐ │
│  │     ITradingPipeline            │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 核心組件設計

### 1. ITradingPipeline (介面層)

**職責**：定義流水線的基本契約

**設計特點**：
- 精簡的介面設計
- 只包含必要的方法
- 支援多態性

```mql4
interface ITradingPipeline
{
    void Execute();        // 執行流水線
    string GetName();      // 獲取名稱
    string GetType();      // 獲取類型
    bool IsExecuted();     // 檢查執行狀態
    void Restore();        // 重置狀態
};
```

### 2. TradingPipeline (抽象層)

**職責**：提供流水線的基本實現和模板方法

**設計模式**：模板方法模式

**設計特點**：
- 定義執行流程骨架
- 子類實現具體邏輯
- 統一的狀態管理

```mql4
class TradingPipeline : public ITradingPipeline
{
    // 模板方法
    virtual void Execute() {
        if(m_executed) return;
        Main();                    // 子類實現
        m_executed = true;
    }
    
    // 抽象方法
    virtual void Main() = 0;
};
```

### 3. CompositePipeline (組合層)

**職責**：實現組合模式，管理子流水線

**設計模式**：組合模式

**設計特點**：
- 統一處理單個和組合流水線
- 支援動態添加/移除子流水線
- 遞歸執行和重置

```mql4
class CompositePipeline : public TradingPipeline
{
    Vector<ITradingPipeline*> m_pipelines;
    
    void Main() override {
        // 順序執行所有子流水線
        foreachv(ITradingPipeline*, pipeline, m_pipelines) {
            pipeline.Execute();
        }
    }
};
```

### 4. PipelineGroup (組織層)

**職責**：管理多個複合流水線，支援分組和批量操作

**設計特點**：
- 支援事件類型分組
- 支援啟用/禁用控制
- 提供批量操作介面

## 資料流設計

### 執行流程

```
客戶端
  │
  ▼
PipelineGroup.ExecuteAll()
  │
  ▼
CompositePipeline.Execute()
  │
  ▼
TradingPipeline.Execute()
  │
  ▼
具體實現.Main()
```

### 狀態管理

```
初始狀態 (m_executed = false)
  │
  ▼ Execute()
執行中
  │
  ▼ Main() 完成
已執行 (m_executed = true)
  │
  ▼ Restore()
初始狀態 (m_executed = false)
```

## 錯誤處理策略

### 1. 結果封裝
- 使用 PipelineResult 封裝執行結果
- 包含成功狀態、消息、來源和錯誤級別

### 2. 錯誤級別
```mql4
enum ENUM_ERROR_LEVEL
{
    ERROR_LEVEL_INFO,       // 信息
    ERROR_LEVEL_WARNING,    // 警告
    ERROR_LEVEL_ERROR,      // 錯誤
    ERROR_LEVEL_CRITICAL    // 嚴重錯誤
};
```

### 3. 容錯機制
- 單個流水線失敗不影響其他流水線
- 詳細的錯誤信息記錄
- 支援錯誤恢復

## 記憶體管理

### 1. 所有權模式
- CompositePipeline 支援 owned 模式
- PipelineGroup 支援 owned 模式
- 明確的資源管理責任

### 2. 生命週期管理
```mql4
// 創建
CompositePipeline* composite = new CompositePipeline("name", true); // owned=true

// 使用
composite.AddPipeline(child);
composite.Execute();

// 清理
delete composite; // 自動清理所有子流水線（如果 owned=true）
```

## 性能考量

### 1. 執行效率
- 減少虛函數調用層次
- 避免不必要的狀態檢查
- 使用 Vector 而非陣列提高性能

### 2. 記憶體效率
- 延遲初始化
- 避免不必要的物件創建
- 及時釋放資源

### 3. 可擴展性
- 支援大量子流水線
- 支援深層嵌套結構
- 支援動態配置

## 擴展點

### 1. 新的流水線類型
```mql4
class CustomPipeline : public TradingPipeline
{
protected:
    void Main() override {
        // 實現自定義邏輯
    }
};
```

### 2. 新的執行模式
- 並行執行
- 條件執行
- 異步執行

### 3. 新的組織方式
- 優先級排序
- 依賴關係管理
- 動態路由

## 測試策略

### 1. 單元測試
- 每個類別獨立測試
- 模擬依賴項
- 邊界條件測試

### 2. 整合測試
- 組合流水線測試
- 流水線組測試
- 端到端測試

### 3. 性能測試
- 大量流水線測試
- 深層嵌套測試
- 記憶體洩漏測試

## 最佳實踐

### 1. 命名規範
- 使用描述性名稱
- 遵循一致的命名模式
- 避免縮寫和簡稱

### 2. 錯誤處理
- 總是檢查返回值
- 提供有意義的錯誤消息
- 記錄詳細的執行信息

### 3. 資源管理
- 明確所有權責任
- 及時釋放資源
- 避免循環引用

### 4. 可維護性
- 保持類別職責單一
- 避免過度設計
- 提供充分的文檔

## 與原始模組的比較

| 特性 | 原始 PipelineAdvance | PipelineAdvance_v1 |
|------|---------------------|-------------------|
| 複雜度 | 高（多層繼承、裝飾者模式） | 低（簡化架構） |
| 學習成本 | 高 | 低 |
| 性能 | 中等（多層抽象） | 高（減少抽象層） |
| 可測試性 | 中等 | 高 |
| 可維護性 | 中等 | 高 |
| 擴展性 | 高但複雜 | 高且簡單 |

## 未來發展方向

### 1. 短期目標
- 完善測試覆蓋率
- 添加性能監控
- 改進錯誤處理

### 2. 中期目標
- 支援異步執行
- 添加配置管理
- 實現流水線持久化

### 3. 長期目標
- 支援分散式執行
- 添加視覺化工具
- 實現自動優化

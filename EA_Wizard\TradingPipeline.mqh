//+------------------------------------------------------------------+
//|                                              TradingPipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineBase.mqh"
#include "interface/ITradingPipelineDriver.mqh"

//+------------------------------------------------------------------+
//| 交易流水線類                                                     |
//| 繼承自 TradingPipelineBase，增加階段和驅動器支持                |
//+------------------------------------------------------------------+
class TradingPipeline : public TradingPipelineBase
{
protected:
    ENUM_TRADING_STAGE m_stage;     // 交易階段
    ITradingPipelineDriver* m_driver; // 驅動器引用

public:
    // 建構函數
    TradingPipeline(string name = "",
                   string type = "TradingPipeline",
                   ENUM_TRADING_STAGE stage = INIT_START,
                   ITradingPipelineDriver* driver = NULL)
        : TradingPipelineBase(name, type),
          m_stage(stage),
          m_driver(driver)
          {}

    // 解構函數
    virtual ~TradingPipeline() {}

    // 獲取交易階段
    ENUM_TRADING_STAGE GetStage() const { return m_stage; }

    // 獲取驅動器
    ITradingPipelineDriver* GetDriver() const { return m_driver; }

protected:
    // 實現基類的抽象方法
    virtual void ExecuteInternal() override
    {
        SetResult(true, "流水線執行成功", ERROR_LEVEL_INFO);
        Main();
    }

    // 主程序 - 子類必須實現
    virtual void Main() = 0;
};

//+------------------------------------------------------------------+
//| 簡單交易流水線實現示例                                           |
//+------------------------------------------------------------------+
class SimpleTradingPipeline : public TradingPipeline
{
};

// 定義 PipelineResult 已定義的宏，用於 TradingErrorHandler
#define PIPELINE_RESULT_DEFINED
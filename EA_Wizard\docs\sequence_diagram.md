# PipelineAdvance_v1 模組序列圖

本文檔展示了 PipelineAdvance_v1 模組的主要執行流程，包括基本流水線執行、複合流水線執行和流水線組執行等場景。

## 基本流水線執行流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant TP as TradingPipeline
    participant Impl as 具體實現類

    Client->>TP: new TradingPipeline(name, type)
    TP-->>Client: pipeline instance
    
    Client->>TP: Execute()
    TP->>TP: 檢查 m_executed 狀態
    alt 未執行
        TP->>Impl: Main()
        Impl-->>TP: 執行完成
        TP->>TP: 設置 m_executed = true
        TP-->>Client: 執行完成
    else 已執行
        TP-->>Client: 直接返回（跳過執行）
    end
    
    Client->>TP: IsExecuted()
    TP-->>Client: true/false
    
    Client->>TP: Restore()
    TP->>TP: 設置 m_executed = false
    TP-->>Client: 重置完成
```

## 複合流水線執行流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant CP as CompositePipeline
    participant Child1 as 子流水線1
    participant Child2 as 子流水線2
    participant Vector as Vector<ITradingPipeline*>
    participant Result as PipelineResult

    Client->>CP: new CompositePipeline(name)
    CP->>Vector: 初始化向量
    CP->>Result: new PipelineResult(false, "尚未執行", name)
    CP-->>Client: composite instance
    
    Client->>CP: AddPipeline(child1)
    CP->>CP: 檢查 child1 != NULL
    CP->>CP: 檢查數量限制
    CP->>Vector: add(child1)
    Vector-->>CP: 添加成功
    CP->>Result: new PipelineResult(true, "成功添加子流水線", name)
    CP-->>Client: true
    
    Client->>CP: AddPipeline(child2)
    CP->>Vector: add(child2)
    CP-->>Client: true
    
    Client->>CP: Execute()
    CP->>CP: 檢查 m_executed 狀態
    CP->>CP: Main()
    
    loop 遍歷所有子流水線
        CP->>Child1: Execute()
        Child1->>Child1: Main()
        Child1-->>CP: 執行完成
        
        CP->>Child2: Execute()
        Child2->>Child2: Main()
        Child2-->>CP: 執行完成
    end
    
    CP->>CP: 設置 m_executed = true
    CP-->>Client: 執行完成
```

## 流水線組執行流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant PG as PipelineGroup
    participant CP1 as CompositePipeline1
    participant CP2 as CompositePipeline2
    participant Vector as Vector<CompositePipeline*>
    participant Result as PipelineResult

    Client->>PG: new PipelineGroup(name, description, eventType)
    PG->>Vector: 初始化向量
    PG->>Result: new PipelineResult(false, "尚未執行", name)
    PG-->>Client: group instance
    
    Client->>PG: AddPipeline(cp1)
    PG->>PG: 檢查 cp1 != NULL
    PG->>Vector: add(cp1)
    PG->>Result: new PipelineResult(true, "成功添加流水線", name)
    PG-->>Client: true
    
    Client->>PG: AddPipeline(cp2)
    PG->>Vector: add(cp2)
    PG-->>Client: true
    
    Client->>PG: ExecuteAll()
    PG->>PG: 檢查 m_isEnabled
    
    alt 組已啟用
        loop 遍歷所有複合流水線
            PG->>CP1: Execute()
            CP1->>CP1: 執行所有子流水線
            CP1-->>PG: 執行完成
            
            PG->>CP2: Execute()
            CP2->>CP2: 執行所有子流水線
            CP2-->>PG: 執行完成
        end
        PG->>PG: 設置 m_executed = true
        PG-->>Client: 執行完成
    else 組已禁用
        PG->>Result: new PipelineResult(false, "流水線組已禁用", name)
        PG-->>Client: 跳過執行
    end
```

## 錯誤處理流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant CP as CompositePipeline
    participant Child1 as 正常子流水線
    participant Child2 as 異常子流水線
    participant Result as PipelineResult

    Client->>CP: Execute()
    CP->>CP: Main()
    
    CP->>Child1: Execute()
    Child1->>Child1: Main()
    Child1-->>CP: 執行成功
    
    CP->>Child2: Execute()
    Child2->>Child2: Main()
    Child2-->>CP: 執行失敗
    
    Note over CP: 繼續執行其他子流水線
    Note over CP: 不會因為單個失敗而停止
    
    CP->>CP: 設置 m_executed = true
    CP-->>Client: 執行完成
    
    Client->>CP: GetLastResult()
    CP->>Result: 返回最後的執行結果
    Result-->>Client: PipelineResult instance
```

## 流水線查找流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant CP as CompositePipeline
    participant Vector as Vector<ITradingPipeline*>
    participant Child as 子流水線
    participant Result as PipelineResult

    Client->>CP: FindByName("目標流水線")
    
    loop 遍歷所有子流水線
        CP->>Vector: get(index)
        Vector-->>CP: child pipeline
        CP->>Child: GetName()
        Child-->>CP: pipeline name
        
        alt 名稱匹配
            CP->>Result: new PipelineResult(true, "成功找到子流水線", name)
            CP-->>Client: 返回找到的流水線
        else 名稱不匹配
            Note over CP: 繼續查找下一個
        end
    end
    
    alt 未找到
        CP->>Result: new PipelineResult(false, "未找到指定流水線", name)
        CP-->>Client: 返回 parent 參數
    end
```

## 流水線重置流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant CP as CompositePipeline
    participant Child1 as 子流水線1
    participant Child2 as 子流水線2
    participant Vector as Vector<ITradingPipeline*>

    Client->>CP: Restore()
    
    CP->>CP: TradingPipeline::Restore()
    Note over CP: 重置自身狀態 m_executed = false
    
    loop 遍歷所有子流水線
        CP->>Vector: get(index)
        Vector-->>CP: child pipeline
        
        CP->>Child1: Restore()
        Child1->>Child1: 重置狀態
        Child1-->>CP: 重置完成
        
        CP->>Child2: Restore()
        Child2->>Child2: 重置狀態
        Child2-->>CP: 重置完成
    end
    
    CP-->>Client: 重置完成
```

## 流水線組管理流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant PG as PipelineGroup
    participant CP as CompositePipeline
    participant Vector as Vector<CompositePipeline*>

    Client->>PG: GetPipelineCount()
    PG->>Vector: size()
    Vector-->>PG: count
    PG-->>Client: pipeline count
    
    Client->>PG: SetEnabled(false)
    PG->>PG: m_isEnabled = false
    PG-->>Client: 設置完成
    
    Client->>PG: IsEnabled()
    PG-->>Client: false
    
    Client->>PG: GetStatusInfo()
    PG->>PG: 格式化狀態信息
    PG-->>Client: 狀態字符串
    
    Client->>PG: ContainsPipelineByName("目標流水線")
    PG->>PG: FindPipelineByName("目標流水線")
    
    loop 查找流水線
        PG->>Vector: get(index)
        Vector-->>PG: pipeline
        PG->>CP: GetName()
        CP-->>PG: name
    end
    
    PG-->>Client: true/false
```

## 關鍵特性

### 1. 延遲執行檢查
- 每個流水線都有 `m_executed` 狀態標記
- 避免重複執行同一流水線

### 2. 組合模式實現
- CompositePipeline 可以包含多個子流水線
- 統一的執行介面

### 3. 錯誤容忍
- 單個子流水線失敗不會影響其他流水線
- 詳細的錯誤信息記錄

### 4. 靈活的組織結構
- 支持流水線的分組管理
- 支持動態啟用/禁用

### 5. 統一的重置機制
- 支持遞歸重置所有子流水線
- 恢復到初始狀態

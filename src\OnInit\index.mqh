//+------------------------------------------------------------------+
//|                                                        index.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ONINIT_INDEX_MQH
#define ONINIT_INDEX_MQH

#property strict

//+------------------------------------------------------------------+
//| OnInit Module Includes                                           |
//| Unified entry point for all initialization modules              |
//+------------------------------------------------------------------+

// Include Account Protection Initialization Module
#include "AccountProtectionInit.mqh"

// TODO: Add other OnInit modules here as they are implemented
// Example:
// #include "IndicatorInit.mqh"
// #include "ParameterValidation.mqh"
// #include "RiskManagementInit.mqh"

#endif // ONINIT_INDEX_MQH
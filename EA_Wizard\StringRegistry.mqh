//+------------------------------------------------------------------+
//|                                                StringRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"

//+------------------------------------------------------------------+
//| String 類型註冊器                                               |
//| 專門用於管理 string 類型數據的註冊器                            |
//+------------------------------------------------------------------+
class StringRegistry : public BaseRegistry<string, string>
{
private:
    bool m_caseSensitive;                      // 是否區分大小寫
    int m_maxStringLength;                     // 最大字符串長度

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    StringRegistry(string name = "StringRegistry",
                  string type = "StringRegistry",
                  int maxRegistrations = 50,
                  bool owned = true,
                  bool caseSensitive = true,
                  int maxStringLength = 1024)
        : BaseRegistry<string, string>(name, type, maxRegistrations, owned),
          m_caseSensitive(caseSensitive),
          m_maxStringLength(maxStringLength)
    {
        UpdateResult(true, "String 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~StringRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 註冊 string 值                                                  |
    //+------------------------------------------------------------------+
    virtual bool Register(string key, string value) override
    {
        // 驗證字符串值（在基類檢查之前）
        if(!ValidateStringValue(value))
        {
            UpdateResult(false, StringFormat("無效的字符串值: %s", value), ERROR_LEVEL_ERROR);
            return false;
        }

        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);

        // 處理值的大小寫敏感性
        string processedValue = m_caseSensitive ? value : ConvertToLower(value);

        // 調用基類方法（會自動創建 RegisteredDetail 對象）
        // 注意：我們使用處理後的鍵和值
        bool result = BaseRegistry<string, string>::Register(processedKey, processedValue);

        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 string 值: 鍵='%s', 值='%s'", key, processedValue), ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 註冊 string 值（帶描述）
    virtual bool Register(string key, string value, string description)
    {
        // 驗證字符串長度（在基類檢查之前）
        if(!ValidateStringLength(value))
        {
            UpdateResult(false, StringFormat("字符串長度超過限制: %d > %d", StringLen(value), m_maxStringLength), ERROR_LEVEL_ERROR);
            return false;
        }

        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);

        // 處理值的大小寫敏感性
        string processedValue = m_caseSensitive ? value : ConvertToLower(value);

        // 調用基類方法（會自動創建 RegisteredDetail 對象，帶描述）
        // 注意：我們使用處理後的鍵和值
        bool result = BaseRegistry<string, string>::Register(processedKey, processedValue, description);

        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 string 值（帶描述）: 鍵='%s', 值='%s', 描述='%s'", key, processedValue, description), ERROR_LEVEL_INFO);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 獲取已註冊的 string 值                                          |
    //+------------------------------------------------------------------+
    virtual string GetRegisteredValue(string key, string defaultValue = "") override
    {
        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);

        // 調用基類方法（會自動從 RegisteredDetail 對象中提取值）
        string value = BaseRegistry<string, string>::GetRegisteredValue(processedKey, defaultValue);

        // 如果成功獲取到值（不是默認值），更新結果
        if(IsRegistered(key))
        {
            UpdateResult(true, StringFormat("成功獲取 string 值: 鍵='%s', 值='%s'", key, value), ERROR_LEVEL_INFO);
        }

        return value;
    }

    //+------------------------------------------------------------------+
    //| 更新已註冊的 string 值                                          |
    //+------------------------------------------------------------------+
    virtual bool UpdateRegisteredValue(string key, string newValue) override
    {
        // 驗證字符串值（在基類檢查之前）
        if(!ValidateStringValue(newValue))
        {
            UpdateResult(false, StringFormat("無效的字符串值: %s", newValue), ERROR_LEVEL_ERROR);
            return false;
        }

        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);

        // 處理值的大小寫敏感性
        string processedValue = m_caseSensitive ? newValue : ConvertToLower(newValue);

        // 調用基類方法（會自動更新 RegisteredDetail 對象）
        bool result = BaseRegistry<string, string>::UpdateRegisteredValue(processedKey, processedValue);

        if(result)
        {
            UpdateResult(true, StringFormat("成功更新 string 值: 鍵='%s', 新值='%s'", key, processedValue), ERROR_LEVEL_INFO);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法                                                     |
    //+------------------------------------------------------------------+

    // 檢查指定鍵是否已註冊（考慮大小寫敏感性）
    virtual bool IsRegistered(string key) override
    {
        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);
        return BaseRegistry<string, string>::IsRegistered(processedKey);
    }

    // 取消註冊指定鍵（考慮大小寫敏感性）
    virtual bool Unregister(string key) override
    {
        // 處理鍵的大小寫敏感性
        string processedKey = m_caseSensitive ? key : ConvertToLower(key);

        // 調用基類方法（會自動處理 RegisteredDetail 對象的清理）
        bool result = BaseRegistry<string, string>::Unregister(processedKey);

        if(result)
        {
            UpdateResult(true, StringFormat("成功取消註冊鍵: '%s'", key), ERROR_LEVEL_INFO);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+

    // 設置大小寫敏感性
    void SetCaseSensitive(bool caseSensitive)
    {
        m_caseSensitive = caseSensitive;
        UpdateResult(true, StringFormat("大小寫敏感性已設置為: %s", caseSensitive ? "是" : "否"), ERROR_LEVEL_INFO);
    }

    // 獲取大小寫敏感性設置
    bool IsCaseSensitive()
    {
        return m_caseSensitive;
    }

    // 設置最大字符串長度
    void SetMaxStringLength(int maxLength)
    {
        m_maxStringLength = MathMax(maxLength, 1);
        UpdateResult(true, StringFormat("最大字符串長度已設置為: %d", m_maxStringLength), ERROR_LEVEL_INFO);
    }

    // 獲取最大字符串長度
    int GetMaxStringLength()
    {
        return m_maxStringLength;
    }

    // 獲取所有已註冊的鍵
    int GetAllKeys(string &keys[])
    {
        // 使用基類方法
        return BaseRegistry<string, string>::GetAllKeys(keys);
    }

    // 按字符串內容搜索
    int SearchByContent(string searchText, string &foundKeys[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();

        ArrayResize(foundKeys, totalCount);

        // 處理搜索文本的大小寫
        string processedSearchText = m_caseSensitive ? searchText : ConvertToLower(searchText);

        foreachm(string, key, RegisteredDetail<string>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                string value = detail.GetValue();
                string processedValue = m_caseSensitive ? value : ConvertToLower(value);

                if(StringFind(processedValue, processedSearchText) >= 0)
                {
                    foundKeys[count] = key;
                    count++;
                }
            }
        }

        // 調整數組大小到實際數量
        ArrayResize(foundKeys, count);

        UpdateResult(true, StringFormat("搜索 '%s' 找到 %d 個匹配項", searchText, count), ERROR_LEVEL_INFO);
        return count;
    }

    // 按前綴搜索
    int SearchByPrefix(string prefix, string &foundKeys[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();

        ArrayResize(foundKeys, totalCount);

        // 處理前綴的大小寫
        string processedPrefix = m_caseSensitive ? prefix : ConvertToLower(prefix);
        int prefixLength = StringLen(processedPrefix);

        foreachm(string, key, RegisteredDetail<string>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                string value = detail.GetValue();
                string processedValue = m_caseSensitive ? value : ConvertToLower(value);

                if(StringLen(processedValue) >= prefixLength &&
                   StringSubstr(processedValue, 0, prefixLength) == processedPrefix)
                {
                    foundKeys[count] = key;
                    count++;
                }
            }
        }

        // 調整數組大小到實際數量
        ArrayResize(foundKeys, count);

        UpdateResult(true, StringFormat("前綴搜索 '%s' 找到 %d 個匹配項", prefix, count), ERROR_LEVEL_INFO);
        return count;
    }

    //+------------------------------------------------------------------+
    //| 獲取統計信息                                                     |
    //+------------------------------------------------------------------+
    virtual string GetStatistics()
    {
        string stats = StringFormat(
            "=== String 註冊器統計信息 ===\n"
            "名稱: %s\n"
            "類型: %s\n"
            "已註冊數量: %d\n"
            "最大註冊數: %d\n"
            "使用率: %.1f%%\n"
            "狀態: %s\n"
            "擁有項目: %s\n"
            "大小寫敏感: %s\n"
            "最大字符串長度: %d",
            GetName(),
            GetType(),
            GetRegisteredCount(),
            GetMaxRegistrations(),
            (GetMaxRegistrations() > 0) ? (GetRegisteredCount() * 100.0 / GetMaxRegistrations()) : 0.0,
            IsEnabled() ? "啟用" : "禁用",
            IsOwned() ? "是" : "否",
            m_caseSensitive ? "是" : "否",
            m_maxStringLength
        );

        if(!IsEmpty())
        {
            // 計算字符串統計信息
            int totalLength = 0;
            int minLength = INT_MAX;
            int maxLength = 0;
            string minLengthKey = "", maxLengthKey = "";

            foreachm(string, key, RegisteredDetail<string>*, detail, m_registeredItems)
            {
                if(detail != NULL && detail.IsValid())
                {
                    string value = detail.GetValue();
                    int length = StringLen(value);
                    totalLength += length;

                    if(length < minLength)
                    {
                        minLength = length;
                        minLengthKey = key;
                    }

                    if(length > maxLength)
                    {
                        maxLength = length;
                        maxLengthKey = key;
                    }
                }
            }

            double avgLength = (GetRegisteredCount() > 0) ? (totalLength * 1.0 / GetRegisteredCount()) : 0.0;

            stats += StringFormat(
                "\n=== 字符串統計 ===\n"
                "總字符數: %d\n"
                "平均長度: %.2f\n"
                "最短字符串: %d 字符 (鍵: %s)\n"
                "最長字符串: %d 字符 (鍵: %s)",
                totalLength,
                avgLength,
                minLength, minLengthKey,
                maxLength, maxLengthKey
            );
        }

        return stats;
    }

protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+

    // 驗證字符串值
    bool ValidateStringValue(string value)
    {
        // 檢查字符串長度
        if(StringLen(value) > m_maxStringLength)
        {
            return false;
        }

        return true;
    }

    // 驗證字符串長度（別名方法，為了保持一致性）
    bool ValidateStringLength(string value)
    {
        return ValidateStringValue(value);
    }

    // 將字符串轉換為小寫
    string ConvertToLower(string str)
    {
        string result = str;
        ::StringToLower(result);  // 使用全局作用域的 StringToLower 函數
        return result;
    }

    // 檢查鍵是否已註冊（考慮大小寫敏感性）
    bool IsKeyRegistered(string processedKey)
    {
        return BaseRegistry<string, string>::IsRegistered(processedKey);
    }

    // 重寫鍵值驗證方法
    virtual bool ValidateKey(string key) override
    {
        // 檢查空鍵
        if(StringLen(key) == 0)
        {
            return false;
        }

        return true;
    }
};

//+------------------------------------------------------------------+
//|                                                  EAIndicator.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef EA_INDICATOR_MQH
#define EA_INDICATOR_MQH

#include "Interface/BaseIndicator.mqh"

//+------------------------------------------------------------------+
//| EAIndicator 類別                                                 |
//| 繼承自 BaseIndicator，提供技術指標的具體實作                     |
//| 遵循 EA_Wizard 框架的 EAComponent 格式風格和編碼慣例             |
//+------------------------------------------------------------------+
class EAIndicator : public BaseIndicator
{
private:
    // 錯誤處理相關靜態成員
    static const EAErrorDescriptor      CODE_ERRORS[];   // 組件特定錯誤代碼
    static bool                         g_lockdown;      // 錯誤處理鎖定

private:
    // 指標配置成員變數
    string            m_symbol;           // 交易品種
    ENUM_TIMEFRAMES   m_timeframe;        // 圖表時間框架
    int               m_period;           // 指標週期
    int               m_shift;            // 資料位移

    // 緩衝區管理成員變數
    double            m_buffer[];         // 指標數值緩衝區
    int               m_bufferSize;       // 緩衝區大小

    //+------------------------------------------------------------------+
    //| 錯誤處理方法 - 繼承自 EAComponent                               |
    //+------------------------------------------------------------------+

protected:
    virtual void      LockDown(bool lockdown = true) override;
    virtual bool      IsLockedDown() override { return g_lockdown; }

    //+------------------------------------------------------------------+
    //| 緩衝區管理方法 - 內部數據處理                                    |
    //+------------------------------------------------------------------+

protected:
    bool              ResizeBuffer(int size) { return ArrayResize(m_buffer, size) == -1 ? false : true; }
    int               BufferSize() const { return m_bufferSize; }
    void              SetBufferValue(int index, double value) { m_buffer[index] = value; }
    double            GetBufferValue(int index) const;

public:
    //--- 建構子與解構子
                      EAIndicator(string componentName = "EAIndicator", string symbol = "",
                                 ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                 int period = 14);
    virtual          ~EAIndicator();

    //+------------------------------------------------------------------+
    //| 資訊介面實作 - 指標參數取得                                      |
    //+------------------------------------------------------------------+

public:
    virtual string    GetSymbol() const override { return m_symbol; }
    virtual ENUM_TIMEFRAMES GetTimeframe() const override { return m_timeframe; }
    virtual int       GetPeriod() const override { return m_period; }
    void              SetShift(int shift) { m_shift = shift; }
    int               GetShift() const { return m_shift; }

    //+------------------------------------------------------------------+
    //| 計算介面實作 - 指標數值計算                                      |
    //+------------------------------------------------------------------+

protected:
    double            Calculate(int shift = 0);
    virtual void      OnCalculate(int shift = 0) override { /* 子類別應覆寫此方法 */ }

    //+------------------------------------------------------------------+
    //| 驗證介面實作 - 數據有效性檢查                                    |
    //+------------------------------------------------------------------+

public:
    virtual bool      IsValidShift(int shift) const override { return (shift >= 0 && shift < GetBarsCount()); }
    virtual bool      IsDataAvailable(int shift = 0) const override { return (GetBarsCount() > m_period + shift); }

    //+------------------------------------------------------------------+
    //| 基礎介面實作 - 初始化、驗證與更新                                |
    //+------------------------------------------------------------------+

protected:
    virtual void      OnInitialize() override;
    virtual void      OnValidate() override;
    virtual void      OnUpdate() override;
    virtual void      OnReset() override;
    virtual void      OnCleanup() override;

    //+------------------------------------------------------------------+
    //| 工具介面實作 - 市場數據存取                                      |
    //+------------------------------------------------------------------+

public:
    virtual double    GetPrice(int priceType, int shift = 0) const override;
    virtual datetime  GetTime(int shift = 0) const override { return IsValidShift(shift) ? iTime(m_symbol, m_timeframe, shift) : 0; }
    virtual int       GetBarsCount() const override { return iBars(m_symbol, m_timeframe); }
};

//+------------------------------------------------------------------+
//| EAIndicator 靜態成員變數定義                                    |
//+------------------------------------------------------------------+

// 靜態錯誤代碼陣列定義
static const EAErrorDescriptor EAIndicator::CODE_ERRORS[] = {
    {20001, "無效的交易品種"},
    {20002, "無效的時間框架"},
    {20003, "無效的指標週期"},
    {20004, "緩衝區大小不足"},
    {20005, "數據不足，無法計算指標"},
    {20006, "無效的位移參數"},
};

// 靜態錯誤處理鎖定狀態
static bool EAIndicator::g_lockdown = false;

//+------------------------------------------------------------------+
//| EAIndicator 方法實作                                            |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 建構子 - 初始化所有成員變數                                      |
//+------------------------------------------------------------------+
EAIndicator::EAIndicator(string componentName = "EAIndicator", string symbol = "",
                        ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                        int period = 14) : BaseIndicator(componentName)
{
    // 初始化指標配置參數
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_period = period;
    m_shift = 0;

    // 初始化緩衝區
    m_bufferSize = 0;
    ArrayResize(m_buffer, 0);

    // 設定錯誤處理鎖定狀態
    if(!IsLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    LockDown(true);
}

//+------------------------------------------------------------------+
//| 解構子 - 清理緩衝區和資源                                        |
//+------------------------------------------------------------------+
EAIndicator::~EAIndicator()
{
    // 清理緩衝區
    ArrayFree(m_buffer);
    m_bufferSize = 0;
}

//+------------------------------------------------------------------+
//| 錯誤處理方法實作                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 設定錯誤處理鎖定狀態                                             |
//+------------------------------------------------------------------+
void EAIndicator::LockDown(bool lockdown = true)
{
    g_lockdown = lockdown;

    // 輸出鎖定狀態變更的日誌訊息
    string statusMsg = lockdown ? "已鎖定" : "已解鎖";
    Print(StringFormat("EAIndicator 錯誤處理狀態: %s", statusMsg));
}

//+------------------------------------------------------------------+
//| 計算介面實作                                                     |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 計算指標數值 - 具體實作方法                                       |
//+------------------------------------------------------------------+
double EAIndicator::Calculate(int shift = 0)
{
    // 運行時參數驗證
    Validate();

    // 如果驗證失敗，返回空值
    if(!IsValid())
    {
        return EMPTY_VALUE;
    }

    OnCalculate(shift);

    // 從緩衝區取得計算結果
    return GetBufferValue(shift);
}



//+------------------------------------------------------------------+
//| 工具介面實作                                                     |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 取得價格數據                                                     |
//+------------------------------------------------------------------+
double EAIndicator::GetPrice(int priceType, int shift = 0) const
{
    // 參數驗證
    if (!IsValidShift(shift))
    {
        return EMPTY_VALUE;
    }

    // 根據價格類型返回對應的價格
    switch(priceType)
    {
        case PRICE_OPEN:    return iOpen(m_symbol, m_timeframe, shift);
        case PRICE_HIGH:    return iHigh(m_symbol, m_timeframe, shift);
        case PRICE_LOW:     return iLow(m_symbol, m_timeframe, shift);
        case PRICE_CLOSE:   return iClose(m_symbol, m_timeframe, shift);
        case PRICE_MEDIAN:
            return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift)) / 2.0;
        case PRICE_TYPICAL:
            return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift) +
                   iClose(m_symbol, m_timeframe, shift)) / 3.0;
        case PRICE_WEIGHTED:
            return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift) +
                   2 * iClose(m_symbol, m_timeframe, shift)) / 4.0;
        default:
            return iClose(m_symbol, m_timeframe, shift);
    }
}



//+------------------------------------------------------------------+
//| 緩衝區管理方法實作                                               |
//+------------------------------------------------------------------+



//+------------------------------------------------------------------+
//| 取得緩衝區數值                                                   |
//+------------------------------------------------------------------+
double EAIndicator::GetBufferValue(int index) const
{
    // 參數驗證
    if (index < 0 || index >= m_bufferSize)
    {
        return EMPTY_VALUE;
    }

    return m_buffer[index];
}

//+------------------------------------------------------------------+
//| BaseIndicator 介面實作 - 生命週期方法                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 指標初始化處理                                                   |
//+------------------------------------------------------------------+
void EAIndicator::OnInitialize()
{
    // 執行父類別的初始化邏輯
    EAComponent::OnInitialize();

    // 初始化緩衝區
    ResizeBuffer(m_period + 10); // 預留額外空間
}

//+------------------------------------------------------------------+
//| 指標驗證處理 - 集中所有驗證邏輯                                   |
//+------------------------------------------------------------------+
void EAIndicator::OnValidate()
{
    // 執行父類別的驗證邏輯
    EAComponent::OnValidate();

    // 驗證交易品種
    if (m_symbol == "")
    {
        HandleError(20001, GetErrorDescription(20001));
        return;
    }

    // 驗證時間框架
    if (m_timeframe <= 0)
    {
        HandleError(20002, GetErrorDescription(20002));
        return;
    }

    // 驗證指標週期
    if (m_period <= 0)
    {
        HandleError(20003, GetErrorDescription(20003));
        return;
    }

    // 檢查市場數據可用性
    if (GetBarsCount() < m_period)
    {
        HandleError(20005, GetErrorDescription(20005));
        return;
    }

    // 檢查緩衝區狀態
    if (m_bufferSize <= 0)
    {
        HandleError(20004, GetErrorDescription(20004));
        return;
    }

    // 驗證指標參數範圍
    if (m_period > GetBarsCount())
    {
        HandleError(20003, GetErrorDescription(20003));
        return;
    }


}

//+------------------------------------------------------------------+
//| 指標更新處理                                                     |
//+------------------------------------------------------------------+
void EAIndicator::OnUpdate()
{
    // 執行父類別的更新邏輯
    EAComponent::OnUpdate();

    // 執行指標計算（會自動呼叫 OnCalculate）
    double currentValue = Calculate(m_shift);
}

//+------------------------------------------------------------------+
//| 指標重置處理                                                     |
//+------------------------------------------------------------------+
void EAIndicator::OnReset()
{
    // 執行父類別的重置邏輯
    EAComponent::OnReset();

    // 重置緩衝區大小
    ResizeBuffer(m_period + 10); // 預留額外空間

    // 清空緩衝區
    for (int i = 0; i < m_bufferSize; i++)
    {
        m_buffer[i] = EMPTY_VALUE;
    }
}

//+------------------------------------------------------------------+
//| 指標清理處理                                                     |
//+------------------------------------------------------------------+
void EAIndicator::OnCleanup()
{
    // 執行父類別的清理邏輯
    EAComponent::OnCleanup();

    // 釋放緩衝區記憶體
    ArrayFree(m_buffer);
    m_bufferSize = 0;
}

#endif // EA_INDICATOR_MQH

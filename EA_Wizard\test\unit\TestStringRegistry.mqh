//+------------------------------------------------------------------+
//|                                             TestStringRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../StringRegistry.mqh"

//+------------------------------------------------------------------+
//| StringRegistry 單元測試類                                        |
//+------------------------------------------------------------------+
class TestStringRegistry : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestStringRegistry(TestRunner* runner = NULL)
        : TestCase("TestStringRegistry"), m_runner(runner) {}

    // 析構函數
    virtual ~TestStringRegistry() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 StringRegistry 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestCaseSensitivity();
        TestStringLengthControl();
        TestStringSpecificOperations();
        TestArrayOperations();
        TestValidation();
        TestStatistics();
        TestErrorHandling();

        Print("=== StringRegistry 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 StringRegistry 構造函數 ---");

        // 測試默認構造函數
        StringRegistry* registry1 = new StringRegistry();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 默認構造函數",
                registry1 != NULL,
                "默認構造函數創建成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 默認名稱",
                registry1.GetName() == "StringRegistry",
                "默認名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 默認大小寫敏感",
                registry1.IsCaseSensitive() == true,
                "默認大小寫敏感設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 默認最大字符串長度",
                registry1.GetMaxStringLength() == 1024,
                "默認最大字符串長度設置正確"
            ));
        }

        delete registry1;

        // 測試帶參數構造函數
        StringRegistry* registry2 = new StringRegistry("CustomStringRegistry", "CustomStringType", 20, false, false, 256);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 自定義名稱",
                registry2.GetName() == "CustomStringRegistry",
                "自定義名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 自定義大小寫敏感",
                registry2.IsCaseSensitive() == false,
                "自定義大小寫敏感設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestConstructor - 自定義最大字符串長度",
                registry2.GetMaxStringLength() == 256,
                "自定義最大字符串長度設置正確"
            ));
        }

        delete registry2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 StringRegistry 基本屬性 ---");

        StringRegistry* registry = new StringRegistry("PropTest", "PropType", 10, true, true, 512);

        if(m_runner != NULL)
        {
            // 測試初始狀態
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestBasicProperties - 初始註冊數量",
                registry.GetRegisteredCount() == 0,
                "初始註冊數量為0"
            ));

            // 測試大小寫敏感設置
            registry.SetCaseSensitive(false);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestBasicProperties - 大小寫敏感設置",
                registry.IsCaseSensitive() == false,
                "大小寫敏感設置正確"
            ));

            registry.SetCaseSensitive(true);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestBasicProperties - 重新設置大小寫敏感",
                registry.IsCaseSensitive() == true,
                "重新設置大小寫敏感正確"
            ));

            // 測試最大字符串長度設置
            registry.SetMaxStringLength(256);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestBasicProperties - 最大字符串長度設置",
                registry.GetMaxStringLength() == 256,
                "最大字符串長度設置正確"
            ));

            // 測試最後結果
            PipelineResult* result = registry.GetLastResult();
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestBasicProperties - 最後結果不為空",
                result != NULL,
                "最後結果對象存在"
            ));
        }

        delete registry;
    }

    // 測試大小寫敏感性
    void TestCaseSensitivity()
    {
        Print("--- 測試 StringRegistry 大小寫敏感性 ---");

        // 測試大小寫敏感模式
        StringRegistry* sensitiveRegistry = new StringRegistry("SensitiveTest", "SensitiveType", 5, true, true, 256);

        if(m_runner != NULL)
        {
            // 註冊大小寫不同的鍵
            bool reg1 = sensitiveRegistry.Register("TestKey", "value1");
            bool reg2 = sensitiveRegistry.Register("testkey", "value2");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestCaseSensitivity - 大小寫敏感註冊",
                reg1 && reg2 && sensitiveRegistry.GetRegisteredCount() == 2,
                "大小寫敏感模式下不同大小寫的鍵可以分別註冊"
            ));

            // 測試獲取
            string value1 = sensitiveRegistry.GetRegisteredValue("TestKey", "");
            string value2 = sensitiveRegistry.GetRegisteredValue("testkey", "");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestCaseSensitivity - 大小寫敏感獲取",
                value1 == "value1" && value2 == "value2",
                "大小寫敏感模式下可以正確獲取不同大小寫的值"
            ));
        }

        delete sensitiveRegistry;

        // 測試大小寫不敏感模式
        StringRegistry* insensitiveRegistry = new StringRegistry("InsensitiveTest", "InsensitiveType", 5, true, false, 256);

        if(m_runner != NULL)
        {
            // 註冊第一個鍵
            bool reg1 = insensitiveRegistry.Register("TestKey", "value1");
            // 嘗試註冊大小寫不同但實際相同的鍵
            bool reg2 = insensitiveRegistry.Register("testkey", "value2");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestCaseSensitivity - 大小寫不敏感註冊",
                reg1 && !reg2 && insensitiveRegistry.GetRegisteredCount() == 1,
                "大小寫不敏感模式下相同鍵（不同大小寫）不能重複註冊"
            ));

            // 測試獲取（應該能用不同大小寫獲取到相同值）
            string value1 = insensitiveRegistry.GetRegisteredValue("TestKey", "");
            string value2 = insensitiveRegistry.GetRegisteredValue("TESTKEY", "");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestCaseSensitivity - 大小寫不敏感獲取",
                value1 == "value1" && value2 == "value1",
                "大小寫不敏感模式下可以用不同大小寫獲取相同值"
            ));
        }

        delete insensitiveRegistry;
    }

    // 測試字符串長度控制
    void TestStringLengthControl()
    {
        Print("--- 測試 StringRegistry 字符串長度控制 ---");

        StringRegistry* registry = new StringRegistry("LengthTest", "LengthType", 5, true, true, 10);

        if(m_runner != NULL)
        {
            // 測試正常長度字符串
            bool regNormal = registry.Register("normal", "short");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringLengthControl - 正常長度字符串",
                regNormal == true,
                "正常長度字符串註冊成功"
            ));

            // 測試邊界長度字符串
            bool regBoundary = registry.Register("boundary", "1234567890");  // 正好10個字符
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringLengthControl - 邊界長度字符串",
                regBoundary == true,
                "邊界長度字符串註冊成功"
            ));

            // 測試超長字符串
            bool regTooLong = registry.Register("toolong", "12345678901");  // 11個字符
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringLengthControl - 超長字符串",
                regTooLong == false,
                "超長字符串註冊正確失敗"
            ));

            // 測試空字符串
            bool regEmpty = registry.Register("empty", "");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringLengthControl - 空字符串",
                regEmpty == true,
                "空字符串註冊成功"
            ));

            // 測試更新為超長字符串
            bool updateTooLong = registry.UpdateRegisteredValue("normal", "12345678901");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringLengthControl - 更新為超長字符串",
                updateTooLong == false,
                "更新為超長字符串正確失敗"
            ));
        }

        delete registry;
    }

    // 測試String特定操作
    void TestStringSpecificOperations()
    {
        Print("--- 測試 StringRegistry String特定操作 ---");

        StringRegistry* registry = new StringRegistry("StringTest", "StringType", 5, true, true, 256);

        if(m_runner != NULL)
        {
            // 測試註冊String值
            bool regResult1 = registry.Register("string1", "Hello World");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringSpecificOperations - 註冊String值",
                regResult1 == true,
                "String值註冊成功"
            ));

            // 測試獲取String值
            string value1 = registry.GetRegisteredValue("string1", "");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringSpecificOperations - 獲取String值",
                value1 == "Hello World",
                "String值獲取正確"
            ));

            // 測試更新String值
            bool updateResult = registry.UpdateRegisteredValue("string1", "Updated Value");
            string updatedValue = registry.GetRegisteredValue("string1", "");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringSpecificOperations - 更新String值",
                updateResult && updatedValue == "Updated Value",
                "String值更新成功"
            ));

            // 測試包含特殊字符的字符串
            bool regResult2 = registry.Register("special", "特殊字符!@#$%^&*()");
            string value2 = registry.GetRegisteredValue("special", "");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringSpecificOperations - 特殊字符String",
                regResult2 && value2 == "特殊字符!@#$%^&*()",
                "特殊字符String處理正確"
            ));

            // 測試數字字符串
            bool regResult3 = registry.Register("numbers", "1234567890");
            string value3 = registry.GetRegisteredValue("numbers", "");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStringSpecificOperations - 數字字符串",
                regResult3 && value3 == "1234567890",
                "數字字符串處理正確"
            ));
        }

        delete registry;
    }

    // 測試數組操作
    void TestArrayOperations()
    {
        Print("--- 測試 StringRegistry 數組操作 ---");

        StringRegistry* registry = new StringRegistry("ArrayTest", "ArrayType", 10, true, true, 256);

        if(m_runner != NULL)
        {
            // 註冊多個String值
            registry.Register("array1", "First");
            registry.Register("array2", "Second");
            registry.Register("array3", "Third");

            // 測試獲取所有鍵
            string keys[];
            int keyCount = registry.GetAllKeys(keys);

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestArrayOperations - 獲取所有鍵數量",
                keyCount == 3,
                "獲取所有鍵數量正確"
            ));

            // 測試獲取所有值
            string values[];
            int valueCount = registry.GetAllValues(values);

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestArrayOperations - 獲取所有值數量",
                valueCount == 3,
                "獲取所有值數量正確"
            ));

            // 驗證值的正確性
            bool hasFirst = false, hasSecond = false, hasThird = false;
            for(int i = 0; i < ArraySize(values); i++)
            {
                if(values[i] == "First") hasFirst = true;
                if(values[i] == "Second") hasSecond = true;
                if(values[i] == "Third") hasThird = true;
            }

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestArrayOperations - 值內容正確性",
                hasFirst && hasSecond && hasThird,
                "所有註冊的值都正確獲取"
            ));
        }

        delete registry;
    }

    // 測試驗證功能
    void TestValidation()
    {
        Print("--- 測試 StringRegistry 驗證功能 ---");

        StringRegistry* registry = new StringRegistry("ValidTest", "ValidType", 5, true, true, 20);

        if(m_runner != NULL)
        {
            // 測試有效鍵驗證
            bool validKey = registry.Register("validKey", "validValue");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestValidation - 有效鍵驗證",
                validKey == true,
                "有效鍵驗證通過"
            ));

            // 測試空鍵驗證
            bool emptyKey = registry.Register("", "emptyKeyValue");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestValidation - 空鍵驗證",
                emptyKey == false,
                "空鍵驗證正確失敗"
            ));

            // 測試超長值驗證
            string longValue = "";
            for(int i = 0; i < 25; i++) longValue += "a";  // 25個字符，超過限制20

            bool longValueResult = registry.Register("longValueKey", longValue);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestValidation - 超長值驗證",
                longValueResult == false,
                "超長值驗證正確失敗"
            ));

            // 測試邊界值驗證
            string boundaryValue = "";
            for(int i = 0; i < 20; i++) boundaryValue += "b";  // 正好20個字符

            bool boundaryResult = registry.Register("boundaryKey", boundaryValue);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestValidation - 邊界值驗證",
                boundaryResult == true,
                "邊界值驗證通過"
            ));
        }

        delete registry;
    }

    // 測試統計功能
    void TestStatistics()
    {
        Print("--- 測試 StringRegistry 統計功能 ---");

        StringRegistry* registry = new StringRegistry("StatTest", "StatType", 5, true, true, 256);

        if(m_runner != NULL)
        {
            // 註冊一些值
            registry.Register("stat1", "Value1");
            registry.Register("stat2", "Value2");

            // 測試統計信息
            string stats = registry.GetStatistics();
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStatistics - 統計信息不為空",
                StringLen(stats) > 0,
                "統計信息生成成功"
            ));

            // 檢查統計信息是否包含關鍵信息
            bool hasCount = StringFind(stats, "數量") >= 0 || StringFind(stats, "Count") >= 0;
            bool hasName = StringFind(stats, registry.GetName()) >= 0;
            bool hasCaseSensitive = StringFind(stats, "大小寫") >= 0 || StringFind(stats, "Case") >= 0;
            bool hasMaxLength = StringFind(stats, "長度") >= 0 || StringFind(stats, "Length") >= 0;

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStatistics - 統計信息包含數量",
                hasCount,
                "統計信息包含數量信息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStatistics - 統計信息包含名稱",
                hasName,
                "統計信息包含註冊器名稱"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStatistics - 統計信息包含大小寫敏感",
                hasCaseSensitive,
                "統計信息包含大小寫敏感信息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestStatistics - 統計信息包含最大長度",
                hasMaxLength,
                "統計信息包含最大長度信息"
            ));
        }

        delete registry;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("--- 測試 StringRegistry 錯誤處理 ---");

        StringRegistry* registry = new StringRegistry("ErrorTest", "ErrorType", 2, true, true, 50);

        if(m_runner != NULL)
        {
            // 測試重複註冊
            registry.Register("duplicate", "value1");
            bool duplicateResult = registry.Register("duplicate", "value2");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 重複註冊",
                duplicateResult == false,
                "重複註冊正確失敗"
            ));

            // 測試更新不存在的鍵
            bool updateNonExistent = registry.UpdateRegisteredValue("nonexistent", "newValue");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 更新不存在的鍵",
                updateNonExistent == false,
                "更新不存在的鍵正確失敗"
            ));

            // 測試獲取不存在的鍵
            string defaultValue = "DEFAULT";
            string nonExistentValue = registry.GetRegisteredValue("nonexistent", defaultValue);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 獲取不存在的鍵",
                nonExistentValue == defaultValue,
                "獲取不存在的鍵返回默認值"
            ));

            // 測試禁用狀態下的操作
            registry.SetEnabled(false);
            bool disabledRegister = registry.Register("disabled", "disabledValue");
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 禁用狀態註冊",
                disabledRegister == false,
                "禁用狀態下註冊正確失敗"
            ));

            // 測試超出容量
            registry.SetEnabled(true);
            registry.Register("cap1", "capacity1");  // 第二個項目
            bool overCapacity = registry.Register("cap2", "capacity2");  // 第三個項目，應該失敗
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 超出容量",
                overCapacity == false,
                "超出容量時註冊正確失敗"
            ));

            // 測試無效的最大長度設置
            registry.SetMaxStringLength(-1);  // 負數長度
            int retrievedMaxLength = registry.GetMaxStringLength();
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 負數最大長度處理",
                retrievedMaxLength >= 0,
                "負數最大長度設置被正確處理"
            ));

            // 測試極長字符串
            registry.Clear();
            registry.SetMaxStringLength(1000);
            string veryLongString = "";
            for(int i = 0; i < 1001; i++) veryLongString += "x";  // 1001個字符

            bool veryLongResult = registry.Register("veryLong", veryLongString);
            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 極長字符串處理",
                veryLongResult == false,
                "極長字符串正確被拒絕"
            ));

            // 測試大小寫不敏感模式下的邊界情況
            registry.Clear();
            registry.SetCaseSensitive(false);
            registry.Register("Test", "value1");
            bool caseInsensitiveDuplicate = registry.Register("TEST", "value2");

            m_runner.RecordResult(new TestResult(
                "TestStringRegistry::TestErrorHandling - 大小寫不敏感重複",
                caseInsensitiveDuplicate == false,
                "大小寫不敏感模式下重複鍵正確被拒絕"
            ));
        }

        delete registry;
    }
};

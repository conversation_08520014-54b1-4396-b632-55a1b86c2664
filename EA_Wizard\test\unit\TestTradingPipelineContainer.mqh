//+------------------------------------------------------------------+
//|                                   TestTradingPipelineContainer.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "TestTradingPipeline.mqh"  // 使用 MockTradingPipeline

//+------------------------------------------------------------------+
//| TradingPipelineContainer 單元測試類                              |
//+------------------------------------------------------------------+
class TestTradingPipelineContainer : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineContainer(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineContainer"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineContainer() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineContainer 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestPipelineManagement();
        TestExecuteFlow();
        TestRestoreFunction();
        TestStatusMethods();

        Print("=== TradingPipelineContainer 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineContainer 構造函數 ---");

        // 測試基本構造函數
        TradingPipelineContainer* container1 = new TradingPipelineContainer("TestContainer");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestConstructor - 基本構造函數",
                container1 != NULL,
                container1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        TradingPipelineContainer* container2 = new TradingPipelineContainer(
            "TestContainer2", "測試描述", "TestType", false, 10);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestConstructor - 帶參數構造函數",
                container2 != NULL && container2.GetName() == "TestContainer2",
                container2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        delete container1;
        delete container2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineContainer 基本屬性 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer(
            "PropTest", "屬性測試", "TestType", false, 5);

        // 測試 GetName
        string name = container.GetName();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestBasicProperties - GetName",
                name == "PropTest",
                name == "PropTest" ? "名稱正確" : "名稱錯誤: " + name
            ));
        }

        // 測試 GetType
        string type = container.GetType();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestBasicProperties - GetType",
                type == "TestType",
                type == "TestType" ? "類型正確" : "類型錯誤: " + type
            ));
        }

        // 測試 GetDescription
        string desc = container.GetDescription();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestBasicProperties - GetDescription",
                desc == "屬性測試",
                desc == "屬性測試" ? "描述正確" : "描述錯誤: " + desc
            ));
        }

        // 測試初始狀態
        bool executed = container.IsExecuted();
        bool enabled = container.IsEnabled();
        int count = container.GetPipelineCount();
        int maxCount = container.GetMaxPipelines();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestBasicProperties - 初始狀態",
                !executed && enabled && count == 0 && maxCount == 5,
                "初始狀態正確"
            ));
        }

        delete container;
    }

    // 測試流水線管理
    void TestPipelineManagement()
    {
        Print("--- 測試 TradingPipelineContainer 流水線管理 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("ManageTest", "", "Test", false, 3);

        // 測試添加流水線
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        bool added1 = container.AddPipeline(pipeline1);
        bool added2 = container.AddPipeline(pipeline2);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestPipelineManagement - 添加流水線",
                added1 && added2 && container.GetPipelineCount() == 2,
                "流水線添加成功"
            ));
        }

        // 測試添加空流水線
        bool addedNull = container.AddPipeline(NULL);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestPipelineManagement - 添加空流水線",
                !addedNull,
                "正確拒絕空流水線"
            ));
        }

        // 測試獲取流水線
        ITradingPipeline* retrieved = container.GetPipeline(0);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestPipelineManagement - 獲取流水線",
                retrieved == pipeline1,
                "流水線獲取正確"
            ));
        }

        // 測試 HasPipeline
        bool hasPipeline1 = container.HasPipeline(pipeline1);
        bool hasPipeline2 = container.HasPipeline(pipeline2);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestPipelineManagement - HasPipeline",
                hasPipeline1 && hasPipeline2,
                "HasPipeline 方法正確"
            ));
        }

        // 測試移除流水線
        bool removed = container.RemovePipeline(pipeline1);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestPipelineManagement - 移除流水線",
                removed && container.GetPipelineCount() == 1,
                "流水線移除成功"
            ));
        }

        delete container;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試執行流程
    void TestExecuteFlow()
    {
        Print("--- 測試 TradingPipelineContainer 執行流程 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("ExecuteTest");

        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Exec1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Exec2");

        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        // 測試執行
        container.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestExecuteFlow - 執行流程",
                container.IsExecuted() && pipeline1.IsExecuted() && pipeline2.IsExecuted(),
                "容器和子流水線都已執行"
            ));
        }

        // 測試重複執行防護
        pipeline1.Restore();  // 重置子流水線
        container.Execute();  // 再次執行容器

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestExecuteFlow - 重複執行防護",
                !pipeline1.IsExecuted(),  // 子流水線不應該再次執行
                "重複執行被正確防護"
            ));
        }

        delete container;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試重置功能
    void TestRestoreFunction()
    {
        Print("--- 測試 TradingPipelineContainer 重置功能 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("RestoreTest");

        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Restore1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Restore2");

        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        // 先執行
        container.Execute();
        bool containerExecutedBefore = container.IsExecuted();
        bool pipeline1ExecutedBefore = pipeline1.IsExecuted();

        // 然後重置
        container.Restore();
        bool containerExecutedAfter = container.IsExecuted();
        bool pipeline1ExecutedAfter = pipeline1.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestRestoreFunction - 重置功能",
                containerExecutedBefore && pipeline1ExecutedBefore &&
                !containerExecutedAfter && !pipeline1ExecutedAfter,
                "容器和子流水線都被正確重置"
            ));
        }

        delete container;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試狀態方法
    void TestStatusMethods()
    {
        Print("--- 測試 TradingPipelineContainer 狀態方法 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("StatusTest", "", "Test", false, 2);

        // 測試 IsEmpty
        bool emptyBefore = container.IsEmpty();

        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Status1");
        container.AddPipeline(pipeline1);

        bool emptyAfter = container.IsEmpty();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestStatusMethods - IsEmpty",
                emptyBefore && !emptyAfter,
                "IsEmpty 方法正確"
            ));
        }

        // 測試 IsFull
        bool fullBefore = container.IsFull();

        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Status2");
        container.AddPipeline(pipeline2);

        bool fullAfter = container.IsFull();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestStatusMethods - IsFull",
                !fullBefore && fullAfter,
                "IsFull 方法正確"
            ));
        }

        // 測試 SetEnabled/IsEnabled
        container.SetEnabled(false);
        bool disabledState = container.IsEnabled();

        container.SetEnabled(true);
        bool enabledState = container.IsEnabled();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainer::TestStatusMethods - SetEnabled/IsEnabled",
                !disabledState && enabledState,
                "啟用/禁用功能正確"
            ));
        }

        delete container;
        delete pipeline1;
        delete pipeline2;
    }
};

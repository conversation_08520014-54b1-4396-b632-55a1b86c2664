#property strict

//+------------------------------------------------------------------+
//| Account Protection Enumerations                                  |
//|                                                                  |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Protection Level Enumeration                                     |
//+------------------------------------------------------------------+
enum ENUM_PROTECTION_LEVEL
{
    PROTECTION_NONE = 0,        // No protection
    PROTECTION_BASIC = 1,       // Basic protection
    PROTECTION_MODERATE = 2,    // Moderate protection
    PROTECTION_STRICT = 3,      // Strict protection
    PROTECTION_CUSTOM = 4       // Custom protection
};

//+------------------------------------------------------------------+
//| Protection Status Enumeration                                    |
//+------------------------------------------------------------------+
enum ENUM_PROTECTION_STATUS
{
    STATUS_NORMAL = 0,          // Normal operation
    STATUS_WARNING = 1,         // Warning level
    STATUS_CRITICAL = 2,        // Critical level
    STATUS_EMERGENCY = 3        // Emergency stop
};

//+------------------------------------------------------------------+
//| Module: History/Trigger.mqh                                      |
//| This file is part of the mql4-lib project:                       |
//|     https://github.com/dingmaotu/mql4-lib                        |
//|                                                                  |
//| Copyright 2017 Li Ding <<EMAIL>>                       |
//|                                                                  |
//| Licensed under the Apache License, Version 2.0 (the "License");  |
//| you may not use this file except in compliance with the License. |
//| You may obtain a copy of the License at                          |
//|                                                                  |
//|     http://www.apache.org/licenses/LICENSE-2.0                   |
//|                                                                  |
//| Unless required by applicable law or agreed to in writing,       |
//| software distributed under the License is distributed on an      |
//| "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,     |
//| either express or implied.                                       |
//| See the License for the specific language governing permissions  |
//| and limitations under the License.                               |
//+------------------------------------------------------------------+
#property strict
//+------------------------------------------------------------------+
//| Trigger interface                                                |
//| A trigger is activated at a certain time point in the continuous |
//| time series data, and it indicates the intended position type    |
//+------------------------------------------------------------------+
interface Trigger
  {
   bool isActivated() const;
   bool isLong() const;
   bool isShort() const;
  };
//+------------------------------------------------------------------+
//| Implements the default methods                                   |
//+------------------------------------------------------------------+
class TriggerAdapter: public Trigger
  {
public:
   virtual bool isActivated() const {return isLong() || isShort(); }
   virtual bool isLong() const {return false;}
   virtual bool isShort() const {return false;}
  };
//+------------------------------------------------------------------+

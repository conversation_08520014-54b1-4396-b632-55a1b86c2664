#property strict

#include "Interface/BaseComponent.mqh"
#include "Components/EAErrorStorage.mqh"

//+------------------------------------------------------------------+
//| EAComponent 類別                                                 |
//| 實作所有 BaseComponent 介面層次結構的虛擬方法                    |
//| 遵循 EA_Wizard 框架的編碼慣例和錯誤處理模式                      |
//+------------------------------------------------------------------+
class EAComponent : BaseComponent
{
private:
    // 錯誤處理相關靜態成員
    static const EAErrorDescriptor      CODE_ERRORS[];   // 全局錯誤代碼
    static bool                         g_lockdown;      // 錯誤處理鎖定
    static EAErrorStorage               g_errorStorage;  // 全局錯誤存儲器

protected:
    // 錯誤處理相關靜態方法
    void                 AppendError(const EAErrorDescriptor& errors[]);   // 將錯誤代碼和描述添加到存儲中
    string               GetErrorDescription(int errorCode);        // 根據錯誤代碼取得錯誤描述
    virtual void         LockDown(bool lockdown);    // 鎖定錯誤處理
    virtual bool         IsLockedDown() { return g_lockdown; }       // 檢查是否鎖定錯誤處理

private:
    // 錯誤處理相關成員變數
    int                 m_lastErrorCode;        // 最後錯誤代碼
    string              m_lastErrorMessage;     // 最後錯誤訊息
    datetime            m_errorTimestamp;       // 錯誤時間戳

    // 狀態管理相關成員變數
    bool                m_initialized;          // 初始化狀態
    bool                m_valid;               // 驗證狀態
    bool                m_enabled;             // 啟用狀態
    datetime            m_lastUpdate;          // 最後更新時間
    string              m_componentName;       // 組件名稱

    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+

public:
                        EAComponent(string componentName = "EAComponent");
    virtual            ~EAComponent();

    //+------------------------------------------------------------------+
    //| BaseErrorHandler 介面實作                                       |
    //+------------------------------------------------------------------+

public:
    virtual void        HandleError(int errorCode, string errorMessage) override;
    virtual void        ClearError() override;
    virtual bool        HasError() const override { return m_lastErrorCode != 0; }
    virtual int         LastError() const override { return m_lastErrorCode; }
    virtual string      LastErrorMessage() const override { return m_lastErrorMessage; }

    //+------------------------------------------------------------------+
    //| BaseInitializable 介面實作                                      |
    //+------------------------------------------------------------------+

public:
    virtual void        Initialize() override;
    virtual bool        IsInitialized() const override { return m_initialized; }
protected:
    virtual void        OnInitialize() override { /* 預設空實作 - 子類別可覆寫 */ }

    //+------------------------------------------------------------------+
    //| BaseValidatable 介面實作                                        |
    //+------------------------------------------------------------------+

public:
    virtual void        Validate() override;
    virtual bool        IsValid() const override { return m_valid; }
protected:
    virtual void        OnValidate() override { /* 預設空實作 - 子類別可覆寫 */ }

    //+------------------------------------------------------------------+
    //| BaseUpdatable 介面實作                                          |
    //+------------------------------------------------------------------+

public:
    virtual void        Update() override;
protected:
    virtual void        OnUpdate() override { /* 預設空實作 - 子類別可覆寫 */ }

    //+------------------------------------------------------------------+
    //| BaseResetable 介面實作                                          |
    //+------------------------------------------------------------------+

public:
    virtual void        Reset() override;
protected:
    virtual void        OnReset() override { /* 預設空實作 - 子類別可覆寫 */ }

    //+------------------------------------------------------------------+
    //| BaseCleanable 介面實作                                          |
    //+------------------------------------------------------------------+

public:
    virtual void        Cleanup() override;
protected:
    virtual void        OnCleanup() override { /* 預設空實作 - 子類別可覆寫 */ }

    //+------------------------------------------------------------------+
    //| 輔助方法 - 狀態管理和資訊取得                                    |
    //+------------------------------------------------------------------+

public:
    string              GetComponentName() const { return m_componentName; }
    void                Enable() { m_enabled = true; }
    void                Disable() { m_enabled = false; }
    bool                IsEnabled() const { return m_enabled; }
    datetime            GetLastUpdate() const { return m_lastUpdate; }
    datetime            GetErrorTimestamp() const { return m_errorTimestamp; }
    string              GetStatusSummary() const;
private:
    void                SetComponentName(string name) { m_componentName = name; }
};
//+------------------------------------------------------------------+
//| EAComponent 靜態成員變數定義                                    |
//+------------------------------------------------------------------+

// 靜態錯誤代碼陣列定義
static const EAErrorDescriptor EAComponent::CODE_ERRORS[] = {
    {10001, "組件已經初始化"},
    {10002, "組件尚未初始化，無法進行驗證"},
    {10003, "組件尚未初始化，無法進行更新"},
    {10004, "組件已禁用，無法進行更新"}
};

// 靜態錯誤處理鎖定狀態
static bool EAComponent::g_lockdown = false;

// 靜態全局錯誤存儲器
static EAErrorStorage EAComponent::g_errorStorage;

//+------------------------------------------------------------------+
//| EAComponent 方法實作                                            |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 建構子 - 初始化所有成員變數                                      |
//+------------------------------------------------------------------+
EAComponent::EAComponent(string componentName = "EAComponent")
{
    m_componentName = componentName;
    m_lastErrorCode = 0;
    m_lastErrorMessage = "";
    m_errorTimestamp = 0;
    m_initialized = false;
    m_valid = false;
    m_enabled = true;
    m_lastUpdate = 0;

    // 初始化錯誤代碼到全局存儲器
    if(!IsLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    LockDown(true);
}

//+------------------------------------------------------------------+
//| 解構子 - 執行清理操作                                            |
//+------------------------------------------------------------------+
EAComponent::~EAComponent()
{
    Cleanup();
}

//+------------------------------------------------------------------+
//| BaseErrorHandler 介面實作                                       |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 處理錯誤 - 記錄錯誤代碼和訊息                                    |
//+------------------------------------------------------------------+
void EAComponent::HandleError(int errorCode, string errorMessage)
{
    m_lastErrorCode = errorCode;
    m_lastErrorMessage = errorMessage;
    m_errorTimestamp = TimeCurrent();

    // 輸出錯誤訊息到日誌
    string errorMsg = StringFormat("ERROR [%s]: %s (Code: %d)",
                                  m_componentName,
                                  errorMessage,
                                  errorCode);
    Print(errorMsg);
}

//+------------------------------------------------------------------+
//| 清除錯誤狀態                                                     |
//+------------------------------------------------------------------+
void EAComponent::ClearError()
{
    m_lastErrorCode = 0;
    m_lastErrorMessage = "";
    m_errorTimestamp = 0;
}



//+------------------------------------------------------------------+
//| BaseInitializable 介面實作                                      |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 初始化組件 - 主要初始化邏輯                                      |
//+------------------------------------------------------------------+
void EAComponent::Initialize()
{
    ClearError();

    if (m_initialized)
    {
        HandleError(10001, GetErrorDescription(10001));
        return;
    }

    // 執行具體初始化邏輯
    OnInitialize();

    // 驗證組件
    Validate();

    if (!HasError())
    {
        m_initialized = true;
        m_lastUpdate = TimeCurrent();
    }
}



//+------------------------------------------------------------------+
//| BaseValidatable 介面實作                                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 驗證組件 - 主要驗證邏輯                                          |
//+------------------------------------------------------------------+
void EAComponent::Validate()
{
    ClearError();

    if (!m_initialized)
    {
        HandleError(10002, GetErrorDescription(10002));
        return;
    }

    // 執行具體驗證邏輯
    OnValidate();

    if (!HasError())
    {
        m_valid = true;
    }
    else
    {
        m_valid = false;
    }
}



//+------------------------------------------------------------------+
//| BaseUpdatable 介面實作                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 更新組件 - 主要更新邏輯                                          |
//+------------------------------------------------------------------+
void EAComponent::Update()
{
    ClearError();

    if (!m_initialized)
    {
        HandleError(10003, GetErrorDescription(10003));
        return;
    }

    if (!m_enabled)
    {
        HandleError(10004, GetErrorDescription(10004));
        return;
    }

    // 執行具體更新邏輯
    OnUpdate();

    if (!HasError())
    {
        m_lastUpdate = TimeCurrent();
    }
}



//+------------------------------------------------------------------+
//| BaseResetable 介面實作                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 重置組件 - 主要重置邏輯                                          |
//+------------------------------------------------------------------+
void EAComponent::Reset()
{
    ClearError();

    // 執行具體重置邏輯
    OnReset();

    if (!HasError())
    {
        // 重置狀態變數但保持初始化狀態
        m_valid = false;
        m_lastUpdate = 0;
    }
}


//+------------------------------------------------------------------+
//| BaseCleanable 介面實作                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 清理組件 - 主要清理邏輯                                          |
//+------------------------------------------------------------------+
void EAComponent::Cleanup()
{
    ClearError();

    // 執行具體清理邏輯
    OnCleanup();

    // 重置所有狀態
    m_initialized = false;
    m_valid = false;
    m_enabled = false;
    m_lastUpdate = 0;
}





//+------------------------------------------------------------------+
//| 取得組件狀態摘要                                                 |
//+------------------------------------------------------------------+
string EAComponent::GetStatusSummary() const
{
    return StringFormat("組件[%s] - 初始化:%s, 有效:%s, 啟用:%s, 錯誤:%s",
                       m_componentName,
                       m_initialized ? "是" : "否",
                       m_valid ? "是" : "否",
                       m_enabled ? "是" : "否",
                       HasError() ? StringFormat("是(代碼:%d)", m_lastErrorCode) : "否");
}

//+------------------------------------------------------------------+
//| 錯誤處理相關靜態方法實作                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 將錯誤代碼和描述添加到全局錯誤存儲中                               |
//+------------------------------------------------------------------+
void EAComponent::AppendError(const EAErrorDescriptor& errors[])
{
    // 檢查錯誤處理是否被鎖定
    if (g_lockdown)
    {
        Print("警告: 錯誤處理已被鎖定，無法添加錯誤");
        return;
    }

    // 將錯誤陣列添加到全局錯誤存儲器
    g_errorStorage.AppendError(errors);

    // 輸出添加錯誤的日誌訊息
    int errorCount = ArraySize(errors);
    Print(StringFormat("已添加 %d 個錯誤到全局錯誤存儲器", errorCount));
}

//+------------------------------------------------------------------+
//| 根據錯誤代碼取得錯誤描述                                          |
//+------------------------------------------------------------------+
string EAComponent::GetErrorDescription(int errorCode)
{
    // 檢查全局錯誤存儲器中是否有此錯誤代碼
    if (g_errorStorage.ContainsError(errorCode))
    {
        return g_errorStorage.GetErrorDescription(errorCode);
    }

    // 返回預設錯誤訊息
    return StringFormat("未知錯誤代碼: %d", errorCode);
}

//+------------------------------------------------------------------+
//| 設定錯誤處理鎖定狀態                                             |
//+------------------------------------------------------------------+
void EAComponent::LockDown(bool lockdown)
{
    g_lockdown = lockdown;

    // 輸出鎖定狀態變更的日誌訊息
    string statusMsg = lockdown ? "已鎖定" : "已解鎖";
    Print(StringFormat("錯誤處理狀態: %s", statusMsg));
}

// struct OrderStats{
//     int totalOrders;
//     double totalProfit;
//     double totalLots;
// };
// struct OrderGroupStats{
//     int totalGroups;
//     int activeGroups;
//     int emptyGroups;
//     OrderStats orderStats;
// };

// interface Refreshable
// {
//     void Refresh();
// };

// class OrderGroup : public Refreshable{
//     OrderStats GetStatistics(){}
//     void Refresh(){}
// };
// class OrderGroupSet : public Refreshable{
//     OrderGroupStats GetStatistics(){}
//     void Refresh(){}
// };
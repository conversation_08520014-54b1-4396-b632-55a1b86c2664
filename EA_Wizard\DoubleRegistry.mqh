//+------------------------------------------------------------------+
//|                                                DoubleRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"

//+------------------------------------------------------------------+
//| Double 類型註冊器                                               |
//| 專門用於管理 double 類型數據的註冊器                            |
//+------------------------------------------------------------------+
class DoubleRegistry : public BaseRegistry<string, double>
{
private:
    double m_precision;                        // 精度控制

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    DoubleRegistry(string name = "DoubleRegistry",
                  string type = "DoubleRegistry",
                  int maxRegistrations = 50,
                  bool owned = true,
                  double precision = 0.00001)
        : BaseRegistry<string, double>(name, type, maxRegistrations, owned),
          m_precision(precision)
    {
        UpdateResult(true, "Double 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~DoubleRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 註冊 double 值                                                  |
    //+------------------------------------------------------------------+
    virtual bool Register(string key, double value) override
    {
        // 驗證 double 值（在基類檢查之前）
        if(!ValidateDoubleValue(value))
        {
            UpdateResult(false, StringFormat("無效的 double 值: %.5f", value), ERROR_LEVEL_ERROR);
            return false;
        }

        // 調用基類方法（會自動創建 RegisteredDetail 對象）
        bool result = BaseRegistry<string, double>::Register(key, value);

        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 double 值: 鍵='%s', 值=%.5f", key, value), ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 註冊 double 值（帶描述）
    virtual bool Register(string key, double value, string description)
    {
        // 驗證 double 值（在基類檢查之前）
        if(!ValidateDoubleValue(value))
        {
            UpdateResult(false, StringFormat("無效的 double 值: %.5f", value), ERROR_LEVEL_ERROR);
            return false;
        }

        // 調用基類方法（會自動創建 RegisteredDetail 對象，帶描述）
        bool result = BaseRegistry<string, double>::Register(key, value, description);

        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 double 值（帶描述）: 鍵='%s', 值=%.5f, 描述='%s'", key, value, description), ERROR_LEVEL_INFO);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 獲取已註冊的 double 值                                          |
    //+------------------------------------------------------------------+
    virtual double GetRegisteredValue(string key, double defaultValue = 0.0) override
    {
        // 調用基類方法（會自動從 RegisteredDetail 對象中提取值）
        double value = BaseRegistry<string, double>::GetRegisteredValue(key, defaultValue);

        // 如果成功獲取到值（不是默認值），更新結果
        if(IsRegistered(key))
        {
            UpdateResult(true, StringFormat("成功獲取 double 值: 鍵='%s', 值=%.5f", key, value), ERROR_LEVEL_INFO);
        }

        return value;
    }

    //+------------------------------------------------------------------+
    //| 更新已註冊的 double 值                                          |
    //+------------------------------------------------------------------+
    virtual bool UpdateRegisteredValue(string key, double newValue) override
    {
        // 驗證新的 double 值（在基類檢查之前）
        if(!ValidateDoubleValue(newValue))
        {
            UpdateResult(false, StringFormat("無效的新 double 值: %.5f", newValue), ERROR_LEVEL_ERROR);
            return false;
        }

        // 調用基類方法（會自動更新 RegisteredDetail 對象）
        bool result = BaseRegistry<string, double>::UpdateRegisteredValue(key, newValue);

        if(result)
        {
            UpdateResult(true, StringFormat("成功更新 double 值: 鍵='%s', 新值=%.5f", key, newValue), ERROR_LEVEL_INFO);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 專用方法（基類方法已足夠，無需重寫）                             |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+

    // 設置精度
    void SetPrecision(double precision)
    {
        m_precision = MathMax(precision, 0.00001);
        UpdateResult(true, StringFormat("精度已設置為: %.8f", m_precision), ERROR_LEVEL_INFO);
    }

    // 獲取精度
    double GetPrecision()
    {
        return m_precision;
    }

    // 獲取所有已註冊的鍵
    int GetAllKeys(string &keys[])
    {
        // 使用基類方法
        return BaseRegistry<string, double>::GetAllKeys(keys);
    }

    // 獲取指定範圍內的值
    int GetValuesInRange(double minValue, double maxValue, string &keys[], double &values[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();

        ArrayResize(keys, totalCount);
        ArrayResize(values, totalCount);

        foreachm(string, key, RegisteredDetail<double>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                double value = detail.GetValue();
                if(value >= minValue && value <= maxValue)
                {
                    keys[count] = key;
                    values[count] = value;
                    count++;
                }
            }
        }

        // 調整數組大小到實際數量
        ArrayResize(keys, count);
        ArrayResize(values, count);

        UpdateResult(true, StringFormat("找到 %d 個在範圍 [%.5f, %.5f] 內的值", count, minValue, maxValue), ERROR_LEVEL_INFO);
        return count;
    }

    // 計算所有註冊值的總和
    double CalculateSum()
    {
        double sum = 0.0;

        foreachm(string, key, RegisteredDetail<double>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                sum += detail.GetValue();
            }
        }

        UpdateResult(true, StringFormat("計算總和完成: %.5f", sum), ERROR_LEVEL_INFO);
        return sum;
    }

    // 計算平均值
    double CalculateAverage()
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法計算平均值", ERROR_LEVEL_WARNING);
            return 0.0;
        }

        double average = CalculateSum() / GetRegisteredCount();
        UpdateResult(true, StringFormat("計算平均值完成: %.5f", average), ERROR_LEVEL_INFO);
        return average;
    }

    // 查找最大值
    double FindMaxValue(string &maxKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最大值", ERROR_LEVEL_WARNING);
            maxKey = "";
            return 0.0;
        }

        double maxValue = -DBL_MAX;
        maxKey = "";

        foreachm(string, key, RegisteredDetail<double>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                double value = detail.GetValue();
                if(value > maxValue)
                {
                    maxValue = value;
                    maxKey = key;
                }
            }
        }

        UpdateResult(true, StringFormat("找到最大值: 鍵='%s', 值=%.5f", maxKey, maxValue), ERROR_LEVEL_INFO);
        return maxValue;
    }

    // 查找最小值
    double FindMinValue(string &minKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最小值", ERROR_LEVEL_WARNING);
            minKey = "";
            return 0.0;
        }

        double minValue = DBL_MAX;
        minKey = "";

        foreachm(string, key, RegisteredDetail<double>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                double value = detail.GetValue();
                if(value < minValue)
                {
                    minValue = value;
                    minKey = key;
                }
            }
        }

        UpdateResult(true, StringFormat("找到最小值: 鍵='%s', 值=%.5f", minKey, minValue), ERROR_LEVEL_INFO);
        return minValue;
    }

protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+

    // 驗證 double 值
    bool ValidateDoubleValue(double value)
    {
        // 檢查是否為有效數字
        if(value != value) // NaN 檢查
        {
            return false;
        }

        // 檢查是否為無限大
        if(MathIsValidNumber(value) == false)
        {
            return false;
        }

        return true;
    }

    // 比較兩個 double 值是否相等（考慮精度）
    bool IsDoubleEqual(double value1, double value2)
    {
        double diff = MathAbs(value1 - value2);

        // 使用 <= 來處理邊界情況，並添加一個極小的容差來處理浮點數精度問題
        // 1e-14 是一個足夠小的容差，不會影響正常的精度比較
        return diff <= m_precision + 1e-14;
    }

public:
    //+------------------------------------------------------------------+
    //| 比較兩個 double 值                                               |
    //+------------------------------------------------------------------+
    bool CompareValues(double value1, double value2)
    {
        return IsDoubleEqual(value1, value2);
    }

    //+------------------------------------------------------------------+
    //| 獲取統計信息                                                     |
    //+------------------------------------------------------------------+
    virtual string GetStatistics()
    {
        string stats = StringFormat(
            "=== Double 註冊器統計信息 ===\n"
            "名稱: %s\n"
            "類型: %s\n"
            "已註冊數量: %d\n"
            "最大註冊數: %d\n"
            "使用率: %.1f%%\n"
            "狀態: %s\n"
            "擁有項目: %s\n"
            "精度: %.8f",
            GetName(),
            GetType(),
            GetRegisteredCount(),
            GetMaxRegistrations(),
            (GetMaxRegistrations() > 0) ? (GetRegisteredCount() * 100.0 / GetMaxRegistrations()) : 0.0,
            IsEnabled() ? "啟用" : "禁用",
            IsOwned() ? "是" : "否",
            m_precision
        );

        if(!IsEmpty())
        {
            double sum = CalculateSum();
            string maxKey, minKey;
            double maxValue = FindMaxValue(maxKey);
            double minValue = FindMinValue(minKey);

            stats += StringFormat(
                "\n=== 數值統計 ===\n"
                "總和: %.5f\n"
                "最大值: %.5f (鍵: %s)\n"
                "最小值: %.5f (鍵: %s)\n"
                "平均值: %.5f",
                sum,
                maxValue, maxKey,
                minValue, minKey,
                (GetRegisteredCount() > 0) ? (sum / GetRegisteredCount()) : 0.0
            );
        }

        return stats;
    }
};

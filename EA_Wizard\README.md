# EA Wizard Framework

## Introduction

The EA Wizard framework provides a powerful and flexible architecture for developing Expert Advisors (EAs) in MetaTrader 4/5. This framework uses only **2 open-source components** that work together to create a robust trading system:

### 🔧 Core Components

1. **TradingController** - EA lifecycle management controller
2. **MainPipeline** - Abstract pipeline class for implementing main business logic

These components provide a clean separation of concerns, making your EA code more maintainable, testable, and scalable.

## Component Overview

### TradingController Component

The `TradingController` is the central orchestrator for EA lifecycle management. It handles:

- **Initialization**: Manages EA startup and configuration
- **Tick Processing**: Coordinates trading logic execution on each tick
- **Cleanup**: Handles proper resource cleanup on EA termination
- **Error Handling**: Provides unified error management and reporting

### MainPipeline Component

The `MainPipeline` is an abstract base class that extends `TradingPipeline`. It provides:

- **Pipeline Architecture**: Structured approach to implementing trading logic
- **Automatic Registration**: Self-registers with the TradingPipelineDriver
- **Message Handling**: Built-in error and message handling capabilities
- **Object Management**: Integrated object registry for dependency management

## Prerequisites

Before using these components, ensure you have:

- MetaTrader 4 or MetaTrader 5 platform
- Access to the EA_Wizard framework files
- Basic understanding of MQL4/MQL5 programming

## Installation & Setup

1. **Include the Framework**: Add the EA_Wizard framework to your project
2. **Reference Components**: Include the required header files in your EA
3. **Initialize Controller**: Create and configure the TradingController instance

## Usage Examples

### Basic TradingController Setup

```mql4
#property strict

#include "EA_Wizard/TradingController.mqh"

// Global controller instance
TradingController* controller = NULL;

int OnInit()
{
    // Get the TradingPipelineDriver instance
    TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

    // Create the controller with the driver
    controller = new TradingController(driver, "MyEA_Controller");

    // Execute initialization pipeline
    ENUM_INIT_RETCODE result = controller.OnInit();
    return result;
}

void OnTick()
{
    if(controller != NULL)
    {
        controller.OnTick();
    }
}

void OnDeinit(const int reason)
{
    if(controller != NULL)
    {
        controller.OnDeinit(reason);
        delete controller;
        controller = NULL;
    }
}
```

### Creating a Custom MainPipeline

```mql4
#property strict

#include "EA_Wizard/MainPipeline.mqh"

// Custom pipeline for signal processing
class SignalProcessingPipeline : public MainPipeline
{
public:
    SignalProcessingPipeline()
        : MainPipeline(TICK_MAIN, "SignalProcessing", "SignalProcessor")
    {
        // Constructor automatically registers the pipeline
    }

protected:
    // Implement the main logic
    virtual void Main() override
    {
        // Your signal processing logic here
        if(ProcessMarketSignals())
        {
            SetResult(true, "Signal processing completed successfully");
        }
        else
        {
            SetResult(false, "Signal processing failed");
        }
    }

private:
    bool ProcessMarketSignals()
    {
        // Example signal processing logic
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double previousPrice = iClose(_Symbol, PERIOD_M1, 1);

        // Simple trend detection
        if(currentPrice > previousPrice)
        {
            Print("Upward trend detected");
            return true;
        }
        else if(currentPrice < previousPrice)
        {
            Print("Downward trend detected");
            return true;
        }

        Print("No clear trend");
        return true;
    }
};
```

### Configuration Pipeline Example

```mql4
#property strict

#include "EA_Wizard/MainPipeline.mqh"

// Input parameters
input int MagicNumber = 12345;
input double Lots = 0.1;
input int StopLoss = 50;
input int TakeProfit = 100;

// Configuration validation pipeline
class ConfigValidationPipeline : public MainPipeline
{
public:
    ConfigValidationPipeline()
        : MainPipeline(INIT_PARAMETERS, "ConfigValidation", "ConfigValidator")
    {
    }

protected:
    virtual void Main() override
    {
        // Register configuration parameters
        if(!Register((long)MagicNumber, "MagicNumber", "EA Magic Number"))
        {
            SetResult(false, "Failed to register MagicNumber");
            return;
        }

        if(!Register((double)Lots, "Lots", "Trading Lot Size"))
        {
            SetResult(false, "Failed to register Lots");
            return;
        }

        if(!Register((double)StopLoss, "StopLoss", "Stop Loss Points"))
        {
            SetResult(false, "Failed to register StopLoss");
            return;
        }

        if(!Register((double)TakeProfit, "TakeProfit", "Take Profit Points"))
        {
            SetResult(false, "Failed to register TakeProfit");
            return;
        }

        // Validate parameters
        if(!ValidateParameters())
        {
            SetResult(false, "Parameter validation failed");
            return;
        }

        SetResult(true, "Configuration validation completed successfully");
    }

private:
    bool ValidateParameters()
    {
        if(MagicNumber <= 0)
        {
            Print("Error: MagicNumber must be positive");
            return false;
        }

        if(Lots <= 0 || Lots > 10)
        {
            Print("Error: Lots must be between 0 and 10");
            return false;
        }

        if(StopLoss < 0 || TakeProfit < 0)
        {
            Print("Error: StopLoss and TakeProfit must be non-negative");
            return false;
        }

        return true;
    }
};
```

### Object Registration Example

```mql4
#property strict

#include "EA_Wizard/MainPipeline.mqh"

// Custom trading class
class TradingSignalProcessor
{
private:
    string m_symbol;
    int m_timeframe;

public:
    TradingSignalProcessor(string symbol, int timeframe)
        : m_symbol(symbol), m_timeframe(timeframe) {}

    bool ProcessSignal()
    {
        // Signal processing logic
        return true;
    }

    string GetSymbol() { return m_symbol; }
    int GetTimeframe() { return m_timeframe; }
};

// Pipeline demonstrating object registration
class ObjectRegistrationPipeline : public MainPipeline
{
public:
    ObjectRegistrationPipeline()
        : MainPipeline(INIT_VARIABLES, "ObjectRegistration", "ObjectManager")
    {
    }

protected:
    virtual void Main() override
    {
        // Create external component
        TradingSignalProcessor* processor = new TradingSignalProcessor(_Symbol, PERIOD_H1);

        // Register object with detailed description
        // Parameters: (void* object, string name, string description)
        // Returns: bool - true if registration successful, false otherwise
        if(!Register(processor, "SignalProcessor", "Main signal processing component for H1 timeframe"))
        {
            SetResult(false, "Failed to register SignalProcessor object");
            delete processor; // Clean up on failure
            return;
        }

        // Register additional objects
        TradingSignalProcessor* backupProcessor = new TradingSignalProcessor(_Symbol, PERIOD_M15);
        if(!Register(backupProcessor, "BackupProcessor", "Backup signal processor for M15 timeframe"))
        {
            SetResult(false, "Failed to register BackupProcessor object");
            delete backupProcessor;
            return;
        }

        // Verify registration
        if(HasObject("SignalProcessor") && HasObject("BackupProcessor"))
        {
            SetResult(true, "All objects registered successfully");
        }
        else
        {
            SetResult(false, "Object registration verification failed");
        }
    }
};
```

### Complete Working Example

```mql4
#property strict

#include "EA_Wizard/TradingController.mqh"
#include "EA_Wizard/MainPipeline.mqh"

// Input parameters
input int MagicNumber = 12345;
input double Lots = 0.1;

// Custom pipelines
class InitializationPipeline : public MainPipeline
{
public:
    InitializationPipeline() : MainPipeline(INIT_START, "Initialization") {}

protected:
    virtual void Main() override
    {
        Print("EA Initialization started");

        // Check trading conditions
        if(!IsTradeAllowed())
        {
            SetResult(false, "Trading is not allowed");
            return;
        }

        if(!IsConnected())
        {
            SetResult(false, "Not connected to trade server");
            return;
        }

        SetResult(true, "EA initialized successfully");
    }
};

class TradingPipeline : public MainPipeline
{
public:
    TradingPipeline() : MainPipeline(TICK_MAIN, "Trading") {}

protected:
    virtual void Main() override
    {
        // Simple trading logic example
        if(ShouldOpenTrade())
        {
            if(OpenTrade())
            {
                SetResult(true, "Trade opened successfully");
            }
            else
            {
                SetResult(false, "Failed to open trade");
            }
        }
        else
        {
            SetResult(true, "No trading signal");
        }
    }

private:
    bool ShouldOpenTrade()
    {
        // Simple example: trade on new bar
        static datetime lastBarTime = 0;
        datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);

        if(currentBarTime != lastBarTime)
        {
            lastBarTime = currentBarTime;
            return true;
        }

        return false;
    }

    bool OpenTrade()
    {
        // Example trade opening logic
        double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        int ticket = OrderSend(_Symbol, OP_BUY, Lots, price, 3, 0, 0,
                              "EA_Wizard", MagicNumber, 0, clrGreen);

        return ticket > 0;
    }
};

// Global instances
TradingController* controller = NULL;
InitializationPipeline* initPipeline = NULL;
TradingPipeline* tradingPipeline = NULL;

int OnInit()
{
    // Create pipeline instances
    initPipeline = new InitializationPipeline();
    tradingPipeline = new TradingPipeline();

    // Create controller
    TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
    controller = new TradingController(driver, "CompleteExample");

    // Execute initialization
    return controller.OnInit();
}

void OnTick()
{
    if(controller != NULL)
    {
        controller.OnTick();
    }
}

void OnDeinit(const int reason)
{
    if(controller != NULL)
    {
        controller.OnDeinit(reason);
        delete controller;
        controller = NULL;
    }
}
```

## Integration Guidelines

### Best Practices for External Component Integration

When integrating external components and basic data types with the EA_Wizard framework, follow these guidelines to maintain clean architecture and avoid tight coupling:

#### ⚠️ Avoid Tight Coupling

**DO NOT** make external components or basic data types tightly coupled with MainPipeline components by making them internal members:

```mql4
// ❌ BAD PRACTICE - Tight Coupling
class BadTradingPipeline : public MainPipeline
{
private:
    // Avoid making external components as class members
    OrderManager* m_orderManager;        // Tight coupling
    TradingSignalProcessor* m_processor; // Tight coupling
    double m_lotSize;                   // Basic data as member
    int m_magicNumber;                  // Basic data as member

public:
    BadTradingPipeline() : MainPipeline(TICK_MAIN, "BadExample")
    {
        // Creating dependencies in constructor creates tight coupling
        m_orderManager = new OrderManager(_Symbol);
        m_processor = new TradingSignalProcessor(_Symbol, PERIOD_H1);
        m_lotSize = 0.1;
        m_magicNumber = 12345;
    }

    virtual void Main() override
    {
        // Direct access to member variables
        if(m_processor.ProcessSignal())
        {
            m_orderManager.OpenOrder(OP_BUY, m_lotSize, m_magicNumber);
        }
    }
};
```

#### ✅ Prefer Loose Coupling

**DO** integrate external components and basic data types using loose coupling approaches:

##### Method 1: Integration within Main() Method

```mql4
// ✅ GOOD PRACTICE - Loose Coupling via Main() Method
class GoodTradingPipeline : public MainPipeline
{
public:
    GoodTradingPipeline() : MainPipeline(TICK_MAIN, "GoodExample") {}

protected:
    virtual void Main() override
    {
        // Retrieve external components from registry (loose coupling)
        ObjectDetail* processorDetail = GetObjectDetail("SignalProcessor");
        if(processorDetail.GetObject() == NULL)
        {
            SetResult(false, "SignalProcessor not found in registry");
            return;
        }

        TradingSignalProcessor* processor = (TradingSignalProcessor*)processorDetail.GetObject();

        // Retrieve basic data types from registry
        double lotSize = GetRegisteredDouble("Lots");
        long magicNumber = GetRegisteredLong("MagicNumber");

        // Use retrieved components and data
        if(processor.ProcessSignal())
        {
            ObjectDetail* orderManagerDetail = GetObjectDetail("OrderManager");
            if(orderManagerDetail.GetObject() != NULL)
            {
                OrderManager* orderManager = (OrderManager*)orderManagerDetail.GetObject();
                orderManager.OpenOrder(OP_BUY, lotSize, (int)magicNumber);
            }
        }

        SetResult(true, "Trading logic executed successfully");
    }
};
```

##### Method 2: Using MainPipeline Registry Methods

```mql4
// ✅ GOOD PRACTICE - Using Built-in Registry Access
class RegistryBasedPipeline : public MainPipeline
{
public:
    RegistryBasedPipeline() : MainPipeline(TICK_MAIN, "RegistryBased") {}

protected:
    virtual void Main() override
    {
        // Check if required data is registered
        if(!HasRegisteredDouble("Lots") || !HasRegisteredLong("MagicNumber"))
        {
            SetResult(false, "Required trading parameters not registered");
            return;
        }

        // Retrieve values using type-safe methods
        double lots = GetRegisteredDouble("Lots");
        long magic = GetRegisteredLong("MagicNumber");
        string symbol = GetRegisteredString("TradingSymbol");

        // Get detailed information with metadata
        RegisteredDetail<double>* lotDetail = GetDoubleDetail("Lots");
        if(lotDetail.IsValid())
        {
            Print("Lot size: ", lotDetail.GetValue(),
                  " (registered at: ", TimeToString(lotDetail.GetRegistrationTime()), ")");
            delete lotDetail; // Clean up memory
        }

        // Use the retrieved data for trading logic
        ProcessTradingLogic(lots, (int)magic, symbol);

        SetResult(true, "Registry-based trading completed");
    }

private:
    void ProcessTradingLogic(double lots, int magic, string symbol)
    {
        // Your trading logic here using the retrieved parameters
        Print("Processing trade: ", lots, " lots, magic: ", magic, ", symbol: ", symbol);
    }
};
```

#### Key Benefits of Loose Coupling

1. **Flexibility**: Components can be easily replaced or modified without changing pipeline code
2. **Testability**: Easier to unit test pipelines by registering mock objects
3. **Reusability**: Pipelines can work with different component implementations
4. **Maintainability**: Changes to external components don't require pipeline modifications
5. **Dependency Injection**: Components are injected through the registry system

#### Integration Workflow

1. **Registration Phase** (typically in INIT stages):

   - Create external components
   - Register components using `Register(object, name, description)`
   - Register basic data types using `Register(value, name, description)`

2. **Usage Phase** (typically in TICK stages):

   - Retrieve components using `GetObjectDetail(name)`
   - Retrieve values using `GetRegisteredLong/Double/String(name)`
   - Access metadata using `GetLongDetail/DoubleDetail/StringDetail(name)`

3. **Cleanup Phase** (typically in DEINIT stages):
   - Components are automatically managed by the registry
   - Manual cleanup only needed for complex scenarios

## API Reference

### TradingController Public API

The TradingController class provides a unified interface for managing EA lifecycle through pipeline execution. It serves as the central orchestrator for initialization, tick processing, and cleanup operations.

#### Constants

```mql4
#define TRADING_CONTROLLER_NAME "TradingController"
#define TRADING_CONTROLLER_TYPE "Controller"
#define TRADING_CONTROLLER_VERSION "1.0.0"
```

#### Constructor

```mql4
TradingController(TradingPipelineDriver* driver, string name = TRADING_CONTROLLER_NAME)
```

- **Parameters:**
  - `driver`: TradingPipelineDriver instance (required, cannot be NULL)
  - `name`: Controller name (optional, default: "TradingController")
- **Description:** Creates a new TradingController instance with the specified driver and name
- **Behavior:** Automatically validates the driver parameter and creates initial result object
- **Error Handling:** Sets error result if driver is NULL

#### Destructor

```mql4
virtual ~TradingController()
```

- **Description:** Cleans up resources and result objects (does not delete the driver)
- **Memory Management:** Automatically deletes m_last_result, sets driver pointer to NULL
- **Thread Safety:** Safe to call multiple times

#### EA Lifecycle Methods

```mql4
ENUM_INIT_RETCODE OnInit()
```

- **Returns:** ENUM_INIT_RETCODE - Standard MT4/MT5 initialization result code
- **Description:** Executes initialization pipeline for EA startup
- **Behavior:** Calls ExecuteInitPipeline() and sets m_isInitialized flag
- **Error Conditions:** Returns INIT_FAILED if driver is NULL or pipeline execution fails
- **Side Effects:** Updates m_last_result with execution status

```mql4
void OnTick()
```

- **Description:** Executes trading pipeline on each tick
- **Preconditions:** Controller must be initialized (IsInitialized() == true)
- **Behavior:** Calls ExecuteTickPipeline() with automatic pipeline reset
- **Error Handling:** Skips execution if not initialized or driver unavailable
- **Performance:** Includes built-in state validation to prevent unnecessary execution

```mql4
void OnDeinit(int reason)
```

- **Parameters:**
  - `reason`: Standard MT4/MT5 deinitialization reason code
- **Description:** Executes cleanup pipeline for EA termination
- **Behavior:** Calls ExecuteDeinitPipeline(reason) and resets initialization state
- **Side Effects:** Sets m_isInitialized to false, updates m_last_result

#### Status and Information Methods

```mql4
bool IsInitialized() const
```

- **Returns:** bool - True if controller is initialized and ready for tick processing
- **Description:** Checks if the controller has been successfully initialized
- **Thread Safety:** Read-only operation, safe for concurrent access

```mql4
string GetName() const
```

- **Returns:** string - Controller name as specified in constructor
- **Description:** Returns the unique identifier name of the controller
- **Usage:** Useful for logging, debugging, and multi-controller scenarios

```mql4
string GetType() const
```

- **Returns:** string - Always returns "Controller"
- **Description:** Returns the type identifier of the controller
- **Usage:** Used for type checking and polymorphic operations

```mql4
PipelineResult* GetResult() const
```

- **Returns:** PipelineResult\* - Last execution result (never NULL)
- **Description:** Returns the result of the last operation performed
- **Memory Management:** Caller should not delete the returned pointer
- **Usage:** Check IsSuccess() and GetMessage() for operation status

```mql4
TradingPipelineDriver* GetDriver() const
```

- **Returns:** TradingPipelineDriver\* - Associated driver instance
- **Description:** Returns the TradingPipelineDriver instance used by this controller
- **Memory Management:** Caller should not delete the returned pointer
- **Usage:** Access driver components like registry, manager, etc.

### MainPipeline Public API

The MainPipeline class is an abstract base class that extends TradingPipeline, providing automatic registration with the TradingPipelineDriver and enhanced functionality for main business logic implementation.

#### Class Hierarchy

```
ITradingPipeline (interface)
    ↓
TradingPipeline (abstract base class)
    ↓
MainPipeline (abstract class)
```

#### Constructor

```mql4
MainPipeline(ENUM_TRADING_STAGE stage = INIT_START,
             string name = "",
             string type = "MainPipeline",
             ITradingPipelineDriver* driver = NULL)
```

- **Parameters:**
  - `stage`: Trading stage for this pipeline (default: INIT_START)
  - `name`: Pipeline name (optional, empty string uses auto-generated name)
  - `type`: Pipeline type (default: "MainPipeline")
  - `driver`: TradingPipelineDriver instance (optional, auto-injected if NULL)
- **Description:** Creates a MainPipeline instance and automatically registers it with the driver
- **Automatic Registration:** Calls TradingPipelineRegistry.Register() and handles error messages
- **Driver Injection:** Uses TradingPipelineDriver::GetInstance() if driver is NULL

#### Abstract Method (Must be Implemented)

```mql4
virtual void Main() = 0
```

- **Description:** Pure virtual method that must be implemented by derived classes to define the main pipeline logic
- **Usage:** Called by Execute() method from TradingPipeline base class
- **Best Practice:** Use SetResult() to indicate success/failure status

#### Protected Helper Methods

##### Message and Error Handling

```mql4
TradingMessageHandler* GetMessageHandler()
```

- **Returns:** TradingMessageHandler\* - Message handler instance (may be NULL)
- **Description:** Returns the error/message handler from the driver
- **Usage:** Use for logging errors, warnings, and information messages
- **Error Handling:** Returns NULL if driver is not available

##### Object Registry Management

```mql4
ObjectDetail* GetObjectDetail(string name)
```

- **Parameters:**
  - `name`: Object name to retrieve
- **Returns:** ObjectDetail\* - Object detail information (never NULL)
- **Description:** Retrieves detailed information about a registered object
- **Error Handling:** Returns empty ObjectDetail if driver/registry unavailable

```mql4
bool Register(void* object, string name, string description)
```

- **Parameters:**
  - `object`: Pointer to object to register
  - `name`: Object name (must be unique)
  - `description`: Object description for documentation
- **Returns:** bool - True if registration successful
- **Description:** Registers an object with the ObjectRegistry
- **Thread Safety:** Safe for concurrent access through driver

```mql4
bool Unregister(string name)
```

- **Parameters:**
  - `name`: Object name to remove
- **Returns:** bool - True if removal successful
- **Description:** Removes an object from the ObjectRegistry

```mql4
bool HasObject(string name)
```

- **Parameters:**
  - `name`: Object name to check
- **Returns:** bool - True if object exists in registry
- **Description:** Checks if an object is registered in the ObjectRegistry

##### Value Registration Methods

```mql4
bool Register(long value, string name)
bool Register(double value, string name)
bool Register(string value, string name)
```

- **Parameters:**
  - `value`: Value to register (long, double, or string)
  - `name`: Unique name for the value
- **Returns:** bool - True if registration successful
- **Description:** Registers typed values with the appropriate registry (Long/Double/String)

```mql4
bool Register(long value, string name, string description)
bool Register(double value, string name, string description)
bool Register(string value, string name, string description)
```

- **Parameters:**
  - `value`: Value to register (long, double, or string)
  - `name`: Unique name for the value
  - `description`: Descriptive text for documentation and debugging
- **Returns:** bool - True if registration successful
- **Description:** Registers typed values with detailed descriptions for enhanced metadata

```mql4
bool UnregisterLong(string name)
bool UnregisterDouble(string name)
bool UnregisterString(string name)
```

- **Parameters:**
  - `name`: Name of value to remove
- **Returns:** bool - True if removal successful
- **Description:** Removes typed values from the appropriate registry

```mql4
bool HasRegisteredLong(string name)
bool HasRegisteredDouble(string name)
bool HasRegisteredString(string name)
```

- **Parameters:**
  - `name`: Name to check
- **Returns:** bool - True if value exists
- **Description:** Checks if a typed value is registered

```mql4
int GetAllRegistered(string &names[], long &values[])
int GetAllRegistered(string &names[], double &values[])
int GetAllRegistered(string &names[], string &values[])
```

- **Parameters:**
  - `names[]`: Output array for names (resized automatically)
  - `values[]`: Output array for values (resized automatically)
- **Returns:** int - Number of registered values
- **Description:** Retrieves all registered values of the specified type

##### Value Access Methods

```mql4
long GetRegisteredLong(string name)
double GetRegisteredDouble(string name)
string GetRegisteredString(string name)
```

- **Parameters:**
  - `name`: Name of the registered value to retrieve
- **Returns:** Registered value of the specified type, or default value (0, 0.0, "") if not found
- **Description:** Type-safe methods for retrieving registered values
- **Usage:** Provides simple access to stored values without metadata

##### Detailed Information Access Methods

```mql4
RegisteredDetail<long>* GetLongDetail(string name)
RegisteredDetail<double>* GetDoubleDetail(string name)
RegisteredDetail<string>* GetStringDetail(string name)
```

- **Parameters:**
  - `name`: Name of the registered value to retrieve detailed information for
- **Returns:** RegisteredDetail\* - Detailed information object containing value and metadata
- **Description:** Retrieves comprehensive information including registration time, access count, and description
- **Memory Management:** **Important** - Caller must delete the returned pointer using `delete`
- **Usage:** Use when you need metadata in addition to the value itself

**RegisteredDetail Object Methods:**

- `GetValue()` - Returns the stored value
- `GetDescription()` - Returns the description provided during registration
- `GetRegistrationTime()` - Returns when the value was registered
- `GetAccessCount()` - Returns how many times the value has been accessed
- `GetSource()` - Returns the source identifier
- `IsValid()` - Returns true if the detail object contains valid data

**Example Usage:**

```mql4
// Simple value access
double lotSize = GetRegisteredDouble("Lots");

// Detailed access with metadata
RegisteredDetail<double>* lotDetail = GetDoubleDetail("Lots");
if(lotDetail.IsValid())
{
    double value = lotDetail.GetValue();
    string desc = lotDetail.GetDescription();
    datetime regTime = lotDetail.GetRegistrationTime();
    int accessCount = lotDetail.GetAccessCount();

    Print("Lot Size: ", value, " (", desc, ") - Registered: ",
          TimeToString(regTime), ", Accessed: ", accessCount, " times");

    delete lotDetail; // Important: Clean up memory
}
```

#### Inherited Methods from TradingPipeline

##### Core Interface Methods (from ITradingPipeline)

```mql4
virtual void Execute()
```

- **Description:** Executes the pipeline (calls Main() method)
- **Behavior:** Checks execution state, calls Main(), sets executed flag
- **Thread Safety:** Not thread-safe, should be called from single thread

```mql4
string GetName()
```

- **Returns:** string - Pipeline name
- **Description:** Returns the name of the pipeline as specified in constructor

```mql4
string GetType()
```

- **Returns:** string - Pipeline type
- **Description:** Returns the type of the pipeline (e.g., "MainPipeline")

```mql4
bool IsExecuted()
```

- **Returns:** bool - True if pipeline has been executed
- **Description:** Checks if the pipeline has already been executed
- **Usage:** Prevents duplicate execution

```mql4
virtual void Restore()
```

- **Description:** Resets the pipeline execution state
- **Behavior:** Sets executed flag to false, updates result object
- **Usage:** Allows pipeline to be executed again

##### Extended Methods (from TradingPipeline)

```mql4
ENUM_TRADING_STAGE GetStage() const
```

- **Returns:** ENUM_TRADING_STAGE - Trading stage
- **Description:** Returns the trading stage this pipeline is associated with
- **Usage:** Used by TradingPipelineRegistry for stage-based organization

```mql4
ITradingPipelineDriver* GetDriver() const
```

- **Returns:** ITradingPipelineDriver\* - Driver instance
- **Description:** Returns the driver instance used by this pipeline
- **Memory Management:** Caller should not delete the returned pointer

```mql4
PipelineResult* GetResult() const
```

- **Returns:** PipelineResult\* - Execution result (never NULL)
- **Description:** Returns the result of the last pipeline execution
- **Memory Management:** Caller should not delete the returned pointer

##### Protected Result Management

```mql4
void SetResult(bool success, string message)
```

- **Parameters:**
  - `success`: Success status
  - `message`: Result message
- **Description:** Sets the execution result for the pipeline
- **Error Level:** Automatically sets ERROR_LEVEL_INFO for success, ERROR_LEVEL_ERROR for failure

#### Utility Macros

##### Object Registry Macros

```mql4
#define PipelineObjectIsRegistered(ObjectType, name)
#define GetPipelineObject(ObjectType, name)
```

- **Description:** Convenience macros for type-safe object retrieval from ObjectRegistry
- **Usage:** Simplify casting and null checking when retrieving registered objects

##### Value Registry Macros

```mql4
#define PipelineDataIsRegistered(type, key)
#define GetPipelineData(type, key)
```

- **Description:** Convenience macros for type-safe value retrieval from typed registries
- **Supported Types:** `string`, `double`, `long`
- **Usage:** Simplify access to registered values with automatic type handling

**Macro Examples:**

```mql4
// Check if data is registered
if(PipelineDataIsRegistered(double, "Lots"))
{
    // Get the value using type-safe macro
    double lotSize = GetPipelineData(double, "Lots");
    Print("Lot size: ", lotSize);
}

// Check for string data
if(PipelineDataIsRegistered(string, "TradingSymbol"))
{
    string symbol = GetPipelineData(string, "TradingSymbol");
    Print("Trading symbol: ", symbol);
}

// Check for long data
if(PipelineDataIsRegistered(long, "MagicNumber"))
{
    long magic = GetPipelineData(long, "MagicNumber");
    Print("Magic number: ", magic);
}
```

**Macro Expansion:**

- `PipelineDataIsRegistered(double, "key")` → `HasRegisteredDouble("key")`
- `GetPipelineData(double, "key")` → `GetRegisteredDouble("key")`
- `PipelineDataIsRegistered(string, "key")` → `HasRegisteredString("key")`
- `GetPipelineData(string, "key")` → `GetRegisteredString("key")`
- `PipelineDataIsRegistered(long, "key")` → `HasRegisteredLong("key")`
- `GetPipelineData(long, "key")` → `GetRegisteredLong("key")`

### PipelineResult Class API

The PipelineResult class encapsulates execution results and provides detailed information about pipeline operations.

#### Constructor

```mql4
PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
```

- **Parameters:**
  - `success`: Execution success status
  - `message`: Descriptive message
  - `source`: Source pipeline name
  - `errorLevel`: Error level (INFO, WARNING, ERROR, CRITICAL)
- **Description:** Creates a new result object with timestamp

#### Public Methods

```mql4
bool IsSuccess() const
```

- **Returns:** bool - True if operation was successful
- **Description:** Returns the success status of the operation

```mql4
string GetMessage() const
```

- **Returns:** string - Descriptive message
- **Description:** Returns the message describing the operation result

```mql4
string GetSource() const
```

- **Returns:** string - Source pipeline name
- **Description:** Returns the name of the pipeline that generated this result

```mql4
datetime GetTimestamp() const
```

- **Returns:** datetime - Execution timestamp
- **Description:** Returns when the result was created

```mql4
ENUM_ERROR_LEVEL GetErrorLevel() const
```

- **Returns:** ENUM_ERROR_LEVEL - Error level
- **Description:** Returns the severity level of the result

```mql4
string ToString() const
```

- **Returns:** string - Formatted result string
- **Description:** Returns a formatted string representation of the result

## Configuration Options

### TradingController Configuration

- **driver**: TradingPipelineDriver instance (required)
- **name**: Controller name (optional, default: "TradingController")

### MainPipeline Configuration

- **stage**: Trading stage (INIT_START, TICK_DATA_FEED, etc.)
- **name**: Pipeline name (optional)
- **type**: Pipeline type (optional, default: "MainPipeline")
- **driver**: TradingPipelineDriver instance (optional, auto-injected)

### ENUM_TRADING_STAGE Values

The framework defines specific trading stages that correspond to different phases of EA execution:

#### Initialization Stages (TRADING_INIT Event)

- **INIT_START**: Initial startup phase
- **INIT_PARAMETERS**: Parameter validation and setup
- **INIT_VARIABLES**: Variable initialization
- **INIT_ENVIRONMENT**: Environment and connection checks
- **INIT_INDICATORS**: Technical indicator initialization
- **INIT_COMPLETE**: Initialization completion phase

#### Trading Stages (TRADING_TICK Event)

- **TICK_DATA_FEED**: Market data processing and validation
- **TICK_SIGNAL_ANALYSIS**: Signal generation and analysis
- **TICK_ORDER_MANAGEMENT**: Order placement and management
- **TICK_RISK_CONTROL**: Risk management and position sizing
- **TICK_LOGGING**: Logging and reporting

#### Cleanup Stages (TRADING_DEINIT Event)

- **DEINIT_CLEANUP**: Resource cleanup and order management
- **DEINIT_SAVE_STATE**: State persistence and data saving
- **DEINIT_COMPLETE**: Final cleanup completion

## EA Lifecycle Stage Mapping

The EA_Wizard framework maps trading stages to specific EA lifecycle methods through the TradingController. Understanding this mapping is crucial for proper pipeline implementation:

### OnInit() Method Stages

When `controller.OnInit()` is called, it executes pipelines registered for the **TRADING_INIT** event in the following order:

| Stage              | Description                | Typical Use Cases                            |
| ------------------ | -------------------------- | -------------------------------------------- |
| `INIT_START`       | EA startup initialization  | Basic setup, welcome messages                |
| `INIT_PARAMETERS`  | Input parameter validation | Validate user inputs, register parameters    |
| `INIT_VARIABLES`   | Variable initialization    | Initialize global variables, arrays          |
| `INIT_ENVIRONMENT` | Environment checks         | Check trading permissions, server connection |
| `INIT_INDICATORS`  | Technical indicator setup  | Initialize custom indicators, buffers        |
| `INIT_COMPLETE`    | Finalization               | Confirm successful initialization            |

### OnTick() Method Stages

When `controller.OnTick()` is called, it executes pipelines registered for the **TRADING_TICK** event in the following order:

| Stage                   | Description            | Typical Use Cases                            |
| ----------------------- | ---------------------- | -------------------------------------------- |
| `TICK_DATA_FEED`        | Market data processing | Validate tick data, update price feeds       |
| `TICK_SIGNAL_ANALYSIS`  | Signal generation      | Technical analysis, signal detection         |
| `TICK_ORDER_MANAGEMENT` | Order operations       | Place/modify/close orders                    |
| `TICK_RISK_CONTROL`     | Risk management        | Position sizing, stop loss management        |
| `TICK_LOGGING`          | Data logging           | Record trading activity, performance metrics |

### OnDeinit() Method Stages

When `controller.OnDeinit(reason)` is called, it executes pipelines registered for the **TRADING_DEINIT** event in the following order:

| Stage               | Description       | Typical Use Cases                  |
| ------------------- | ----------------- | ---------------------------------- |
| `DEINIT_CLEANUP`    | Resource cleanup  | Close open orders, cleanup objects |
| `DEINIT_SAVE_STATE` | State persistence | Save configuration, trading state  |
| `DEINIT_COMPLETE`   | Final cleanup     | Final logging, goodbye messages    |

### Stage Registration Example

```mql4
// Register pipelines for different stages
class MyInitPipeline : public MainPipeline
{
public:
    MyInitPipeline() : MainPipeline(INIT_PARAMETERS, "ParameterCheck") {}
    // This pipeline will execute during OnInit()
};

class MyTradingPipeline : public MainPipeline
{
public:
    MyTradingPipeline() : MainPipeline(TICK_SIGNAL_ANALYSIS, "SignalAnalysis") {}
    // This pipeline will execute during OnTick()
};

class MyCleanupPipeline : public MainPipeline
{
public:
    MyCleanupPipeline() : MainPipeline(DEINIT_CLEANUP, "ResourceCleanup") {}
    // This pipeline will execute during OnDeinit()
};
```

### Execution Flow

1. **EA Startup**: `OnInit()` → Executes all INIT\_\* stages in sequence
2. **Each Tick**: `OnTick()` → Executes all TICK\_\* stages in sequence
3. **EA Shutdown**: `OnDeinit()` → Executes all DEINIT\_\* stages in sequence

This structured approach ensures that your EA logic is organized, predictable, and maintainable.

## Error Handling

Both components provide built-in error handling:

```mql4
// Check controller status
if(controller.IsInitialized())
{
    PipelineResult* result = controller.GetLastResult();
    if(result != NULL && !result.IsSuccess())
    {
        Print("Controller error: ", result.GetMessage());
    }
}

// Check pipeline execution result
MyPipeline* pipeline = new MyPipeline();
pipeline.Execute();
PipelineResult* result = pipeline.GetResult();
if(result != NULL)
{
    Print("Pipeline result: ", result.IsSuccess() ? "Success" : "Failed");
    Print("Message: ", result.GetMessage());
}
```

## Troubleshooting

### Common Issues

1. **Controller not initializing**

   - Ensure TradingPipelineDriver is properly initialized
   - Check that driver parameter is not NULL

2. **Pipeline not executing**

   - Verify pipeline is properly registered
   - Check trading stage configuration

3. **Memory issues**
   - Always delete created objects in OnDeinit()
   - Use proper pointer management

### Debug Tips

- Enable detailed logging in your pipelines
- Use the built-in message handling system
- Check PipelineResult objects for execution status

## Best Practices

1. **Always check for NULL pointers** before using objects
2. **Implement proper cleanup** in OnDeinit()
3. **Use meaningful names** for pipelines and controllers
4. **Handle errors gracefully** using the result system
5. **Test thoroughly** in demo environment before live trading

## Support

For additional support and documentation, refer to the EA_Wizard framework documentation and community resources.

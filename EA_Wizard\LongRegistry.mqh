//+------------------------------------------------------------------+
//|                                                  LongRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"

//+------------------------------------------------------------------+
//| Long 類型註冊器                                                 |
//| 專門用於管理 long 類型數據的註冊器                              |
//+------------------------------------------------------------------+
class LongRegistry : public BaseRegistry<string, long>
{
public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    LongRegistry(string name = "LongRegistry",
                string type = "LongRegistry",
                int maxRegistrations = 50,
                bool owned = true)
        : BaseRegistry<string, long>(name, type, maxRegistrations, owned)
    {
        UpdateResult(true, "Long 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~LongRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 重寫基類方法以提供更詳細的消息                                   |
    //+------------------------------------------------------------------+

    // 重寫註冊方法以提供更詳細的 long 類型消息
    virtual bool Register(string key, long value)
    {
        bool result = BaseRegistry<string, long>::Register(key, value);
        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 long 值: 鍵='%s', 值=%d", key, value), ERROR_LEVEL_INFO);
        }
        return result;
    }

    // 註冊 long 值（帶描述）
    virtual bool Register(string key, long value, string description)
    {
        // 調用基類方法（會自動創建 RegisteredDetail 對象，帶描述）
        bool result = BaseRegistry<string, long>::Register(key, value, description);

        if(result)
        {
            UpdateResult(true, StringFormat("成功註冊 long 值（帶描述）: 鍵='%s', 值=%d, 描述='%s'", key, value, description), ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 重寫獲取方法以提供更詳細的 long 類型消息
    virtual long GetRegisteredValue(string key, long defaultValue = 0)
    {
        long value = BaseRegistry<string, long>::GetRegisteredValue(key, defaultValue);
        if(IsRegistered(key))
        {
            UpdateResult(true, StringFormat("成功獲取 long 值: 鍵='%s', 值=%d", key, value), ERROR_LEVEL_INFO);
        }
        return value;
    }

    // 重寫更新方法以提供更詳細的 long 類型消息
    virtual bool UpdateRegisteredValue(string key, long newValue)
    {
        bool result = BaseRegistry<string, long>::UpdateRegisteredValue(key, newValue);
        if(result)
        {
            UpdateResult(true, StringFormat("成功更新 long 值: 鍵='%s', 新值=%d", key, newValue), ERROR_LEVEL_INFO);
        }
        return result;
    }

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+



    // 獲取指定範圍內的值
    int GetValuesInRange(long minValue, long maxValue, string &keys[], long &values[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();

        ArrayResize(keys, totalCount);
        ArrayResize(values, totalCount);

        foreachm(string, key, RegisteredDetail<long>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                long value = detail.GetValue();
                if(value >= minValue && value <= maxValue)
                {
                    keys[count] = key;
                    values[count] = value;
                    count++;
                }
            }
        }

        // 調整數組大小到實際數量
        ArrayResize(keys, count);
        ArrayResize(values, count);

        UpdateResult(true, StringFormat("找到 %d 個在範圍 [%d, %d] 內的值", count, minValue, maxValue), ERROR_LEVEL_INFO);
        return count;
    }

    // 計算所有註冊值的總和
    long CalculateSum()
    {
        long sum = 0;

        foreachm(string, key, RegisteredDetail<long>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                sum += detail.GetValue();
            }
        }

        UpdateResult(true, StringFormat("計算總和完成: %d", sum), ERROR_LEVEL_INFO);
        return sum;
    }

    // 查找最大值
    long FindMaxValue(string &maxKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最大值", ERROR_LEVEL_WARNING);
            maxKey = "";
            return 0;
        }

        long maxValue = LONG_MIN;
        maxKey = "";

        foreachm(string, key, RegisteredDetail<long>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                long value = detail.GetValue();
                if(value > maxValue)
                {
                    maxValue = value;
                    maxKey = key;
                }
            }
        }

        UpdateResult(true, StringFormat("找到最大值: 鍵='%s', 值=%d", maxKey, maxValue), ERROR_LEVEL_INFO);
        return maxValue;
    }

    // 查找最小值
    long FindMinValue(string &minKey)
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法查找最小值", ERROR_LEVEL_WARNING);
            minKey = "";
            return 0;
        }

        long minValue = LONG_MAX;
        minKey = "";

        foreachm(string, key, RegisteredDetail<long>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                long value = detail.GetValue();
                if(value < minValue)
                {
                    minValue = value;
                    minKey = key;
                }
            }
        }

        UpdateResult(true, StringFormat("找到最小值: 鍵='%s', 值=%d", minKey, minValue), ERROR_LEVEL_INFO);
        return minValue;
    }

    //+------------------------------------------------------------------+
    //| 獲取統計信息                                                     |
    //+------------------------------------------------------------------+
    virtual string GetStatistics()
    {
        string stats = StringFormat(
            "=== Long 註冊器統計信息 ===\n"
            "名稱: %s\n"
            "類型: %s\n"
            "已註冊數量: %d\n"
            "最大註冊數: %d\n"
            "使用率: %.1f%%\n"
            "狀態: %s\n"
            "擁有項目: %s",
            GetName(),
            GetType(),
            GetRegisteredCount(),
            GetMaxRegistrations(),
            (GetMaxRegistrations() > 0) ? (GetRegisteredCount() * 100.0 / GetMaxRegistrations()) : 0.0,
            IsEnabled() ? "啟用" : "禁用",
            IsOwned() ? "是" : "否"
        );

        if(!IsEmpty())
        {
            long sum = CalculateSum();
            string maxKey, minKey;
            long maxValue = FindMaxValue(maxKey);
            long minValue = FindMinValue(minKey);

            stats += StringFormat(
                "\n=== 數值統計 ===\n"
                "總和: %d\n"
                "最大值: %d (鍵: %s)\n"
                "最小值: %d (鍵: %s)\n"
                "平均值: %.2f",
                sum,
                maxValue, maxKey,
                minValue, minKey,
                (GetRegisteredCount() > 0) ? (sum * 1.0 / GetRegisteredCount()) : 0.0
            );
        }

        return stats;
    }
};

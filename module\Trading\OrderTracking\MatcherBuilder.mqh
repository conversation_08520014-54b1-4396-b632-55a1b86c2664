//+------------------------------------------------------------------+
//|                                               MatcherBuilder.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MATCHER_BUILDER_MQH
#define MATCHER_BUILDER_MQH

#include "OrderMatcher.mqh"

//+------------------------------------------------------------------+
//| MatcherBuilder Class                                             |
//| Builder pattern implementation for constructing OrderMatcher    |
//| objects with fluent interface and method chaining               |
//|                                                                  |
//| Usage Example:                                                   |
//|   OrderMatcher* matcher = MatcherBuilder()                      |
//|       .withSymbolMatcher("EURUSD")                              |
//|       .withMagicNumberMatcher(12345)                            |
//|       .withBuyOrderMatcher()                                     |
//|       .build();                                                  |
//|                                                                  |
//| Features:                                                        |
//| - Fluent interface with method chaining                         |
//| - Immutable OrderMatcher configuration                          |
//| - Support for all existing OrderMatcher types                   |
//| - Static factory methods for common patterns                    |
//| - Automatic AND logic combination of criteria                   |
//+------------------------------------------------------------------+
class MatcherBuilder
{
private:
    AndOrderMatcher*  m_andMatcher;
    bool              m_hasMatchers;

public:
                      MatcherBuilder()
    {
        m_andMatcher = new AndOrderMatcher();
        m_hasMatchers = false;
    }

    virtual          ~MatcherBuilder()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            delete m_andMatcher;
        }
    }

    //+------------------------------------------------------------------+
    //| Basic Matcher Methods                                            |
    //+------------------------------------------------------------------+

    MatcherBuilder*   withTicketMatcher(int ticket)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            TicketOrderMatcher* matcher = new TicketOrderMatcher(ticket);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withSymbolMatcher(string symbol)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            SymbolOrderMatcher* matcher = new SymbolOrderMatcher(symbol);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withMagicNumberMatcher(int magicNumber)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            MagicNumberOrderMatcher* matcher = new MagicNumberOrderMatcher(magicNumber);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withCommentMatcher(string comment, bool exactMatch = false)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            CommentOrderMatcher* matcher = new CommentOrderMatcher(comment, exactMatch);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withTypeMatcher(int orderType)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            OrderTypeMatcher* matcher = new OrderTypeMatcher(orderType);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    //+------------------------------------------------------------------+
    //| Advanced Matcher Methods                                         |
    //+------------------------------------------------------------------+

    MatcherBuilder*   withProfitRangeMatcher(double minProfit, double maxProfit)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            ProfitRangeOrderMatcher* matcher = new ProfitRangeOrderMatcher(minProfit, maxProfit);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withLotSizeRangeMatcher(double minLots, double maxLots)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            LotSizeRangeOrderMatcher* matcher = new LotSizeRangeOrderMatcher(minLots, maxLots);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withTimeRangeMatcher(datetime startTime, datetime endTime)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            TimeRangeOrderMatcher* matcher = new TimeRangeOrderMatcher(startTime, endTime);
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    //+------------------------------------------------------------------+
    //| Predefined Matcher Methods                                       |
    //+------------------------------------------------------------------+

    MatcherBuilder*   withPendingOrderMatcher()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            PendingOrderMatcher* matcher = new PendingOrderMatcher();
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withMarketOrderMatcher()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            MarketOrderMatcher* matcher = new MarketOrderMatcher();
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withBuyOrderMatcher()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            BuyOrderMatcher* matcher = new BuyOrderMatcher();
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    MatcherBuilder*   withSellOrderMatcher()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            SellOrderMatcher* matcher = new SellOrderMatcher();
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    //+------------------------------------------------------------------+
    //| Custom Matcher Method                                            |
    //+------------------------------------------------------------------+

    MatcherBuilder*   withCustomMatcher(OrderMatcher* matcher)
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID && CheckPointer(matcher) != POINTER_INVALID)
        {
            m_andMatcher.addMatcher(matcher);
            m_hasMatchers = true;
        }
        return GetPointer(this);
    }

    //+------------------------------------------------------------------+
    //| Reset Method                                                     |
    //+------------------------------------------------------------------+
    MatcherBuilder*    reset()
    {
        if (CheckPointer(m_andMatcher) != POINTER_INVALID)
        {
            delete m_andMatcher;
        }
        m_andMatcher = new AndOrderMatcher();
        m_hasMatchers = false;
        return GetPointer(this);
    }

    //+------------------------------------------------------------------+
    //| Build Method                                                     |
    //+------------------------------------------------------------------+

    OrderMatcher*     build()
    {
        if (!m_hasMatchers)
        {
            // Return a base matcher that matches everything if no criteria specified
            return new BaseOrderMatcher();
        }

        // Transfer ownership of the matcher to the caller
        OrderMatcher* result = m_andMatcher;
        m_andMatcher = NULL;  // Prevent deletion in destructor
        return result;
    }

    //+------------------------------------------------------------------+
    //| Static Factory Methods for Common Patterns                      |
    //+------------------------------------------------------------------+

    static OrderMatcher* createSymbolAndMagicMatcher(string symbol, int magicNumber)
    {
        MatcherBuilder builder;
        return builder.withSymbolMatcher(symbol)
                     .withMagicNumberMatcher(magicNumber)
                     .build();
    }

    static OrderMatcher* createProfitableBuyOrdersMatcher(string symbol)
    {
        MatcherBuilder builder;
        return builder.withSymbolMatcher(symbol)
                     .withBuyOrderMatcher()
                     .withProfitRangeMatcher(0.01, 999999.0)
                     .build();
    }

    static OrderMatcher* createPendingOrdersForSymbolMatcher(string symbol)
    {
        MatcherBuilder builder;
        return builder.withSymbolMatcher(symbol)
                     .withPendingOrderMatcher()
                     .build();
    }

    static OrderMatcher* createMarketOrdersWithMagicMatcher(int magicNumber)
    {
        MatcherBuilder builder;
        return builder.withMagicNumberMatcher(magicNumber)
                     .withMarketOrderMatcher()
                     .build();
    }

    static OrderMatcher* createLossingOrdersMatcher(string symbol)
    {
        MatcherBuilder builder;
        return builder.withSymbolMatcher(symbol)
                     .withProfitRangeMatcher(-999999.0, -0.01)
                     .build();
    }

    static OrderMatcher* createTodayOrdersMatcher()
    {
        datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
        datetime endOfDay = startOfDay + 86400 - 1; // 24 hours - 1 second

        MatcherBuilder builder;
        return builder.withTimeRangeMatcher(startOfDay, endOfDay)
                     .build();
    }
};

#endif // MATCHER_BUILDER_MQH

#property strict

#include "../EAComponent.mqh"

//+------------------------------------------------------------------+
//| Signal Types Enumeration                                         |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = 0,        // 無信號
    SIGNAL_BUY = 1,         // 買入信號
    SIGNAL_SELL = -1,       // 賣出信號
    SIGNAL_NEUTRAL = 2      // 中性信號
};

//+------------------------------------------------------------------+
//| Signal Strength Enumeration                                      |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_STRENGTH
{
    STRENGTH_WEAK = 1,      // 弱信號
    STRENGTH_MEDIUM = 2,    // 中等信號
    STRENGTH_STRONG = 3     // 強信號
};

//+------------------------------------------------------------------+
//| Signal Information Structure                                     |
//+------------------------------------------------------------------+
struct SignalInfo
{
    ENUM_SIGNAL_TYPE      type;           // 信號類型
    ENUM_SIGNAL_STRENGTH  strength;       // 信號強度
    double                confidence;     // 信心水平 (0.0 - 1.0)
    datetime              timestamp;      // 信號時間戳
    double                value;          // 指標數值
    string                description;    // 信號描述
};

//+------------------------------------------------------------------+
//| BaseSignal 類別                                                 |
//| 純抽象基礎類別，定義信號生成的核心介面規範                        |
//| 直接繼承自 EAComponent，所有方法都是純虛擬方法                   |
//+------------------------------------------------------------------+
class BaseSignal : public EAComponent
{
public:
    //--- 建構子與解構子
                      BaseSignal(string componentName = "BaseSignal") : EAComponent(componentName) {}
    virtual          ~BaseSignal() {}

    //+------------------------------------------------------------------+
    //| 信號介面 - 信號生成與取得                                        |
    //+------------------------------------------------------------------+

public:
    virtual SignalInfo GetLastSignal() const = 0;
    virtual ENUM_SIGNAL_TYPE GetSignalType() const = 0;
    virtual ENUM_SIGNAL_STRENGTH GetSignalStrength() const = 0;
    virtual double    GetSignalConfidence() const = 0;

    //+------------------------------------------------------------------+
    //| 計算介面 - 信號生成計算                                          |
    //+------------------------------------------------------------------+

protected:
    virtual SignalInfo GenerateSignal(int shift = 0) = 0;
    virtual void      OnGenerateSignal(int shift = 0) = 0;   // 核心信號生成邏輯處理

    //+------------------------------------------------------------------+
    //| 基礎介面 - 初始化、驗證與更新                                    |
    //+------------------------------------------------------------------+

protected:
    virtual void      OnInitialize() = 0;
    virtual void      OnValidate() = 0;
    virtual void      OnUpdate() = 0;
    virtual void      OnReset() = 0;
    virtual void      OnCleanup() = 0;
};

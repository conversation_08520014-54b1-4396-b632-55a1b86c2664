#property strict

#include "../EAComponent.mqh"

//+------------------------------------------------------------------+
//| BaseIndicator 類別                                              |
//| 純抽象基礎類別，定義技術指標計算的核心介面規範                    |
//| 直接繼承自 EAComponent，所有方法都是純虛擬方法                   |
//+------------------------------------------------------------------+
class BaseIndicator : public EAComponent
{
public:
    //--- 建構子與解構子
                      BaseIndicator(string componentName = "BaseIndicator") : EAComponent(componentName) {}
    virtual          ~BaseIndicator() {}

    //+------------------------------------------------------------------+
    //| 資訊介面 - 指標參數取得                                          |
    //+------------------------------------------------------------------+

public:
    virtual string    GetSymbol() const = 0;
    virtual ENUM_TIMEFRAMES GetTimeframe() const = 0;
    virtual int       GetPeriod() const = 0;

    //+------------------------------------------------------------------+
    //| 計算介面 - 指標數值計算                                          |
    //+------------------------------------------------------------------+

protected:
    virtual double    Calculate(int shift = 0) = 0;
    virtual void      OnCalculate(int shift = 0) = 0;        // 核心計算邏輯處理

    //+------------------------------------------------------------------+
    //| 驗證介面 - 數據有效性檢查                                        |
    //+------------------------------------------------------------------+

public:
    virtual bool      IsValidShift(int shift) const = 0;
    virtual bool      IsDataAvailable(int shift = 0) const = 0;

    //+------------------------------------------------------------------+
    //| 基礎介面 - 初始化、驗證與更新                                    |
    //+------------------------------------------------------------------+

protected:
    virtual void      OnInitialize() = 0;
    virtual void      OnValidate() = 0;
    virtual void      OnUpdate() = 0;
    virtual void      OnReset() = 0;
    virtual void      OnCleanup() = 0;

    //+------------------------------------------------------------------+
    //| 工具介面 - 市場數據存取                                          |
    //+------------------------------------------------------------------+

public:
    virtual double    GetPrice(int priceType, int shift = 0) const = 0;
    virtual datetime  GetTime(int shift = 0) const = 0;
    virtual int       GetBarsCount() const = 0;
};
//+------------------------------------------------------------------+
//| Module: Format/RespParseError.mqh                                |
//| This file is part of the mql4-lib project:                       |
//|     https://github.com/dingmaotu/mql4-lib                        |
//|                                                                  |
//| Copyright 2017 Li Ding <<EMAIL>>                       |
//|                                                                  |
//| Licensed under the Apache License, Version 2.0 (the "License");  |
//| you may not use this file except in compliance with the License. |
//| You may obtain a copy of the License at                          |
//|                                                                  |
//|     http://www.apache.org/licenses/LICENSE-2.0                   |
//|                                                                  |
//| Unless required by applicable law or agreed to in writing,       |
//| software distributed under the License is distributed on an      |
//| "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,     |
//| either express or implied.                                       |
//| See the License for the specific language governing permissions  |
//| and limitations under the License.                               |
//+------------------------------------------------------------------+
#property strict
//+------------------------------------------------------------------+
//| Parse errors                                                     |
//+------------------------------------------------------------------+
enum RespParseError
  {
   RespParseErrorNone,                // No error
   RespParseErrorNeedMoreInput,       // buffer is incomplete, needs more input
   RespParseErrorInvalidPrefix,       // type prefix is not one of '+' '-' ':' '$' '*'
   RespParseErrorNewlineMalformed,    // Newline is not "\r\n" or '\r' is not followed by '\n'
   RespParseErrorInvalidInteger,      // Integer is not valid
   RespParseErrorArrayLengthNotValid, // Array length is not a positive integer
   RespParseErrorBytesLengthNotValid  // Bytes (bulk string) length is not a positive integer or -1
  };
//+------------------------------------------------------------------+

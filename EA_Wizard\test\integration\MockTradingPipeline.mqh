//+------------------------------------------------------------------+
//|                                        MockTradingPipeline.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 模擬交易流水線類 - 用於整合測試                                   |
//+------------------------------------------------------------------+
class MockTradingPipeline : public TradingPipeline
{
private:
    bool m_shouldExecute;                       // 是否應該執行成功
    int m_executeCount;                         // 執行次數計數

public:
    // 構造函數
    MockTradingPipeline(string name = "MockPipeline",
                       string type = "Mock",
                       ENUM_TRADING_STAGE stage = INIT_START,
                       bool shouldExecute = true)
        : TradingPipeline(name, type, stage, TradingPipelineDriver::GetInstance()),
          m_shouldExecute(shouldExecute),
          m_executeCount(0)
    {
    }

    // 析構函數
    virtual ~MockTradingPipeline() {}

    // 實現抽象方法
    virtual void Main() override
    {
        m_executeCount++;
        if(!m_shouldExecute)
        {
            // 模擬執行失敗
            return;
        }
    }

    // 測試輔助方法
    int GetExecuteCount() const { return m_executeCount; }
    void SetShouldExecute(bool should) { m_shouldExecute = should; }
};

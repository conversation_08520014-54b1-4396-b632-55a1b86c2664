//+------------------------------------------------------------------+
//| EAOrderGroupManager.mqh                                          |
//| 專門管理 EAOrderGroup 實例的模組類別                             |
//| 遵循 EA_Wizard 框架模式，使用組合模式管理多個 OrderGroup        |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard Framework"
#property version   "1.00"
#property strict

#include "../../Base/BaseComponent.mqh"
#include "../../../../mql4_module/mql4-lib/Collection/HashMap.mqh"
#include "../../../../mql4_module/mql4-lib/Trade/OrderTracker.mqh"
#include "EAOrderGroup.mqh"
#include "MatcherBuilder.mqh"
#include "OrderGroupCodec.mqh"

//+------------------------------------------------------------------+
//| OrderGroup 管理統計結構                                         |
//+------------------------------------------------------------------+
struct OrderGroupManagerStats
{
    int               totalGroups;        // 總群組數
    int               totalOrders;        // 總訂單數
    double            totalProfit;        // 總盈虧
    double            totalLots;          // 總手數
    int               activeGroups;       // 活躍群組數
    int               emptyGroups;        // 空群組數
    string            nextGroupId;        // 下一個群組編碼
    datetime          lastUpdate;         // 最後更新時間
};

//+------------------------------------------------------------------+
//| OrderGroup 查詢鍵結構                                           |
//+------------------------------------------------------------------+
struct OrderGroupKey
{
    string            symbol;             // 交易符號
    int               magicNumber;        // 魔術數字

    // 建構函數
    OrderGroupKey(string sym = "", int magic = 0) : symbol(sym), magicNumber(magic) {}

    // 生成唯一鍵字串
    string            ToString() const { return StringFormat("%s_%d", symbol, magicNumber); }
};

//+------------------------------------------------------------------+
//| EAOrderGroup 管理器類別                                         |
//| 負責管理多個 EAOrderGroup 實例的生命週期和操作                  |
//+------------------------------------------------------------------+
class EAOrderGroupManager : public BaseComponent
{
private:
    // 錯誤代碼定義
    static const BaseErrorDescriptor CODE_ERRORS[];
    static bool                     g_lockdownError;

    // 核心成員變量
    string                         m_symbol;            // 交易符號
    int                            m_originalMagic;     // 原始魔術數字（編碼基礎）
    HashMap<string, EAOrderGroup*>  m_groups;           // OrderGroup 集合 (key: groupId)
    OrderGroupManagerStats          m_stats;            // 管理器統計
    int                            m_maxGroups;         // 最大群組數量限制
    string                         m_nextGroupId;       // 下一個可用的群組編碼

    // 內部方法
    string                        GenerateGroupId(int originalMagic, int groupIndex) const;
    void                          UpdateStatistics();
    void                          ResetStatistics();
    bool                          ValidateGroupParameters(string groupId);
    OrderTracker*                 CreateTracker(string groupId);

    // 預先分配相關方法
    void                          PreallocateGroups();
    void                          ScanExistingOrders();
    void                          UpdateNextGroupId();

public:
    // 建構函數和解構函數
                                  EAOrderGroupManager(string symbol = "",
                                                     int magicNumber = 0,
                                                     string name = "EAOrderGroupManager",
                                                     int maxGroups = 50);
    virtual                      ~EAOrderGroupManager();

    //--- Core Management
    string                       GenerateNewGroupId(int originalMagic);
    EAOrderGroup*                CreateGroup(string groupId);
    bool                         RemoveGroup(string groupId);
    bool                         RemoveGroup(EAOrderGroup* group);
    EAOrderGroup*                GetGroup(string groupId);
    bool                         HasGroup(string groupId);

    //--- Batch Operations
    void                         UpdateAllGroups();
    int                          CleanupEmptyGroups();
    void                         RemoveAllGroups();

    //--- Statistics/Query
    OrderGroupManagerStats       GetStatistics();
    int                          GetGroupCount() const { return m_groups.size(); }
    int                          GetActiveGroupCount() const;
    int                          GetAllKeys(Vector<string>& keys);
    int                          GetGroups(Vector<EAOrderGroup*>& groups);

    //--- Configuration Management
    int                          GetMaxGroups() const { return m_maxGroups; }
    string                       GetNextGroupId() const { return m_nextGroupId; }

protected:
    // BaseComponent 覆寫方法
    virtual bool                 OnInitialize() override;
    virtual bool                 OnValidate() override;
    virtual bool                 OnUpdate() override;
    virtual void                 OnCleanup() override;
};

// 靜態成員初始化
const BaseErrorDescriptor EAOrderGroupManager::CODE_ERRORS[] =
{
    // EAOrderGroup 管理器錯誤
    {10301, "Invalid symbol parameter"},
    {10302, "Invalid magic number parameter"},
    {10303, "OrderGroup already exists"},
    {10304, "OrderGroup not found"},
    {10305, "Maximum groups limit reached"},
    {10306, "Failed to create OrderGroup"},
    {10307, "Failed to remove OrderGroup"},
    {10308, "OrderGroup pointer is null"},
    {10309, "Statistics update failed"},
    {10310, "Group validation failed"}
};

bool EAOrderGroupManager::g_lockdownError = false;

// Constructor
EAOrderGroupManager::EAOrderGroupManager(string symbol = "",
                                         int magicNumber = 0,
                                         string name = "EAOrderGroupManager",
                                         int maxGroups = 50)
    : BaseComponent(name), m_groups(NULL, true), m_maxGroups(maxGroups)
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_originalMagic = (magicNumber == 0) ? 1000 : magicNumber; // 預設原始魔術數字為 1000
    m_nextGroupId = GenerateGroupId(m_originalMagic, OrderGroupCodec::GetMinGroupIndex()); // 初始化為第一個群組編碼
    ResetStatistics();

    // 預先分配所有可能的群組位置
    PreallocateGroups();

    // 掃描現有訂單並建立對應的 EAOrderGroup
    ScanExistingOrders();

    if(!IsErrorLockedDown())
        AppendError(CODE_ERRORS);
    SetLockDownError(true);
}

// Destructor
EAOrderGroupManager::~EAOrderGroupManager()
{
    RemoveAllGroups();
}

// Generate group id using OrderGroupCodec
string EAOrderGroupManager::GenerateGroupId(int originalMagic, int groupIndex) const
{
    return OrderGroupCodec::Encode(originalMagic, groupIndex);
}

// Create OrderTracker for group
OrderTracker* EAOrderGroupManager::CreateTracker(string groupId)
{
    // 驗證群組編碼格式
    if(!OrderGroupCodec::IsValidGroupId(groupId))
        return NULL;

    int encodedMagic = (int)StringToInteger(groupId);

    // 建立 MatcherBuilder 和 OrderMatcher
    MatcherBuilder builder;
    OrderMatcher* matcher = builder.withSymbolMatcher(m_symbol)
                                   .withMagicNumberMatcher(encodedMagic)
                                   .build();

    if(matcher == NULL)
        return NULL;

    // 建立 TradingPool
    TradingPool* pool = new TradingPool(matcher);
    if(pool == NULL)
        return NULL;

    // 建立 OrderTracker
    OrderTracker* tracker = new OrderTracker(pool);
    return tracker;
}

// Validate group parameters using OrderGroupCodec
bool EAOrderGroupManager::ValidateGroupParameters(string groupId)
{
    if(!OrderGroupCodec::IsValidGroupId(groupId))
    {
        HandleError(10301, GetErrorDescription(10301));
        return false;
    }

    return true;
}

// Generate new group id with auto-increment group index
string EAOrderGroupManager::GenerateNewGroupId(int originalMagic)
{
    // 從當前的 m_nextGroupId 開始尋找下一個可用的群組編碼
    int currentGroupIndex = OrderGroupCodec::DecodeGroupIndex(m_nextGroupId);

    // 尋找下一個可用的群組位置（NULL 值）
    for(int groupIndex = currentGroupIndex; groupIndex <= m_maxGroups; groupIndex++)
    {
        string candidateGroupId = GenerateGroupId(originalMagic, groupIndex);

        // 檢查該群組位置是否可用（NULL 或不存在）
        if(!m_groups.contains(candidateGroupId) || m_groups.get(candidateGroupId, NULL) == NULL)
        {
            // 更新 m_nextGroupId 為下一個位置
            if(groupIndex < m_maxGroups)
                m_nextGroupId = GenerateGroupId(originalMagic, groupIndex + 1);
            else
                m_nextGroupId = GenerateGroupId(originalMagic, OrderGroupCodec::GetMinGroupIndex()); // 回到開始

            return candidateGroupId;
        }
    }

    // 如果沒有找到可用位置，返回空字串
    return "";
}

// Create new EAOrderGroup
EAOrderGroup* EAOrderGroupManager::CreateGroup(string groupId)
{
    ClearError();

    if(!ValidateGroupParameters(groupId))
        return NULL;

    // 檢查群組是否在預分配的範圍內
    if(!m_groups.contains(groupId))
    {
        HandleError(10304, GetErrorDescription(10304)); // 群組不在預分配範圍內
        return NULL;
    }

    // 檢查群組是否已經存在（不是 NULL）
    EAOrderGroup* existingGroup = m_groups.get(groupId, NULL);
    if(existingGroup != NULL)
    {
        HandleError(10303, GetErrorDescription(10303)); // 群組已存在
        return NULL;
    }

    // 解碼取得編碼後的魔術數字
    int encodedMagic = (int)StringToInteger(groupId);

    EAOrderGroup* group = new EAOrderGroup(m_symbol, encodedMagic);
    if(group == NULL)
    {
        HandleError(10306, GetErrorDescription(10306));
        return NULL;
    }

    m_groups.set(groupId, group);
    UpdateStatistics();

    return group;
}

// Remove group by group id
bool EAOrderGroupManager::RemoveGroup(string groupId)
{
    ClearError();

    if(!m_groups.contains(groupId))
    {
        HandleError(10304, GetErrorDescription(10304));
        return false;
    }

    // 取得群組物件並刪除（如果不是 NULL）
    EAOrderGroup* group = m_groups.get(groupId, NULL);
    if(group != NULL)
    {
        delete group; // 刪除實際的群組物件
    }

    // 將群組位置重設為 NULL（保持預分配結構）
    m_groups.set(groupId, NULL);

    // 更新下一個可用的群組編碼
    UpdateNextGroupId();

    UpdateStatistics();
    return true;
}

// Remove group by pointer
bool EAOrderGroupManager::RemoveGroup(EAOrderGroup* group)
{
    ClearError();

    if(group == NULL)
    {
        HandleError(10308, GetErrorDescription(10308));
        return false;
    }

    foreachm(string, key, EAOrderGroup*, storedGroup, m_groups)
    {
        if(storedGroup == group)
        {
            bool result = m_groups.remove(key);

            if(!result)
            {
                HandleError(10307, GetErrorDescription(10307));
                return false;
            }

            UpdateStatistics();
            return true;
        }
    }

    HandleError(10304, GetErrorDescription(10304));
    return false;
}

// Get group by group id
EAOrderGroup* EAOrderGroupManager::GetGroup(string groupId)
{
    return m_groups.get(groupId, NULL);
}

// Check if group exists (not NULL)
bool EAOrderGroupManager::HasGroup(string groupId)
{
    if(!m_groups.contains(groupId))
        return false;

    return m_groups.get(groupId, NULL) != NULL;
}

// Update all groups
void EAOrderGroupManager::UpdateAllGroups()
{
    foreachm(string, key, EAOrderGroup*, group, m_groups)
    {
        if(group != NULL)
            group.RefreshGroup();
    }

    UpdateStatistics();
}

// Cleanup empty groups
int EAOrderGroupManager::CleanupEmptyGroups()
{
    Vector<string> keysToCleanup;
    int cleanupCount = 0;

    foreachm(string, key, EAOrderGroup*, group, m_groups)
    {
        if(group != NULL && group.GetOrderCount() == 0)
            keysToCleanup.add(key);
    }

    for(int i = 0; i < keysToCleanup.size(); i++)
    {
        string key = keysToCleanup.get(i);
        EAOrderGroup* group = m_groups.get(key, NULL);
        if(group != NULL)
        {
            delete group; // 刪除空的群組物件
            m_groups.set(key, NULL); // 重設為 NULL（保持預分配結構）
            cleanupCount++;
        }
    }

    if(cleanupCount > 0)
    {
        UpdateNextGroupId(); // 更新下一個可用的群組編碼
        UpdateStatistics();
    }

    return cleanupCount;
}

// Remove all groups
void EAOrderGroupManager::RemoveAllGroups()
{
    // 刪除所有非 NULL 的群組物件
    foreachm(string, key, EAOrderGroup*, group, m_groups)
    {
        if(group != NULL)
        {
            delete group;
        }
    }

    m_groups.clear();
    ResetStatistics();
}

// Get statistics
OrderGroupManagerStats EAOrderGroupManager::GetStatistics()
{
    UpdateStatistics();
    return m_stats;
}

// Get count of active groups (non-NULL)
int EAOrderGroupManager::GetActiveGroupCount() const
{
    int activeCount = 0;
    foreachm(string, key, EAOrderGroup*, group, GetPointer(m_groups))
    {
        if(group != NULL)
            activeCount++;
    }
    return activeCount;
}

// Get all keys
int EAOrderGroupManager::GetAllKeys(Vector<string>& keys)
{
    keys.clear();

    foreachm(string, key, EAOrderGroup*, group, GetPointer(m_groups))
    {
        keys.add(key);
    }

    return keys.size();
}

// Get all groups
int EAOrderGroupManager::GetGroups(Vector<EAOrderGroup*>& groups)
{
    groups.clear();

    foreachm(string, key, EAOrderGroup*, group, GetPointer(m_groups))
    {
        if(group != NULL)
            groups.add(group);
    }

    return groups.size();
}

// Update statistics
void EAOrderGroupManager::UpdateStatistics()
{
    ResetStatistics();

    m_stats.totalGroups = m_groups.size(); // 總分配的群組位置數
    m_stats.nextGroupId = m_nextGroupId;   // 下一個可用的群組編碼
    m_stats.lastUpdate = TimeCurrent();

    foreachm(string, key, EAOrderGroup*, group, GetPointer(m_groups))
    {
        if(group != NULL)
        {
            int orderCount = group.GetOrderCount();
            OrderGroupStats groupStats = group.GetStatistics();

            m_stats.totalOrders += orderCount;
            m_stats.totalProfit += groupStats.totalProfit;
            m_stats.totalLots += groupStats.totalLots;

            if(orderCount > 0)
                m_stats.activeGroups++;
            else
                m_stats.emptyGroups++;
        }
        else
        {
            // NULL 群組計為空群組
            m_stats.emptyGroups++;
        }
    }
}

// Reset statistics
void EAOrderGroupManager::ResetStatistics()
{
    m_stats.totalGroups = 0;
    m_stats.totalOrders = 0;
    m_stats.totalProfit = 0.0;
    m_stats.totalLots = 0.0;
    m_stats.activeGroups = 0;
    m_stats.emptyGroups = 0;
    m_stats.nextGroupId = "";
    m_stats.lastUpdate = 0;
}

// BaseComponent override - Initialize
bool EAOrderGroupManager::OnInitialize()
{
    ClearError();

    // 驗證 m_maxGroups 不超過編解碼器的最大群組索引
    if(m_maxGroups > OrderGroupCodec::GetMaxGroupIndex())
    {
        HandleError(10305, StringFormat("Maximum groups (%d) exceeds codec limit (%d)",
                                       m_maxGroups, OrderGroupCodec::GetMaxGroupIndex()));
        return false;
    }

    ResetStatistics();
    return true;
}

// BaseComponent override - Validate
bool EAOrderGroupManager::OnValidate()
{
    ClearError();

    if(m_maxGroups <= 0)
    {
        HandleError(10310, GetErrorDescription(10310));
        return false;
    }

    // 驗證原始魔術數字是否有效
    if(!OrderGroupCodec::IsValidMagicNumber(m_originalMagic))
    {
        HandleError(10302, GetErrorDescription(10302));
        return false;
    }

    // 在預分配模式下，NULL 群組是正常的，不需要報錯
    // 只驗證非 NULL 群組的有效性
    foreachm(string, key, EAOrderGroup*, group, GetPointer(m_groups))
    {
        if(group != NULL)
        {
            // 可以在這裡添加對群組物件的進一步驗證
            // 例如檢查群組的符號和魔術數字是否正確
        }
    }

    return true;
}

// BaseComponent override - Update
bool EAOrderGroupManager::OnUpdate()
{
    ClearError();

    UpdateStatistics();

    // Check if any error occurred during statistics update
    if(HasError())
    {
        HandleError(10309, GetErrorDescription(10309));
        return false;
    }

    return true;
}

// BaseComponent override - Cleanup
void EAOrderGroupManager::OnCleanup()
{
    RemoveAllGroups();
}

//+------------------------------------------------------------------+
//| 預先分配所有可能的群組位置                                       |
//+------------------------------------------------------------------+
void EAOrderGroupManager::PreallocateGroups()
{
    // 使用固定的 m_maxGroups，不進行自我調整（已在 OnInitialize 中驗證）

    // 為每個可能的群組索引預先建立 HashMap 條目
    for(int groupIndex = OrderGroupCodec::GetMinGroupIndex(); groupIndex <= m_maxGroups; groupIndex++)
    {
        string groupId = GenerateGroupId(m_originalMagic, groupIndex);

        // 在 HashMap 中建立條目，初始值設為 NULL
        m_groups.set(groupId, NULL);
    }

    Print(StringFormat("[%s] 預先分配了 %d 個群組位置 (原始魔術數字: %d)",
                       GetName(), m_maxGroups, m_originalMagic));
}

//+------------------------------------------------------------------+
//| 掃描現有訂單並建立對應的 EAOrderGroup                           |
//+------------------------------------------------------------------+
void EAOrderGroupManager::ScanExistingOrders()
{
    // 建立帶有 symbol filter 的 OrderMatcher
    MatcherBuilder builder;
    OrderMatcher* symbolMatcher = builder.withSymbolMatcher(m_symbol).build();
    if(symbolMatcher == NULL)
    {
        Print(StringFormat("[%s] 無法建立 symbol matcher", GetName()));
        return;
    }

    // 建立帶有 symbol filter 的 TradingPool
    TradingPool* scanPool = new TradingPool(symbolMatcher);
    if(scanPool == NULL)
    {
        Print(StringFormat("[%s] 無法建立掃描用的 TradingPool", GetName()));
        delete symbolMatcher;
        return;
    }

    // 建立 OrderTracker 來掃描訂單
    OrderTracker* tracker = new OrderTracker(scanPool);
    if(tracker == NULL)
    {
        Print(StringFormat("[%s] 無法建立 OrderTracker", GetName()));
        delete scanPool;
        delete symbolMatcher;
        return;
    }

    int foundGroups = 0;
    int totalOrders = 0;

    // 使用 foreachorder 巨集遍歷符合條件的訂單
    foreachorder(scanPool)
    {
        totalOrders++;

        int orderMagic = OrderMagicNumber();

        // 檢查魔術數字是否符合我們的編碼格式
        string potentialGroupId = StringFormat("%d", orderMagic);
        if(!OrderGroupCodec::IsValidGroupId(potentialGroupId))
            continue;

        // 解碼魔術數字檢查是否屬於我們的原始魔術數字
        int decodedOriginalMagic = OrderGroupCodec::DecodeOriginalMagic(potentialGroupId);
        if(decodedOriginalMagic != m_originalMagic)
            continue;

        // 檢查群組是否已經存在於我們的預分配列表中
        if(!m_groups.contains(potentialGroupId))
            continue;

        // 如果該群組位置還是 NULL，則建立實際的 EAOrderGroup
        if(m_groups.get(potentialGroupId, NULL) == NULL)
        {
            EAOrderGroup* group = new EAOrderGroup(m_symbol, orderMagic);
            if(group != NULL)
            {
                m_groups.set(potentialGroupId, group);
                foundGroups++;

                Print(StringFormat("[%s] 發現現有群組: %s (編碼魔術數字: %d)",
                                   GetName(), potentialGroupId, orderMagic));
            }
        }
    }

    // 清理資源
    delete tracker;
    delete scanPool;
    delete symbolMatcher;

    // 更新下一個可用的群組編碼
    UpdateNextGroupId();

    Print(StringFormat("[%s] 掃描完成，發現 %d 個現有群組 (總訂單數: %d)",
                       GetName(), foundGroups, totalOrders));
}

//+------------------------------------------------------------------+
//| 更新下一個可用的群組編碼                                         |
//+------------------------------------------------------------------+
void EAOrderGroupManager::UpdateNextGroupId()
{
    // 從最小群組索引開始尋找第一個可用的位置
    for(int groupIndex = OrderGroupCodec::GetMinGroupIndex(); groupIndex <= m_maxGroups; groupIndex++)
    {
        string candidateGroupId = GenerateGroupId(m_originalMagic, groupIndex);

        // 如果找到可用位置（NULL 或不存在），設為下一個群組編碼
        if(!m_groups.contains(candidateGroupId) || m_groups.get(candidateGroupId, NULL) == NULL)
        {
            m_nextGroupId = candidateGroupId;
            return;
        }
    }

    // 如果所有位置都被佔用，設為第一個位置（循環使用）
    m_nextGroupId = GenerateGroupId(m_originalMagic, OrderGroupCodec::GetMinGroupIndex());
}

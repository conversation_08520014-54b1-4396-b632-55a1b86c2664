#property strict


interface BaseErrorHandler{
    void    HandleError(int errorCode, string errorMessage);
    void    ClearError();
    bool    HasError() const;
    int     LastError() const;
    string  LastErrorMessage() const;
};

interface BaseInitializable : BaseErrorHandler{
    void Initialize();
    void OnInitialize();
    bool IsInitialized() const;
};

interface BaseValidatable : BaseInitializable{
    void Validate();
    void OnValidate();
    bool IsValid() const;
};

interface BaseUpdatable : BaseValidatable{
    void Update();
    void OnUpdate();
};

interface BaseResetable : BaseUpdatable{
    void Reset();
    void OnReset();
};

interface BaseCleanable : BaseResetable{
    void Cleanup();
    void OnCleanup();
};

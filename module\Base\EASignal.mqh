//+------------------------------------------------------------------+
//|                                                     EASignal.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef EA_SIGNAL_MQH
#define EA_SIGNAL_MQH

#include "Interface/BaseSignal.mqh"

//+------------------------------------------------------------------+
//| EASignal 類別                                                   |
//| 繼承自 BaseSignal，提供信號生成的具體實作                       |
//| 遵循 EA_Wizard 框架的 EAComponent 格式風格和編碼慣例             |
//+------------------------------------------------------------------+
class EASignal : public BaseSignal
{
private:
    // 錯誤處理相關靜態成員
    static const EAErrorDescriptor      CODE_ERRORS[];   // 組件特定錯誤代碼
    static bool                         g_lockdown;      // 錯誤處理鎖定

private:
    // 信號處理成員變數
    SignalInfo        m_lastSignal;       // 最後生成的信號

    //+------------------------------------------------------------------+
    //| 錯誤處理方法 - 繼承自 EAComponent                               |
    //+------------------------------------------------------------------+

protected:
    virtual void      LockDown(bool lockdown = true) override;
    virtual bool      IsLockedDown() override { return g_lockdown; }

    //+------------------------------------------------------------------+
    //| 信號處理方法 - 內部信號管理                                    |
    //+------------------------------------------------------------------+

protected:
    void              SetSignal(ENUM_SIGNAL_TYPE type, ENUM_SIGNAL_STRENGTH strength,
                                double confidence, double value, string description = "");
    SignalInfo        GetSignal() const { return m_lastSignal; }

public:
    //--- 建構子與解構子
                      EASignal(string componentName = "EASignal");
    virtual          ~EASignal();

    //+------------------------------------------------------------------+
    //| 信號介面實作 - 信號生成與取得                                    |
    //+------------------------------------------------------------------+

public:
    virtual SignalInfo GetLastSignal() const override { return m_lastSignal; }
    virtual ENUM_SIGNAL_TYPE GetSignalType() const override { return m_lastSignal.type; }
    virtual ENUM_SIGNAL_STRENGTH GetSignalStrength() const override { return m_lastSignal.strength; }
    virtual double    GetSignalConfidence() const override { return m_lastSignal.confidence; }

    //+------------------------------------------------------------------+
    //| 計算介面實作 - 信號生成計算                                      |
    //+------------------------------------------------------------------+

protected:
    SignalInfo        GenerateSignal(int shift = 0);
    virtual void      OnGenerateSignal(int shift = 0) override { /* 子類別應覆寫此方法 */ }

    //+------------------------------------------------------------------+
    //| 基礎介面實作 - 初始化、驗證與更新                                |
    //+------------------------------------------------------------------+

protected:
    virtual void      OnInitialize() override;
    virtual void      OnValidate() override;
    virtual void      OnUpdate() override;
    virtual void      OnReset() override;
    virtual void      OnCleanup() override;
};

//+------------------------------------------------------------------+
//| EASignal 靜態成員變數定義                                       |
//+------------------------------------------------------------------+

// 靜態錯誤代碼陣列定義
static const EAErrorDescriptor EASignal::CODE_ERRORS[] = {
    {30001, "信號生成失敗"},
    {30002, "無效的信號類型"},
    {30003, "無效的信號強度"},
    {30004, "信心水平超出範圍"},
    {30005, "信號時間戳無效"},
    {30006, "信號描述為空"},
};

// 靜態錯誤處理鎖定狀態
static bool EASignal::g_lockdown = false;

//+------------------------------------------------------------------+
//| EASignal 方法實作                                              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 建構子 - 初始化所有成員變數                                      |
//+------------------------------------------------------------------+
EASignal::EASignal(string componentName = "EASignal") : BaseSignal(componentName)
{
    // 初始化信號結構
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";

    // 設定錯誤處理鎖定狀態
    if(!IsLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    LockDown(true);
}

//+------------------------------------------------------------------+
//| 解構子 - 清理資源                                               |
//+------------------------------------------------------------------+
EASignal::~EASignal()
{
    // 清除信號資訊
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";
}

//+------------------------------------------------------------------+
//| 錯誤處理方法實作                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 設定錯誤處理鎖定狀態                                             |
//+------------------------------------------------------------------+
void EASignal::LockDown(bool lockdown = true)
{
    g_lockdown = lockdown;

    // 輸出鎖定狀態變更的日誌訊息
    string statusMsg = lockdown ? "已鎖定" : "已解鎖";
    Print(StringFormat("EASignal 錯誤處理狀態: %s", statusMsg));
}

//+------------------------------------------------------------------+
//| 生成信號 - 具體實作方法                                          |
//+------------------------------------------------------------------+
SignalInfo EASignal::GenerateSignal(int shift = 0)
{
    // 運行時參數驗證
    Validate();

    // 如果驗證失敗，返回空信號
    if(!IsValid())
    {
        m_lastSignal.type = SIGNAL_NONE;
        m_lastSignal.strength = STRENGTH_WEAK;
        m_lastSignal.confidence = 0.0;
        m_lastSignal.timestamp = TimeCurrent();
        m_lastSignal.value = 0.0;
        m_lastSignal.description = "信號生成失敗";
        return m_lastSignal;
    }

    // 生成信號
    OnGenerateSignal(shift);

    // 返回信號
    return m_lastSignal;
}

//+------------------------------------------------------------------+
//| 設定信號資訊                                                     |
//+------------------------------------------------------------------+
void EASignal::SetSignal(ENUM_SIGNAL_TYPE type, ENUM_SIGNAL_STRENGTH strength,
                         double confidence, double value, string description = "")
{
    m_lastSignal.type = type;
    m_lastSignal.strength = strength;
    m_lastSignal.confidence = MathMax(0.0, MathMin(1.0, confidence)); // 限制在 0.0-1.0 範圍
    m_lastSignal.timestamp = TimeCurrent();
    m_lastSignal.value = value;
    m_lastSignal.description = description;
}

//+------------------------------------------------------------------+
//| BaseSignal 介面實作 - 生命週期方法                              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 信號初始化處理                                                   |
//+------------------------------------------------------------------+
void EASignal::OnInitialize()
{
    // 執行父類別的初始化邏輯
    EAComponent::OnInitialize();

    // 清除信號狀態
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";
}

//+------------------------------------------------------------------+
//| 信號驗證處理 - 集中所有驗證邏輯                                   |
//+------------------------------------------------------------------+
void EASignal::OnValidate()
{
    // 執行父類別的驗證邏輯
    EAComponent::OnValidate();

    // 驗證信號類型
    if (m_lastSignal.type < SIGNAL_SELL || m_lastSignal.type > SIGNAL_NEUTRAL)
    {
        HandleError(30002, GetErrorDescription(30002));
        return;
    }

    // 驗證信號強度
    if (m_lastSignal.strength < STRENGTH_WEAK || m_lastSignal.strength > STRENGTH_STRONG)
    {
        HandleError(30003, GetErrorDescription(30003));
        return;
    }

    // 驗證信心水平範圍
    if (m_lastSignal.confidence < 0.0 || m_lastSignal.confidence > 1.0)
    {
        HandleError(30004, GetErrorDescription(30004));
        return;
    }

    // 驗證時間戳
    if (m_lastSignal.timestamp <= 0)
    {
        HandleError(30005, GetErrorDescription(30005));
        return;
    }
}

//+------------------------------------------------------------------+
//| 信號更新處理                                                     |
//+------------------------------------------------------------------+
void EASignal::OnUpdate()
{
    // 執行父類別的更新邏輯
    EAComponent::OnUpdate();

    // 執行信號生成（會自動呼叫 OnGenerateSignal）
    GenerateSignal(0);
}

//+------------------------------------------------------------------+
//| 信號重置處理                                                     |
//+------------------------------------------------------------------+
void EASignal::OnReset()
{
    // 執行父類別的重置邏輯
    EAComponent::OnReset();

    // 重置信號狀態
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";
}

//+------------------------------------------------------------------+
//| 信號清理處理                                                     |
//+------------------------------------------------------------------+
void EASignal::OnCleanup()
{
    // 執行父類別的清理邏輯
    EAComponent::OnCleanup();

    // 清除信號資訊
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";
}

#endif // EA_SIGNAL_MQH

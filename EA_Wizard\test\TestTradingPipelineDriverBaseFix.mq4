//+------------------------------------------------------------------+
//|                              TestTradingPipelineDriverBaseFix.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 測試 TradingPipelineDriverBase 修復的腳本                       |
//+------------------------------------------------------------------+

#include "unit/TestTradingPipelineDriverBase.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 開始測試 TradingPipelineDriverBase 修復 ===");
    Print("測試時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 60));
    
    // 運行 TradingPipelineDriverBase 測試
    TestRunner* runner = new TestRunner();
    TestTradingPipelineDriverBase* test = new TestTradingPipelineDriverBase(runner);
    
    Print("運行 TradingPipelineDriverBase 單元測試...");
    runner.RunTestCase(test);
    
    // 顯示測試結果
    runner.ShowSummary();
    
    // 檢查是否所有測試都通過
    bool allPassed = (runner.GetFailedCount() == 0);
    
    Print("\n" + StringRepeat("=", 60));
    if(allPassed)
    {
        Print("✅ 所有測試通過！TradingPipelineDriverBase 修復成功");
    }
    else
    {
        Print("❌ 仍有測試失敗，需要進一步檢查");
        Print("失敗測試數量: " + IntegerToString(runner.GetFailedCount()));
        Print("總測試數量: " + IntegerToString(runner.GetTotalCount()));
    }
    Print("測試結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 60));
    
    // 清理資源
    delete test;
    delete runner;
    
    // 額外驗證：直接測試錯誤處理器功能
    VerifyErrorHandlerIntegration();
}

//+------------------------------------------------------------------+
//| 驗證錯誤處理器集成                                               |
//+------------------------------------------------------------------+
void VerifyErrorHandlerIntegration()
{
    Print("\n=== 驗證錯誤處理器集成 ===");
    
    // 創建測試驅動器實例
    TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("VerifyTest", "VerifyType");
    
    // 測試構造函數中的錯誤處理器
    TradingErrorHandler* errorHandler = driver.GetErrorHandler();
    if(errorHandler != NULL)
    {
        Print("✅ 錯誤處理器在構造函數中正確初始化");
        
        // 測試錯誤處理器功能
        int initialCount = errorHandler.GetErrorCount();
        errorHandler.AddError("驗證測試錯誤", "VerifyErrorHandlerIntegration", ERROR_LEVEL_INFO);
        int newCount = errorHandler.GetErrorCount();
        
        if(newCount == initialCount + 1)
        {
            Print("✅ 錯誤處理器功能正常");
            Print("   錯誤數量從 " + IntegerToString(initialCount) + " 增加到 " + IntegerToString(newCount));
            Print("   最後錯誤: " + errorHandler.GetLastError());
        }
        else
        {
            Print("❌ 錯誤處理器功能異常");
        }
    }
    else
    {
        Print("❌ 錯誤處理器在構造函數中未正確初始化");
    }
    
    // 測試初始化流程
    Print("\n測試初始化流程...");
    bool initResult = driver.Initialize();
    
    if(initResult)
    {
        Print("✅ 驅動器初始化成功");
        
        // 檢查初始化後的狀態
        if(driver.IsInitialized())
        {
            Print("✅ 驅動器狀態檢查通過");
            
            // 檢查所有組件
            bool allComponentsReady = (
                driver.GetManager() != NULL &&
                driver.GetRegistry() != NULL &&
                driver.GetExplorer() != NULL &&
                driver.GetObjectRegistry() != NULL &&
                driver.GetErrorHandler() != NULL
            );
            
            if(allComponentsReady)
            {
                Print("✅ 所有組件（包括錯誤處理器）都正確初始化");
            }
            else
            {
                Print("❌ 部分組件初始化失敗");
                Print("   管理器: " + (driver.GetManager() != NULL ? "正常" : "NULL"));
                Print("   註冊器: " + (driver.GetRegistry() != NULL ? "正常" : "NULL"));
                Print("   探索器: " + (driver.GetExplorer() != NULL ? "正常" : "NULL"));
                Print("   對象註冊器: " + (driver.GetObjectRegistry() != NULL ? "正常" : "NULL"));
                Print("   錯誤處理器: " + (driver.GetErrorHandler() != NULL ? "正常" : "NULL"));
            }
        }
        else
        {
            Print("❌ 驅動器狀態檢查失敗");
        }
    }
    else
    {
        Print("❌ 驅動器初始化失敗");
    }
    
    // 清理資源
    delete driver;
    
    Print("=== 錯誤處理器集成驗證完成 ===");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                   |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result += str;
    }
    return result;
}

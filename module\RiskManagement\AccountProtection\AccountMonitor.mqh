//+------------------------------------------------------------------+
//|                                            AccountMonitor.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_MONITOR_MQH
#define ACCOUNT_MONITOR_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"

//+------------------------------------------------------------------+
//| AccountMonitor Class                                             |
//| Combines monitoring and status management responsibilities      |
//| Responsibilities: Account Monitoring + Status Management        |
//+------------------------------------------------------------------+
class AccountMonitor : public BaseComponent
{
private:
    static const BaseErrorDescriptor CODE_ERRORS[]; // Component specific error codes
    static bool                      g_lockdownError;   // Lockdown error handling

    // Monitoring variables
    double                  m_initialBalance;       // Initial account balance
    double                  m_dailyStartBalance;    // Balance at start of day
    double                  m_maxEquity;            // Maximum equity reached
    datetime                m_lastResetTime;        // Last daily reset time
    
    // Status management
    ENUM_PROTECTION_STATUS  m_currentStatus;        // Current protection status
    ENUM_PROTECTION_STATUS  m_previousStatus;       // Previous protection status
    datetime                m_statusChangeTime;     // Last status change time
    string                  m_statusMessage;        // Status message
    
    // Status thresholds (as percentage of limits)
    double                  m_warningThreshold;     // Warning threshold (50%)
    double                  m_criticalThreshold;    // Critical threshold (70%)
    double                  m_emergencyThreshold;   // Emergency threshold (90%)

protected:
    //--- Override base class methods
    virtual void            SetLockDownError(bool lockdown = true) override;
    virtual bool            IsErrorLockedDown() override;

public:
    //--- Constructor and Destructor
                            AccountMonitor();
    virtual                ~AccountMonitor();
    
    //--- Monitoring methods
    void                    UpdateMetrics();
    void                    ResetDailyCounters();
    bool                    IsNewTradingDay() const;
    
    //--- Status management methods
    void                    UpdateStatus(double maxLossPercent, double maxDrawdownPercent);
    void                    SetStatus(ENUM_PROTECTION_STATUS status, string message = "");
    
    //--- Information methods
    double                  GetInitialBalance() const { return m_initialBalance; }
    double                  GetDailyStartBalance() const { return m_dailyStartBalance; }
    double                  GetMaxEquity() const { return m_maxEquity; }
    double                  GetCurrentLossPercent() const;
    double                  GetCurrentDrawdownPercent() const;
    double                  GetDailyLoss() const;
    double                  GetDailyProfit() const;
    
    //--- Status information methods
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const { return m_currentStatus; }
    ENUM_PROTECTION_STATUS  GetPreviousStatus() const { return m_previousStatus; }
    string                  GetStatusDescription() const;
    string                  GetStatusMessage() const { return m_statusMessage; }
    bool                    HasStatusChanged() const { return m_currentStatus != m_previousStatus; }
    
    //--- Threshold configuration
    void                    SetWarningThreshold(double threshold) { m_warningThreshold = threshold; }
    void                    SetCriticalThreshold(double threshold) { m_criticalThreshold = threshold; }
    void                    SetEmergencyThreshold(double threshold) { m_emergencyThreshold = threshold; }
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetMonitoringSummary() const;
    
private:
    //--- Internal data access methods
    double                  GetAccountBalance() const { return AccountBalance(); }
    double                  GetAccountEquity() const { return AccountEquity(); }
    
    //--- Internal status calculation
    ENUM_PROTECTION_STATUS  CalculateStatus(double maxLossPercent, double maxDrawdownPercent);
    bool                    IsLossThresholdExceeded(double threshold, double maxLossPercent);
    bool                    IsDrawdownThresholdExceeded(double threshold, double maxDrawdownPercent);
    string                  GenerateStatusMessage(ENUM_PROTECTION_STATUS status);
};

const BaseErrorDescriptor AccountMonitor::CODE_ERRORS[] =   // Component specific error codes
{
    // Account monitor specific errors
    {13001, "Invalid initial account balance"},
    {13002, "Invalid daily start balance"}
};

bool AccountMonitor::g_lockdownError = false;  // Lockdown error handling

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountMonitor::AccountMonitor() : BaseComponent("AccountMonitor")
{
    m_initialBalance = 0.0;
    m_dailyStartBalance = 0.0;
    m_maxEquity = 0.0;
    m_lastResetTime = 0;
    
    m_currentStatus = STATUS_NORMAL;
    m_previousStatus = STATUS_NORMAL;
    m_statusChangeTime = 0;
    m_statusMessage = "";
    
    m_warningThreshold = 50.0;
    m_criticalThreshold = 70.0;
    m_emergencyThreshold = 90.0;

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountMonitor::~AccountMonitor()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize monitor                                               |
//+------------------------------------------------------------------+
bool AccountMonitor::OnInitialize()
{
    m_initialBalance = GetAccountBalance();
    m_dailyStartBalance = m_initialBalance;
    m_maxEquity = GetAccountEquity();
    m_lastResetTime = TimeCurrent();
    
    if (m_initialBalance <= 0.0)
    {
        HandleError(13001, GetErrorDescription(13001));
        return false;
    }
    
    m_currentStatus = STATUS_NORMAL;
    m_statusChangeTime = TimeCurrent();
    m_statusMessage = "Monitor initialized";
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate monitor                                                 |
//+------------------------------------------------------------------+
bool AccountMonitor::OnValidate()
{
    if (m_initialBalance <= 0.0)
    {
        HandleError(13002, GetErrorDescription(13002));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update monitor                                                   |
//+------------------------------------------------------------------+
bool AccountMonitor::OnUpdate()
{
    if (IsNewTradingDay())
    {
        ResetDailyCounters();
    }
    
    UpdateMetrics();
    return true;
}

//+------------------------------------------------------------------+
//| Update metrics                                                   |
//+------------------------------------------------------------------+
void AccountMonitor::UpdateMetrics()
{
    // Update maximum equity
    double currentEquity = GetAccountEquity();
    if (currentEquity > m_maxEquity)
    {
        m_maxEquity = currentEquity;
    }
}

//+------------------------------------------------------------------+
//| Update status                                                    |
//+------------------------------------------------------------------+
void AccountMonitor::UpdateStatus(double maxLossPercent, double maxDrawdownPercent)
{
    ENUM_PROTECTION_STATUS newStatus = CalculateStatus(maxLossPercent, maxDrawdownPercent);
    
    if (newStatus != m_currentStatus)
    {
        m_previousStatus = m_currentStatus;
        m_currentStatus = newStatus;
        m_statusChangeTime = TimeCurrent();
        m_statusMessage = GenerateStatusMessage(newStatus);
        
        Print("Protection status changed to: ", GetStatusDescription());
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void AccountMonitor::ResetDailyCounters()
{
    m_dailyStartBalance = GetAccountBalance();
    m_lastResetTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Check if new trading day                                         |
//+------------------------------------------------------------------+
bool AccountMonitor::IsNewTradingDay() const
{
    datetime currentTime = TimeCurrent();
    return (TimeDay(currentTime) != TimeDay(m_lastResetTime) || 
            TimeMonth(currentTime) != TimeMonth(m_lastResetTime) ||
            TimeYear(currentTime) != TimeYear(m_lastResetTime));
}

//+------------------------------------------------------------------+
//| Get current loss percentage                                      |
//+------------------------------------------------------------------+
double AccountMonitor::GetCurrentLossPercent() const
{
    if (m_initialBalance <= 0.0) return 0.0;
    
    double currentBalance = GetAccountBalance();
    double loss = m_initialBalance - currentBalance;
    return (loss / m_initialBalance) * 100.0;
}

//+------------------------------------------------------------------+
//| Get current drawdown percentage                                  |
//+------------------------------------------------------------------+
double AccountMonitor::GetCurrentDrawdownPercent() const
{
    if (m_maxEquity <= 0.0) return 0.0;
    
    double currentEquity = GetAccountEquity();
    double drawdown = m_maxEquity - currentEquity;
    return (drawdown / m_maxEquity) * 100.0;
}

//+------------------------------------------------------------------+
//| Get daily loss                                                   |
//+------------------------------------------------------------------+
double AccountMonitor::GetDailyLoss() const
{
    double currentBalance = GetAccountBalance();
    double loss = m_dailyStartBalance - currentBalance;
    return MathMax(0.0, loss);
}

//+------------------------------------------------------------------+
//| Get daily profit                                                 |
//+------------------------------------------------------------------+
double AccountMonitor::GetDailyProfit() const
{
    double currentBalance = GetAccountBalance();
    double profit = currentBalance - m_dailyStartBalance;
    return MathMax(0.0, profit);
}

//+------------------------------------------------------------------+
//| Set status manually                                              |
//+------------------------------------------------------------------+
void AccountMonitor::SetStatus(ENUM_PROTECTION_STATUS status, string message = "")
{
    if (status != m_currentStatus)
    {
        m_previousStatus = m_currentStatus;
        m_currentStatus = status;
        m_statusChangeTime = TimeCurrent();
        m_statusMessage = (message != "") ? message : GenerateStatusMessage(status);
    }
}

//+------------------------------------------------------------------+
//| Get status description                                           |
//+------------------------------------------------------------------+
string AccountMonitor::GetStatusDescription() const
{
    switch(m_currentStatus)
    {
        case STATUS_NORMAL:     return "Normal";
        case STATUS_WARNING:    return "Warning";
        case STATUS_CRITICAL:   return "Critical";
        case STATUS_EMERGENCY:  return "Emergency";
        default:                return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Get monitoring summary                                           |
//+------------------------------------------------------------------+
string AccountMonitor::GetMonitoringSummary() const
{
    string summary = StringFormat("Balance: %.2f → %.2f (Loss: %.2f%%)", 
                                 m_initialBalance, GetAccountBalance(), GetCurrentLossPercent());
    summary += StringFormat("\nEquity: %.2f (Max: %.2f, Drawdown: %.2f%%)", 
                           GetAccountEquity(), m_maxEquity, GetCurrentDrawdownPercent());
    summary += StringFormat("\nDaily: Loss=%.2f, Profit=%.2f", GetDailyLoss(), GetDailyProfit());
    summary += "\nStatus: " + GetStatusDescription() + " - " + m_statusMessage;
    
    return summary;
}

//+------------------------------------------------------------------+
//| Calculate status based on thresholds                            |
//+------------------------------------------------------------------+
ENUM_PROTECTION_STATUS AccountMonitor::CalculateStatus(double maxLossPercent, double maxDrawdownPercent)
{
    if (IsLossThresholdExceeded(m_emergencyThreshold, maxLossPercent) || 
        IsDrawdownThresholdExceeded(m_emergencyThreshold, maxDrawdownPercent))
        return STATUS_EMERGENCY;
    
    if (IsLossThresholdExceeded(m_criticalThreshold, maxLossPercent) || 
        IsDrawdownThresholdExceeded(m_criticalThreshold, maxDrawdownPercent))
        return STATUS_CRITICAL;
    
    if (IsLossThresholdExceeded(m_warningThreshold, maxLossPercent) || 
        IsDrawdownThresholdExceeded(m_warningThreshold, maxDrawdownPercent))
        return STATUS_WARNING;
    
    return STATUS_NORMAL;
}

//+------------------------------------------------------------------+
//| Check loss threshold                                             |
//+------------------------------------------------------------------+
bool AccountMonitor::IsLossThresholdExceeded(double threshold, double maxLossPercent)
{
    double currentLossPercent = GetCurrentLossPercent();
    double thresholdValue = maxLossPercent * (threshold / 100.0);
    return (currentLossPercent >= thresholdValue);
}

//+------------------------------------------------------------------+
//| Check drawdown threshold                                         |
//+------------------------------------------------------------------+
bool AccountMonitor::IsDrawdownThresholdExceeded(double threshold, double maxDrawdownPercent)
{
    double currentDrawdownPercent = GetCurrentDrawdownPercent();
    double thresholdValue = maxDrawdownPercent * (threshold / 100.0);
    return (currentDrawdownPercent >= thresholdValue);
}

//+------------------------------------------------------------------+
//| Generate status message                                          |
//+------------------------------------------------------------------+
string AccountMonitor::GenerateStatusMessage(ENUM_PROTECTION_STATUS status)
{
    double lossPercent = GetCurrentLossPercent();
    double drawdownPercent = GetCurrentDrawdownPercent();
    
    switch(status)
    {
        case STATUS_NORMAL:
            return "All metrics within normal range";
        case STATUS_WARNING:
            return StringFormat("Warning: Loss=%.2f%%, Drawdown=%.2f%%", lossPercent, drawdownPercent);
        case STATUS_CRITICAL:
            return StringFormat("Critical: Loss=%.2f%%, Drawdown=%.2f%%", lossPercent, drawdownPercent);
        case STATUS_EMERGENCY:
            return StringFormat("EMERGENCY: Loss=%.2f%%, Drawdown=%.2f%%", lossPercent, drawdownPercent);
        default:
            return "Unknown status";
    }
}

//+------------------------------------------------------------------+
//| Set lockdown error handling                                      |
//+------------------------------------------------------------------+
void AccountMonitor::SetLockDownError(bool lockdown)
{
    BaseComponent::SetLockDownError(lockdown);
    g_lockdownError = lockdown;
}

//+------------------------------------------------------------------+
//| Check if error is locked down                                    |
//+------------------------------------------------------------------+
bool AccountMonitor::IsErrorLockedDown()
{
    if(g_lockdownError && BaseComponent::IsErrorLockedDown()) 
    {
        return true;
    }
    return g_lockdownError;
}

#endif // ACCOUNT_MONITOR_MQH

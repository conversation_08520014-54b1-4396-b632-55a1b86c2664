//+------------------------------------------------------------------+
//|                                    TestMainPipelineEnhancements.mqh |
//|                                                        EA_Wizard |
//|                                  測試 MainPipeline 增強功能      |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../MainPipeline.mqh"
#include "../../TradingPipelineDriver.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體 MainPipeline 實現                                   |
//+------------------------------------------------------------------+
class MockEnhancedMainPipeline : public MainPipeline
{
private:
    bool m_registrationWithDescriptionPassed;
    bool m_detailRetrievalPassed;
    bool m_convenienceMacrosPassed;

public:
    MockEnhancedMainPipeline(string name = "TestEnhancedPipeline")
        : MainPipeline(INIT_START, name, "MockEnhancedMainPipeline"),
          m_registrationWithDescriptionPassed(false),
          m_detailRetrievalPassed(false),
          m_convenienceMacrosPassed(false) {}

    // 獲取測試結果
    bool GetRegistrationWithDescriptionResult() const { return m_registrationWithDescriptionPassed; }
    bool GetDetailRetrievalResult() const { return m_detailRetrievalPassed; }
    bool GetConvenienceMacrosResult() const { return m_convenienceMacrosPassed; }

protected:
    virtual void Main() override
    {
        // 首先註冊測試數據，確保所有測試方法都能訪問
        m_registrationWithDescriptionPassed = TestRegistrationWithDescription();

        // 只有在註冊成功後才進行其他測試
        if(m_registrationWithDescriptionPassed)
        {
            // 測試詳細信息檢索功能
            m_detailRetrievalPassed = TestDetailRetrieval();

            // 測試便利宏功能
            m_convenienceMacrosPassed = TestConvenienceMacros();
        }
        else
        {
            // 如果註冊失敗，其他測試也標記為失敗
            m_detailRetrievalPassed = false;
            m_convenienceMacrosPassed = false;
        }

        bool allPassed = m_registrationWithDescriptionPassed &&
                        m_detailRetrievalPassed &&
                        m_convenienceMacrosPassed;

        SetResult(allPassed, allPassed ? "MainPipeline 增強功能測試完成" : "MainPipeline 增強功能測試失敗");
    }

private:
    // 測試帶描述的註冊功能
    bool TestRegistrationWithDescription()
    {
        // 檢查驅動器是否可用
        if(m_driver == NULL)
        {
            return false;
        }

        // 註冊不同類型的值，帶描述
        bool result1 = Register(100, "test_long", "測試長整型值");
        bool result2 = Register(3.14159, "test_double", "圓周率近似值");
        bool result3 = Register("Hello World", "test_string", "測試字符串");

        return result1 && result2 && result3;
    }
    
    // 測試詳細信息檢索功能
    bool TestDetailRetrieval()
    {
        // 檢查驅動器是否可用
        if(m_driver == NULL)
        {
            return false;
        }

        bool longDetailValid = false;
        bool doubleDetailValid = false;
        bool stringDetailValid = false;

        // 獲取 Long 詳細信息
        RegisteredDetail<long>* longDetail = GetLongDetail("test_long");
        if(longDetail != NULL)
        {
            if(longDetail.IsValid())
            {
                longDetailValid = (longDetail.GetValue() == 100 &&
                                  longDetail.GetDescription() == "測試長整型值");
            }
            delete longDetail;
        }

        // 獲取 Double 詳細信息
        RegisteredDetail<double>* doubleDetail = GetDoubleDetail("test_double");
        if(doubleDetail != NULL)
        {
            if(doubleDetail.IsValid())
            {
                doubleDetailValid = (MathAbs(doubleDetail.GetValue() - 3.14159) < 0.00001 &&
                                    doubleDetail.GetDescription() == "圓周率近似值");
            }
            delete doubleDetail;
        }

        // 獲取 String 詳細信息
        RegisteredDetail<string>* stringDetail = GetStringDetail("test_string");
        if(stringDetail != NULL)
        {
            if(stringDetail.IsValid())
            {
                stringDetailValid = (stringDetail.GetValue() == "Hello World" &&
                                    stringDetail.GetDescription() == "測試字符串");
            }
            delete stringDetail;
        }

        return longDetailValid && doubleDetailValid && stringDetailValid;
    }
    
    // 測試便利宏功能
    bool TestConvenienceMacros()
    {
        // 檢查驅動器是否可用
        if(m_driver == NULL)
        {
            return false;
        }

        // 測試 PipelineDataIsRegistered 宏
        bool longExists = PipelineDataIsRegistered(long, "test_long");
        bool doubleExists = PipelineDataIsRegistered(double, "test_double");
        bool stringExists = PipelineDataIsRegistered(string, "test_string");
        bool nonExistent = PipelineDataIsRegistered(long, "non_existent");

        // 測試 GetPipelineData 宏
        long longValue = GetPipelineData(long, "test_long");
        double doubleValue = GetPipelineData(double, "test_double");
        string stringValue = GetPipelineData(string, "test_string");
        long defaultValue = GetPipelineData(long, "non_existent");

        // 驗證結果
        bool existenceTestsPassed = longExists && doubleExists && stringExists && !nonExistent;
        bool valueTestsPassed = (longValue == 100 &&
                                MathAbs(doubleValue - 3.14159) < 0.00001 &&
                                stringValue == "Hello World" &&
                                defaultValue == 0);

        return existenceTestsPassed && valueTestsPassed;
    }
};

//+------------------------------------------------------------------+
//| MainPipeline 增強功能單元測試類                                  |
//+------------------------------------------------------------------+
class TestMainPipelineEnhancements : public TestCase
{
private:
    TestRunner* m_runner;
    MockEnhancedMainPipeline* m_sharedPipeline;

public:
    // 構造函數
    TestMainPipelineEnhancements(TestRunner* runner = NULL)
        : TestCase("TestMainPipelineEnhancements"), m_runner(runner), m_sharedPipeline(NULL) {}

    // 析構函數
    virtual ~TestMainPipelineEnhancements()
    {
        if(m_sharedPipeline != NULL)
        {
            delete m_sharedPipeline;
            m_sharedPipeline = NULL;
        }
    }

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 MainPipeline 增強功能單元測試 ===");

        // 創建共享的測試管道實例
        m_sharedPipeline = new MockEnhancedMainPipeline("SharedTestPipeline");
        if(m_sharedPipeline == NULL)
        {
            Print("❌ 無法創建共享測試管道");
            return;
        }

        // 執行管道以註冊測試數據
        Print("--- 初始化共享測試管道 ---");
        m_sharedPipeline.Execute();

        // 運行各個測試方法
        TestRegistrationWithDescription();
        TestDetailRetrieval();
        TestConvenienceMacros();
        TestIntegratedWorkflow();

        Print("=== MainPipeline 增強功能單元測試完成 ===");
    }

private:
    // 測試帶描述的註冊功能
    void TestRegistrationWithDescription()
    {
        Print("--- 測試帶描述的註冊功能 ---");

        if(m_sharedPipeline == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipelineEnhancements::TestRegistrationWithDescription - 帶描述註冊",
                    false,
                    "共享管道未初始化"
                ));
            }
            return;
        }

        bool testResult = m_sharedPipeline.GetRegistrationWithDescriptionResult();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipelineEnhancements::TestRegistrationWithDescription - 帶描述註冊",
                testResult,
                testResult ? "帶描述註冊功能測試成功" : "帶描述註冊功能測試失敗"
            ));
        }
    }

    // 測試詳細信息檢索功能
    void TestDetailRetrieval()
    {
        Print("--- 測試詳細信息檢索功能 ---");

        if(m_sharedPipeline == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipelineEnhancements::TestDetailRetrieval - 詳細信息檢索",
                    false,
                    "共享管道未初始化"
                ));
            }
            return;
        }

        bool testResult = m_sharedPipeline.GetDetailRetrievalResult();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipelineEnhancements::TestDetailRetrieval - 詳細信息檢索",
                testResult,
                testResult ? "詳細信息檢索功能測試成功" : "詳細信息檢索功能測試失敗"
            ));
        }
    }

    // 測試便利宏功能
    void TestConvenienceMacros()
    {
        Print("--- 測試便利宏功能 ---");

        if(m_sharedPipeline == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipelineEnhancements::TestConvenienceMacros - 便利宏功能",
                    false,
                    "共享管道未初始化"
                ));
            }
            return;
        }

        bool testResult = m_sharedPipeline.GetConvenienceMacrosResult();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipelineEnhancements::TestConvenienceMacros - 便利宏功能",
                testResult,
                testResult ? "便利宏功能測試成功" : "便利宏功能測試失敗"
            ));
        }
    }

    // 測試整合工作流程
    void TestIntegratedWorkflow()
    {
        Print("--- 測試整合工作流程 ---");

        if(m_sharedPipeline == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipelineEnhancements::TestIntegratedWorkflow - 整合工作流程",
                    false,
                    "共享管道未初始化"
                ));
            }
            return;
        }

        // 檢查各個功能測試結果
        bool registrationPassed = m_sharedPipeline.GetRegistrationWithDescriptionResult();
        bool detailRetrievalPassed = m_sharedPipeline.GetDetailRetrievalResult();
        bool macrosPassed = m_sharedPipeline.GetConvenienceMacrosResult();

        // 整合測試通過條件：所有功能測試都通過
        bool allTestsPassed = registrationPassed && detailRetrievalPassed && macrosPassed;

        if(m_runner != NULL)
        {
            string message = StringFormat("註冊:%s, 詳細信息:%s, 宏:%s",
                                        registrationPassed ? "通過" : "失敗",
                                        detailRetrievalPassed ? "通過" : "失敗",
                                        macrosPassed ? "通過" : "失敗");

            m_runner.RecordResult(new TestResult(
                "TestMainPipelineEnhancements::TestIntegratedWorkflow - 整合工作流程",
                allTestsPassed,
                allTestsPassed ? "整合工作流程測試成功 - " + message : "整合工作流程測試失敗 - " + message
            ));
        }
    }
};

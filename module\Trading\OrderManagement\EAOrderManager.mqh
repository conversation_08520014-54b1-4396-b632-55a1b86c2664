//+------------------------------------------------------------------+
//|                                              EAOrderManager.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef EA_ORDER_MANAGER_MQH
#define EA_ORDER_MANAGER_MQH

#include "../../Base/BaseComponent.mqh"
#include "../../../../mql4_module/mql4-lib/Trade/OrderManager.mqh"
#include "../../../../mql4_module/mql4-lib/Lang/Error.mqh"
#include "Model/TradingParameterModels.mqh"
#include "Model/TradingResultModels.mqh"

//+------------------------------------------------------------------+
//| EAOrderManager Class                                             |
//| Implementation of order execution and management system         |
//+------------------------------------------------------------------+
class EAOrderManager : public BaseComponent
{
private:
    static const BaseErrorDescriptor CODE_ERRORS[]; // Component specific error codes
    static bool                      g_lockdownError;   // Lockdown error handling

    // mql4-lib OrderManager as internal engine (composition pattern)
    OrderManager      m_orderEngine;        // Internal order execution engine
    string            m_symbol;             // Trading symbol

    //--- Override base class methods
protected:
    virtual void      SetLockDownError(bool lockdown = true) override;
    virtual bool      IsErrorLockedDown() override;

public:
    //--- Constructor and Destructor
                      EAOrderManager(string symbol, int magicNumber, int slippage = 3);
    virtual          ~EAOrderManager();

    //--- Configuration methods
    void              SetMagicNumber(int magic);
    void              SetSlippage(int slippage);
    void              SetRetry(int retry);

    //--- Information methods
    string            GetSymbol() const;
    int               GetMagicNumber() const;
    int               GetSlippage() const;
    int               GetRetry() const;
    bool              SymbolIsValid() const;
    bool              MarketTradeAllowed() const;

    //--- Direct delegation methods (mql4-lib compatible)
    // Market orders
    OrderResult*      buy(double lots, double stoploss, double takeprofit, string comment = NULL);
    OrderResult*      sell(double lots, double stoploss, double takeprofit, string comment = NULL);
    OrderResult*      buy(double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL);
    OrderResult*      sell(double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL);
    OrderResult*      buy(OpenInfo* openInfo);
    OrderResult*      sell(OpenInfo* openInfo);

    // Pending orders
    OrderResult*      pendBuy(double price, double lots, double stoploss, double takeprofit, string comment = NULL);
    OrderResult*      pendSell(double price, double lots, double stoploss, double takeprofit, string comment = NULL);
    OrderResult*      pendBuy(double price, double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL);
    OrderResult*      pendSell(double price, double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL);
    OrderResult*      pendBuy(PendInfo* pendInfo);
    OrderResult*      pendSell(PendInfo* pendInfo);

    //--- Order modification methods (direct delegation)
    ModifyResult*     modify(int ticket, double stoploss, double takeprofit);
    ModifyResult*     modify(int ticket, int stoploss, int takeprofit);
    ModifyResult*     modifyPending(int ticket, double price, datetime expiration = 0);
    ModifyResult*     modify(ModifyInfo* modifyInfo);
    ModifyResult*     modifyPending(ModifyInfo* modifyInfo);

    //--- Order closure methods (direct delegation)
    ClosureResult*    close(int ticket);
    ClosureResult*    close(int ticket, double lots);
    ClosureResult*    closeBy(int ticket, int other);

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;

    //--- Trading allowed check (mql4-lib compatible)
    static bool       IsTradeAllowed();

protected:
    //--- Override error description method to handle MQL4 trading errors
    static string     GetErrorDescription(int errorCode);

private:
    //--- Internal methods
    OrderResult*      OperateOrderResult(int currTicket, int currErrorCode);
    ModifyResult*     OperateModifyResult(int ticket, int currErrorCode);
    ClosureResult*    OperateClosureResult(int ticket, int currErrorCode);
};

//+------------------------------------------------------------------+
//| EAOrderManager Error Codes                                      |
//+------------------------------------------------------------------+
const BaseErrorDescriptor EAOrderManager::CODE_ERRORS[] =
{
    // EAOrderManager core errors (10400-10404 range)
    {10400, "No error"},
    {10401, "Invalid symbol for order management"},
    {10402, "Invalid magic number"},
    {10403, "Trading is not allowed"},
    {10404, "Market is closed"},
    {10405, "Invalid slippage value"},
    {10406, "OpenInfo parameter is NULL"},
    {10407, "PendInfo parameter is NULL"},
    {10408, "ModifyInfo parameter is NULL"}
};

bool EAOrderManager::g_lockdownError = false;  // Lockdown error handling

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
EAOrderManager::EAOrderManager(string symbol, int magicNumber, int slippage = 3) :
    BaseComponent("EAOrderManager"),
    m_orderEngine(symbol)
{
    // Configure mql4-lib OrderManager instance
    m_symbol = symbol;
    m_orderEngine.setMagic(magicNumber);
    m_orderEngine.setSlippage(slippage);
    m_orderEngine.setRetry(3); // Default retry count

    // Initialize error codes
    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
EAOrderManager::~EAOrderManager()
{
    // OrderManager instance cleanup handled automatically
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize order manager                                         |
//+------------------------------------------------------------------+
bool EAOrderManager::OnInitialize()
{
    // Basic validation - OrderManager handles symbol internally
    if (!SymbolIsValid())
    {
        HandleError(10401, GetErrorDescription(10401));
        return false;
    }

    if (m_orderEngine.getMagic() <= 0)
    {
        HandleError(10402, GetErrorDescription(10402));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool EAOrderManager::OnValidate()
{
    // Basic validation without validator components
    // Check if trading is allowed
    if (!IsTradeAllowed())
    {
        HandleError(10403, GetErrorDescription(10403));
        return false;
    }

    // Check market conditions
    if (!MarketTradeAllowed())
    {
        HandleError(10404, GetErrorDescription(10404));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Configuration methods implementation                            |
//+------------------------------------------------------------------+
void EAOrderManager::SetMagicNumber(int magic)
{
    m_orderEngine.setMagic(magic);
}

void EAOrderManager::SetSlippage(int slippage)
{
    m_orderEngine.setSlippage(MathMax(0, slippage));
}

void EAOrderManager::SetRetry(int retry)
{
    m_orderEngine.setRetry(MathMax(1, retry));
}

//+------------------------------------------------------------------+
//| Information methods implementation                              |
//+------------------------------------------------------------------+
string EAOrderManager::GetSymbol() const
{
    return Symbol(); // Return current symbol since OrderManager uses it internally
}

int EAOrderManager::GetMagicNumber() const
{
    return m_orderEngine.getMagic();
}

int EAOrderManager::GetSlippage() const
{
    return m_orderEngine.getSlippage();
}

int EAOrderManager::GetRetry() const
{
    return m_orderEngine.getRetry();
}

bool EAOrderManager::SymbolIsValid() const
{
    return SymbolInfoInteger(m_symbol, SYMBOL_SELECT) != 0;
}

bool EAOrderManager::MarketTradeAllowed() const
{
    return MarketInfo(m_symbol, MODE_TRADEALLOWED) != 0;
}

//+------------------------------------------------------------------+
//| Direct delegation methods implementation                         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Buy order with double SL/TP (direct delegation)                 |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::buy(double lots, double stoploss, double takeprofit, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.buy(lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Sell order with double SL/TP (direct delegation)                |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::sell(double lots, double stoploss, double takeprofit, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.sell(lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Buy order with int SL/TP in points (direct delegation)          |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::buy(double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.buy(lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Sell order with int SL/TP in points (direct delegation)         |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::sell(double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.sell(lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Pending buy order with double SL/TP (direct delegation)         |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendBuy(double price, double lots, double stoploss, double takeprofit, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.pendBuy(price, lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Pending sell order with double SL/TP (direct delegation)        |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendSell(double price, double lots, double stoploss, double takeprofit, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.pendSell(price, lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Pending buy order with int SL/TP in points (direct delegation)  |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendBuy(double price, double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.pendBuy(price, lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Pending sell order with int SL/TP in points (direct delegation) |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendSell(double price, double lots, int stoploss = 0, int takeprofit = 0, string comment = NULL)
{
    // Basic validation
    if (!Validate())
    {
        int lastError = GetLastError();
        string errorMsg = GetLastErrorMessage();
        return new OrderResult(false, -1, 0.0, 0.0, errorMsg, lastError);
    }

    // Direct delegation to mql4-lib OrderManager
    int ticket = m_orderEngine.pendSell(price, lots, stoploss, takeprofit, comment);

    // Create OrderResult based on operation success
    return OperateOrderResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Order modification methods (direct delegation)                  |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Modify order with double SL/TP (direct delegation)              |
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::modify(int ticket, double stoploss, double takeprofit)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.modify(ticket, stoploss, takeprofit);

    // Create ModifyResult based on operation success
    return OperateModifyResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Modify order with int SL/TP in points (direct delegation)       |
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::modify(int ticket, int stoploss, int takeprofit)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.modify(ticket, stoploss, takeprofit);

    // Create ModifyResult based on operation success
    return OperateModifyResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Modify pending order price (direct delegation)                  |
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::modifyPending(int ticket, double price, datetime expiration = 0)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.modifyPending(ticket, price, expiration);

    // Create ModifyResult based on operation success
    return OperateModifyResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Model Class Based Trading Methods                               |
//| Methods that accept model classes as parameters                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Buy order using OpenInfo model (direct delegation)             |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::buy(OpenInfo* openInfo)
{
    // Validate input parameter
    if (openInfo == NULL)
    {
        HandleError(10406, GetErrorDescription(10406));
        return new OrderResult(false, -1, 0.0, 0.0, GetErrorDescription(10406),10406);
    }

    // Delegate to existing buy method with extracted parameters
    return buy(openInfo.GetLots(), openInfo.GetStopLoss(), openInfo.GetTakeProfit(), openInfo.GetComment());
}

//+------------------------------------------------------------------+
//| Sell order using OpenInfo model (direct delegation)            |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::sell(OpenInfo* openInfo)
{
    // Validate input parameter
    if (openInfo == NULL)
    {
        HandleError(10406, GetErrorDescription(10406));
        return new OrderResult(false, -1, 0.0, 0.0, GetErrorDescription(10406),10406);
    }

    // Delegate to existing sell method with extracted parameters
    return sell(openInfo.GetLots(), openInfo.GetStopLoss(), openInfo.GetTakeProfit(), openInfo.GetComment());
}

//+------------------------------------------------------------------+
//| Pending buy order using PendInfo model (direct delegation)     |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendBuy(PendInfo* pendInfo)
{
    // Validate input parameter
    if (pendInfo == NULL)
    {
        HandleError(10407, GetErrorDescription(10407));
        return new OrderResult(false, -1, 0.0, 0.0, GetErrorDescription(10407),10407);
    }

    // Delegate to existing pendBuy method with extracted parameters
    return pendBuy(pendInfo.GetPrice(), pendInfo.GetLots(), pendInfo.GetStopLoss(),
                   pendInfo.GetTakeProfit(), pendInfo.GetComment());
}

//+------------------------------------------------------------------+
//| Pending sell order using PendInfo model (direct delegation)    |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::pendSell(PendInfo* pendInfo)
{
    // Validate input parameter
    if (pendInfo == NULL)
    {
        HandleError(10407, GetErrorDescription(10407));
        return new OrderResult(false, -1, 0.0, 0.0, GetErrorDescription(10407),10407);
    }

    // Delegate to existing pendSell method with extracted parameters
    return pendSell(pendInfo.GetPrice(), pendInfo.GetLots(), pendInfo.GetStopLoss(),
                    pendInfo.GetTakeProfit(), pendInfo.GetComment());
}

//+------------------------------------------------------------------+
//| Modify order using ModifyInfo model (direct delegation)        |
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::modify(ModifyInfo* modifyInfo)
{
    // Validate input parameter
    if (modifyInfo == NULL)
    {
        HandleError(10408, GetErrorDescription(10408));
        return new ModifyResult(false, -1, 0.0, 0.0, 0.0, GetErrorDescription(10408),10408);
    }

    // Delegate to existing modify method with extracted parameters
    return modify(modifyInfo.GetTicket(), modifyInfo.GetStopLoss(), modifyInfo.GetTakeProfit());
}

//+------------------------------------------------------------------+
//| Modify pending order using ModifyInfo model (direct delegation)|
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::modifyPending(ModifyInfo* modifyInfo)
{
    // Validate input parameter
    if (modifyInfo == NULL)
    {
        HandleError(10408, GetErrorDescription(10408));
        return new ModifyResult(false, -1, 0.0, 0.0, 0.0, GetErrorDescription(10408),10408);
    }

    // Delegate to existing modifyPending method with extracted parameters
    return modifyPending(modifyInfo.GetTicket(), modifyInfo.GetPrice(), modifyInfo.GetExpiration());
}

//+------------------------------------------------------------------+
//| Order closure methods (direct delegation)                       |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Close order by ticket (direct delegation)                       |
//+------------------------------------------------------------------+
ClosureResult* EAOrderManager::close(int ticket)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.close(ticket);

    // Create ClosureResult based on operation success
    return OperateClosureResult(ticket, m_orderEngine.getLastError());
}

//+------------------------------------------------------------------+
//| Close order partially by ticket and lots (direct delegation)    |
//+------------------------------------------------------------------+
ClosureResult* EAOrderManager::close(int ticket, double lots)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.close(ticket, lots);

    // Create ClosureResult based on operation success
    return OperateClosureResult(ticket, m_orderEngine.getLastError());
}



//+------------------------------------------------------------------+
//| Close by opposite order (direct delegation)                     |
//+------------------------------------------------------------------+
ClosureResult* EAOrderManager::closeBy(int ticket, int other)
{
    // Direct delegation to mql4-lib OrderManager
    bool result = m_orderEngine.closeBy(ticket, other);

    // Create ClosureResult based on operation success
    return OperateClosureResult(ticket, m_orderEngine.getLastError());
}


//+------------------------------------------------------------------+
//| mql4-lib compatible error handling                              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if trading is allowed (static method)                     |
//+------------------------------------------------------------------+
bool EAOrderManager::IsTradeAllowed()
{
    return (::IsTradeAllowed() && !IsTradeContextBusy() && IsConnected());
}

//+------------------------------------------------------------------+
//| Get error description (override to handle MQL4 trading errors) |
//+------------------------------------------------------------------+
string EAOrderManager::GetErrorDescription(int errorCode)
{
    // For MQL4 trading error codes (0-150), use mql4-lib error system
    if (errorCode >= 0 && errorCode <= 150)
    {
        return ::GetErrorDescription(errorCode);
    }

    // For framework error codes (10400-10404), use BaseComponent error system
    return BaseComponent::GetErrorDescription(errorCode);
}

//+------------------------------------------------------------------+
//| Create OrderResult based on operation success                    |
//+------------------------------------------------------------------+
OrderResult* EAOrderManager::OperateOrderResult(int currTicket, int currErrorCode)
{
    OrderResult* result = NULL;

    int ticket = currTicket;
    int errorCode = currErrorCode;
    bool success = false;
    double openPrice = 0.0;
    double lots = 0.0;
    string errorMessage = "";

    if (ticket > 0)
    {
        // Get order information for successful order
        if (OrderSelect(ticket, SELECT_BY_TICKET))
        {
            success = true;
            openPrice = OrderOpenPrice();
            lots = OrderLots();
            errorMessage = GetErrorDescription(errorCode);
        }
        else
        {
            // Get error information for failed order
            success = false;
            openPrice = 0.0;
            lots = 0.0;
            errorCode = GetLastError();
            errorMessage = GetErrorDescription(errorCode);
        }
    }
    else
    {
        // Get error information for failed order
        success = false;
        openPrice = 0.0;
        lots = 0.0;
        errorMessage = GetErrorDescription(errorCode);
    }

    return new OrderResult(success, ticket, openPrice, lots, errorMessage, errorCode);
}



//+------------------------------------------------------------------+
//| Create ModifyResult for modification operations                 |
//+------------------------------------------------------------------+
ModifyResult* EAOrderManager::OperateModifyResult(int currTicket, int currErrorCode)
{
    string errorMessage = "";
    bool success = false;
    double stoploss = 0.0;
    double takeprofit = 0.0;
    double price = 0.0;

    // Determine success based on error code
    if (currErrorCode == 0)
    {
        success = true;
        errorMessage = "";

        // Try to get current order information if successful
        if (OrderSelect(currTicket, SELECT_BY_TICKET))
        {
            stoploss = OrderStopLoss();
            takeprofit = OrderTakeProfit();
            price = OrderOpenPrice();
        }
    }
    else
    {
        success = false;
        errorMessage = GetErrorDescription(currErrorCode);
    }

    return new ModifyResult(success, currTicket, stoploss, takeprofit, price, errorMessage, currErrorCode);
}

//+------------------------------------------------------------------+
//| Create ClosureResult for closure operations                     |
//+------------------------------------------------------------------+
ClosureResult* EAOrderManager::OperateClosureResult(int ticket, int currErrorCode)
{
    string errorMessage = "";
    bool success = false;
    double closedLots = 0.0;
    double closurePrice = 0.0;
    double profit = 0.0;
    datetime closureTime = 0;

    // Determine success based on error code
    if (currErrorCode == 0)
    {
        success = true;
        errorMessage = "";

        // Try to get closure information if successful
        // Note: For closed orders, we need to check order history
        if (OrderSelect(ticket, SELECT_BY_TICKET, MODE_HISTORY))
        {
            closedLots = OrderLots();
            closurePrice = OrderClosePrice();
            profit = OrderProfit() + OrderSwap() + OrderCommission();
            closureTime = OrderCloseTime();
        }
        else if (OrderSelect(ticket, SELECT_BY_TICKET, MODE_TRADES))
        {
            // Order might still be open (partial closure case)
            closedLots = OrderLots();
            closurePrice = OrderClosePrice();
            profit = OrderProfit() + OrderSwap() + OrderCommission();
            closureTime = TimeCurrent();
        }
    }
    else
    {
        success = false;
        errorMessage = GetErrorDescription(currErrorCode);
    }

    return new ClosureResult(success, ticket, closedLots, closurePrice, profit, closureTime, errorMessage, currErrorCode);
}

//+------------------------------------------------------------------+
//| Set lockdown error handling                                      |
//+------------------------------------------------------------------+
void EAOrderManager::SetLockDownError(bool lockdown)
{
    BaseComponent::SetLockDownError(lockdown);
    g_lockdownError = lockdown;
}

//+------------------------------------------------------------------+
//| Check if error handling is locked down                           |
//+------------------------------------------------------------------+
bool EAOrderManager::IsErrorLockedDown()
{
    if(g_lockdownError && BaseComponent::IsErrorLockedDown()) 
    {
        return true;
    }
    return false;
}

#endif // EA_ORDER_MANAGER_MQH

//+------------------------------------------------------------------+
//|                            TestTradingPipelineContainerManager.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "TestTradingPipeline.mqh"  // 使用 MockTradingPipeline

//+------------------------------------------------------------------+
//| TradingPipelineContainerManager 單元測試類                       |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerManager : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineContainerManager(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineContainerManager"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineContainerManager() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineContainerManager 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestContainerManagement();
        TestExecuteFlow();
        TestRestoreFunction();
        TestStatusMethods();

        Print("=== TradingPipelineContainerManager 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineContainerManager 構造函數 ---");

        // 測試默認構造函數
        TradingPipelineContainerManager* manager1 = new TradingPipelineContainerManager();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestConstructor - 默認構造函數",
                manager1 != NULL,
                manager1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        TradingPipelineContainerManager* manager2 = new TradingPipelineContainerManager(
            "TestManager", "TestType", false, 5);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestConstructor - 帶參數構造函數",
                manager2 != NULL && manager2.GetName() == "TestManager",
                manager2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        delete manager1;
        delete manager2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineContainerManager 基本屬性 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "PropTest", "TestType", false, 3);

        // 測試 GetName
        string name = manager.GetName();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestBasicProperties - GetName",
                name == "PropTest",
                name == "PropTest" ? "名稱正確" : "名稱錯誤: " + name
            ));
        }

        // 測試 GetType
        string type = manager.GetType();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestBasicProperties - GetType",
                type == "TestType",
                type == "TestType" ? "類型正確" : "類型錯誤: " + type
            ));
        }

        // 測試初始狀態 - 修正：移除了 manager.IsExecuted() 調用
        bool enabled = manager.IsEnabled();
        int count = manager.GetContainerCount();
        int maxCount = manager.GetMaxContainers();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestBasicProperties - 初始狀態",
                enabled && count == 0 && maxCount == 3,
                "初始狀態正確"
            ));
        }

        delete manager;
    }

    // 測試容器管理
    void TestContainerManagement()
    {
        Print("--- 測試 TradingPipelineContainerManager 容器管理 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "ManageTest", "Test", false, 3);

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("Container1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("Container2");

        // 測試設置容器
        bool set1 = manager.SetContainer(TRADING_TICK, container1);
        bool set2 = manager.SetContainer(TRADING_DEINIT, container2);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestContainerManagement - 設置容器",
                set1 && set2 && manager.GetContainerCount() == 2,
                "容器設置成功"
            ));
        }

        // 測試設置空容器
        bool setNull = manager.SetContainer(TRADING_INIT, NULL);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestContainerManagement - 設置空容器",
                !setNull,
                "正確拒絕空容器"
            ));
        }

        // 測試獲取容器
        TradingPipelineContainer* retrieved = manager.GetContainer(TRADING_TICK);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestContainerManagement - 獲取容器",
                retrieved == container1,
                "容器獲取正確"
            ));
        }

        // 測試移除容器（按容器指針）
        bool removed = manager.RemoveContainer(container1);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestContainerManagement - 移除容器",
                removed && manager.GetContainerCount() == 1,
                "容器移除成功"
            ));
        }

        delete manager;
        delete container1;
        delete container2;
    }

    // 測試執行流程
    void TestExecuteFlow()
    {
        Print("--- 測試 TradingPipelineContainerManager 執行流程 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("ExecuteTest");

        // 創建容器和流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("ExecContainer");
        MockTradingPipeline* pipeline = new MockTradingPipeline("ExecPipeline");

        container.AddPipeline(pipeline);
        manager.SetContainer(TRADING_TICK, container);

        // 測試按事件執行
        manager.Execute(TRADING_TICK);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestExecuteFlow - 按事件執行",
                container.IsExecuted() && pipeline.IsExecuted(),
                "按事件執行成功"
            ));
        }

        // 重置並測試執行不同事件
        container.Restore();
        pipeline.Restore();

        // 添加另一個容器來測試多事件執行
        TradingPipelineContainer* container2 = new TradingPipelineContainer("ExecContainer2");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("ExecPipeline2");
        container2.AddPipeline(pipeline2);
        manager.SetContainer(TRADING_DEINIT, container2);

        // 分別執行不同事件
        manager.Execute(TRADING_TICK);
        manager.Execute(TRADING_DEINIT);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestExecuteFlow - 執行多個事件",
                container.IsExecuted() && pipeline.IsExecuted() &&
                container2.IsExecuted() && pipeline2.IsExecuted(),
                "執行多個事件容器成功"
            ));
        }

        delete container2;
        delete pipeline2;

        delete manager;
        delete container;
        delete pipeline;
    }

    // 測試重置功能
    void TestRestoreFunction()
    {
        Print("--- 測試 TradingPipelineContainerManager 重置功能 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("RestoreTest");

        // 創建容器和流水線
        TradingPipelineContainer* container1 = new TradingPipelineContainer("RestoreContainer1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("RestoreContainer2");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("RestorePipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("RestorePipeline2");

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);
        manager.SetContainer(TRADING_TICK, container1);
        manager.SetContainer(TRADING_DEINIT, container2);

        // 先執行所有容器
        manager.Execute(TRADING_TICK);
        manager.Execute(TRADING_DEINIT);
        bool container1ExecutedBefore = container1.IsExecuted();
        bool pipeline1ExecutedBefore = pipeline1.IsExecuted();

        // 測試按事件重置
        manager.Restore(TRADING_TICK);
        bool container1ExecutedAfter = container1.IsExecuted();
        bool container2ExecutedAfter = container2.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestRestoreFunction - 按事件重置",
                container1ExecutedBefore && !container1ExecutedAfter && container2ExecutedAfter,
                "按事件重置成功"
            ));
        }

        // 測試重置所有容器（分別重置每個事件）
        manager.Restore(TRADING_TICK);
        manager.Restore(TRADING_DEINIT);
        bool allRestored = !container1.IsExecuted() && !container2.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestRestoreFunction - 重置所有",
                allRestored,
                "重置所有容器成功"
            ));
        }

        delete manager;
        delete container1;
        delete container2;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試狀態方法
    void TestStatusMethods()
    {
        Print("--- 測試 TradingPipelineContainerManager 狀態方法 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("StatusTest", "Test", false, 2);

        // 測試啟用/禁用
        manager.SetEnabled(false);
        bool disabledState = manager.IsEnabled();

        manager.SetEnabled(true);
        bool enabledState = manager.IsEnabled();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestStatusMethods - SetEnabled/IsEnabled",
                !disabledState && enabledState,
                "啟用/禁用功能正確"
            ));
        }

        // 測試清空
        TradingPipelineContainer* container = new TradingPipelineContainer("ClearTest");
        manager.SetContainer(TRADING_TICK, container);

        int countBefore = manager.GetContainerCount();
        manager.Clear();
        int countAfter = manager.GetContainerCount();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerManager::TestStatusMethods - Clear",
                countBefore == 1 && countAfter == 0,
                "清空功能正確"
            ));
        }

        delete manager;
        delete container;
    }
};

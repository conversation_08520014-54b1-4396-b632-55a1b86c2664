//+------------------------------------------------------------------+
//| StandardEA.mqh                                                   |
//| EA_Wizard-dev2 Main Expert Advisor File                         |
//| Copyright 2024, EA_Wizard Framework                              |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, EA_Wizard Framework"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| Main Expert Advisor File                                         |
//| This file serves as the main entry point for the EA_Wizard-dev2 |
//| Expert Advisor, implementing the standardized EA_Wizard         |
//| framework architecture                                           |
//+------------------------------------------------------------------+

// Include EA_Wizard framework components
#include "../../mql4_module/EA_Wizard/TradingController.mqh"
#include "../../mql4_module/EA_Wizard/MainPipeline.mqh"

// Include project modules
#include "Config/index.mqh"
#include "OnInit/index.mqh"
#include "OnTick/index.mqh"
#include "OnDeinit/index.mqh"

TradingPipelineDriver* pipeline_driver = TradingPipelineDriver::GetInstance();
TradingController controller(pipeline_driver);

int OnInit()
{
   // 初始化代碼
   ENUM_INIT_RETCODE result = controller.OnInit();

   return(result);
}

void OnDeinit(const int reason)
{
   // 執行清理代碼
   controller.OnDeinit(reason);
}

void OnTick()
{
   // 執行 EA 處理器
   controller.OnTick();
}

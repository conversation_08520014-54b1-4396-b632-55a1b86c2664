//+------------------------------------------------------------------+
//|                                      AccountProtectionConfig.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_CONFIG_MQH
#define ACCOUNT_PROTECTION_CONFIG_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"

//+------------------------------------------------------------------+
//| AccountProtectionConfig Class                                    |
//| Responsible for managing account protection configuration        |
//| Single Responsibility: Configuration Management                  |
//|                                                                  |
//| IMMUTABLE PATTERN:                                               |
//| - All configuration must be set during construction             |
//| - No setter methods available after object creation             |
//| - Configuration cannot be modified during object lifetime       |
//| - Use appropriate constructor for desired configuration          |
//|                                                                  |
//| Follows EA_Wizard framework patterns with BaseComponent         |
//| inheritance and CODE_ERRORS enumeration support                 |
//+------------------------------------------------------------------+
class AccountProtectionConfig : public BaseComponent
{
private:
    static const BaseErrorDescriptor CODE_ERRORS[]; // Component specific error codes
    static bool                      g_lockdownError;   // Lockdown error handling

    ENUM_PROTECTION_LEVEL   m_protectionLevel;     // Protection level

    // Account limits
    double                  m_maxLossPercent;       // Maximum loss percentage
    double                  m_maxDailyLossPercent;  // Maximum daily loss percentage
    double                  m_maxDrawdownPercent;   // Maximum drawdown percentage
    double                  m_minEquityPercent;     // Minimum equity percentage

    // Position limits
    int                     m_maxOpenOrders;        // Maximum open orders
    double                  m_maxLotSize;           // Maximum lot size per trade
    double                  m_maxTotalLotSize;      // Maximum total lot size
    double                  m_maxSpread;            // Maximum allowed spread

protected:
    //--- Override base class methods
    virtual void            SetLockDownError(bool lockdown = true) override;
    virtual bool            IsErrorLockedDown() override;

public:
    //--- Constructor and Destructor
                            AccountProtectionConfig(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE);
                            AccountProtectionConfig(double maxLossPercent,
                                                  double maxDrawdownPercent,
                                                  double maxDailyLossPercent,
                                                  int maxOpenOrders,
                                                  double maxLotSize,
                                                  double maxSpread,
                                                  double maxTotalLotSize);
    virtual                ~AccountProtectionConfig();

    //--- Information methods
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const { return m_protectionLevel; }
    double                  GetMaxLossPercent() const { return m_maxLossPercent; }
    double                  GetMaxDailyLossPercent() const { return m_maxDailyLossPercent; }
    double                  GetMaxDrawdownPercent() const { return m_maxDrawdownPercent; }
    double                  GetMinEquityPercent() const { return m_minEquityPercent; }
    int                     GetMaxOpenOrders() const { return m_maxOpenOrders; }
    double                  GetMaxLotSize() const { return m_maxLotSize; }
    double                  GetMaxTotalLotSize() const { return m_maxTotalLotSize; }
    double                  GetMaxSpread() const { return m_maxSpread; }

    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;

    //--- Utility methods
    void                    LoadDefaultsForLevel(ENUM_PROTECTION_LEVEL level);
    string                  GetConfigSummary() const;
};

const BaseErrorDescriptor AccountProtectionConfig::CODE_ERRORS[] =   // Component specific error codes
{
    // Account protection specific errors
    {10600, "Invalid maximum loss percentage"},
    {10601, "Invalid maximum drawdown percentage"},
    {10602, "Invalid maximum open orders count"},
    {10603, "Invalid maximum lot size"},
    {10604, "Invalid maximum total lot size"},
    {10605, "Invalid maximum spread"},
    {10606, "Total lot size cannot be less than max lot size"},
    {10607, "Daily loss percentage cannot be greater than max loss percentage"},
    {10608, "Invalid minimum equity percentage"},
    {10609, "Invalid maximum daily loss percentage"}
};

bool AccountProtectionConfig::g_lockdownError = false;  // Lockdown error handling

//+------------------------------------------------------------------+
//| Constructor with protection level                                |
//+------------------------------------------------------------------+
AccountProtectionConfig::AccountProtectionConfig(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE)
    : BaseComponent("AccountProtectionConfig")
{
    m_protectionLevel = level;
    LoadDefaultsForLevel(level);

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Constructor with custom parameters                               |
//+------------------------------------------------------------------+
AccountProtectionConfig::AccountProtectionConfig(double maxLossPercent,
                                                double maxDrawdownPercent,
                                                double maxDailyLossPercent,
                                                int maxOpenOrders,
                                                double maxLotSize,
                                                double maxSpread,
                                                double maxTotalLotSize)
    : BaseComponent("AccountProtectionConfig")
{
    // Set protection level as custom
    m_protectionLevel = PROTECTION_CUSTOM;

    m_maxLossPercent = maxLossPercent;
    m_maxDrawdownPercent = maxDrawdownPercent;
    m_maxDailyLossPercent = maxDailyLossPercent;
    m_maxOpenOrders = maxOpenOrders;
    m_maxLotSize = maxLotSize;
    m_maxSpread = maxSpread;
    m_maxTotalLotSize = maxTotalLotSize;

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtectionConfig::~AccountProtectionConfig()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize configuration                                         |
//+------------------------------------------------------------------+
bool AccountProtectionConfig::OnInitialize()
{
    if (m_maxLossPercent <= 0.0)
    {
        HandleError(10600, GetErrorDescription(10600));
        return false;
    }

    if (m_maxDrawdownPercent <= 0.0)
    {
        HandleError(10601, GetErrorDescription(10601));
        return false;
    }

    if (m_maxDailyLossPercent <= 0.0)
    {
        HandleError(10609, GetErrorDescription(10609));
        return false;
    }

    if (m_maxOpenOrders <= 0)
    {
        HandleError(10602, GetErrorDescription(10602));
        return false;
    }

    if (m_maxLotSize <= 0.0)
    {
        HandleError(10603, GetErrorDescription(10603));
        return false;
    }

    if (m_maxTotalLotSize <= 0.0)
    {
        HandleError(10604, GetErrorDescription(10604));
        return false;
    }

    // Cross-validation: check parameter relationships
    if (m_maxTotalLotSize < m_maxLotSize)
    {
        HandleError(10606, GetErrorDescription(10606));
        return false;
    }
    if (m_maxDailyLossPercent > m_maxLossPercent)
    {
        HandleError(10607, GetErrorDescription(10607));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate configuration parameters                                |
//+------------------------------------------------------------------+
bool AccountProtectionConfig::OnValidate()
{
    // Immutable data container - no runtime validation needed
    return true;
}

//+------------------------------------------------------------------+
//| Load default values for protection level                        |
//+------------------------------------------------------------------+
void AccountProtectionConfig::LoadDefaultsForLevel(ENUM_PROTECTION_LEVEL level)
{
    switch(level)
    {
        case PROTECTION_NONE:
            m_maxLossPercent = 50.0;
            m_maxDrawdownPercent = 80.0;
            m_maxOpenOrders = 100;
            m_maxLotSize = 10.0;
            m_maxTotalLotSize = 50.0;
            m_maxSpread = 20.0;
            break;

        case PROTECTION_BASIC:
            m_maxLossPercent = 30.0;
            m_maxDrawdownPercent = 50.0;
            m_maxOpenOrders = 50;
            m_maxLotSize = 5.0;
            m_maxTotalLotSize = 20.0;
            m_maxSpread = 10.0;
            break;

        case PROTECTION_MODERATE:
            m_maxLossPercent = 20.0;
            m_maxDrawdownPercent = 30.0;
            m_maxOpenOrders = 20;
            m_maxLotSize = 2.0;
            m_maxTotalLotSize = 10.0;
            m_maxSpread = 5.0;
            break;

        case PROTECTION_STRICT:
            m_maxLossPercent = 10.0;
            m_maxDrawdownPercent = 15.0;
            m_maxOpenOrders = 10;
            m_maxLotSize = 1.0;
            m_maxTotalLotSize = 5.0;
            m_maxSpread = 3.0;
            break;

        case PROTECTION_CUSTOM:
            // Custom protection - don't override existing values
            return;
    }

    m_minEquityPercent = 100.0 - m_maxLossPercent;
    // Daily loss is typically half of max loss for conservative approach
    m_maxDailyLossPercent = m_maxLossPercent / 2.0; // Will be set based on account balance
}

//+------------------------------------------------------------------+
//| Get configuration summary                                        |
//+------------------------------------------------------------------+
string AccountProtectionConfig::GetConfigSummary() const
{
    string summary = "Protection Level: ";

    switch(m_protectionLevel)
    {
        case PROTECTION_NONE:     summary += "NONE"; break;
        case PROTECTION_BASIC:    summary += "BASIC"; break;
        case PROTECTION_MODERATE: summary += "MODERATE"; break;
        case PROTECTION_STRICT:   summary += "STRICT"; break;
        case PROTECTION_CUSTOM:   summary += "CUSTOM"; break;
    }

    summary += StringFormat("\nMax Loss: %.1f%%, Max Drawdown: %.1f%%, Max Daily Loss: %.1f%%",
                           m_maxLossPercent, m_maxDrawdownPercent, m_maxDailyLossPercent);
    summary += StringFormat("\nMax Orders: %d, Max Lot: %.2f, Max Total Lot: %.2f",
                           m_maxOpenOrders, m_maxLotSize, m_maxTotalLotSize);
    summary += StringFormat("\nMax Spread: %.1f points", m_maxSpread);

    return summary;
}

//+------------------------------------------------------------------+
//| Set lockdown error handling                                      |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetLockDownError(bool lockdown)
{
    BaseComponent::SetLockDownError(lockdown);
    g_lockdownError = lockdown;
}

//+------------------------------------------------------------------+
//| Check if error handling is locked down                           |
//+------------------------------------------------------------------+
bool AccountProtectionConfig::IsErrorLockedDown()
{
    if(g_lockdownError && BaseComponent::IsErrorLockedDown())
    {
        return true;
    }
    return false;
}


#endif // ACCOUNT_PROTECTION_CONFIG_MQH

//+------------------------------------------------------------------+
//|                                                       Logger.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef LOGGER_MQH
#define LOGGER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Log Level Enumeration                                            |
//+------------------------------------------------------------------+
enum ENUM_LOG_LEVEL
{
    LOG_LEVEL_TRACE = 0,    // Trace level (most verbose)
    LOG_LEVEL_DEBUG = 1,    // Debug level
    LOG_LEVEL_INFO = 2,     // Information level
    LOG_LEVEL_WARN = 3,     // Warning level
    LOG_LEVEL_ERROR = 4,    // Error level
    LOG_LEVEL_FATAL = 5     // Fatal level (least verbose)
};

//+------------------------------------------------------------------+
//| Log Entry Structure                                              |
//+------------------------------------------------------------------+
struct LogEntry
{
    datetime          timestamp;        // Log timestamp
    ENUM_LOG_LEVEL    level;           // Log level
    string            category;        // Log category/module
    string            message;         // Log message
    string            details;         // Additional details
};

//+------------------------------------------------------------------+
//| Logger Class                                                     |
//| Implementation of comprehensive logging system                  |
//+------------------------------------------------------------------+
class Logger : public BaseComponent
{
private:
    ENUM_LOG_LEVEL    m_logLevel;          // Current log level
    bool              m_enableFileLog;     // Enable file logging
    bool              m_enableConsoleLog;  // Enable console logging
    bool              m_enableAlertLog;    // Enable alert logging
    string            m_logFileName;       // Log file name
    string            m_logDirectory;      // Log directory
    int               m_maxFileSize;       // Maximum file size (KB)
    int               m_maxFiles;          // Maximum number of log files
    bool              m_includeTimestamp;  // Include timestamp in logs
    bool              m_includeLevel;      // Include level in logs
    bool              m_includeCategory;   // Include category in logs
    
    // Buffering
    LogEntry          m_logBuffer[];       // Log entry buffer
    int               m_bufferSize;        // Buffer size
    int               m_bufferIndex;       // Current buffer index
    bool              m_autoFlush;         // Auto flush buffer
    int               m_flushInterval;     // Flush interval (seconds)
    datetime          m_lastFlush;         // Last flush time

public:
    //--- Constructor and Destructor
                      Logger(string name = "Logger", ENUM_LOG_LEVEL level = LOG_LEVEL_INFO);
    virtual          ~Logger();
    
    //--- Configuration methods
    void              SetLogLevel(ENUM_LOG_LEVEL level) { m_logLevel = level; }
    void              SetEnableFileLog(bool enable) { m_enableFileLog = enable; }
    void              SetEnableConsoleLog(bool enable) { m_enableConsoleLog = enable; }
    void              SetEnableAlertLog(bool enable) { m_enableAlertLog = enable; }
    void              SetLogFileName(string fileName) { m_logFileName = fileName; }
    void              SetLogDirectory(string directory) { m_logDirectory = directory; }
    void              SetMaxFileSize(int sizeKB) { m_maxFileSize = MathMax(100, sizeKB); }
    void              SetMaxFiles(int count) { m_maxFiles = MathMax(1, count); }
    void              SetBufferSize(int size);
    void              SetAutoFlush(bool enable, int intervalSeconds = 60);
    
    //--- Information methods
    ENUM_LOG_LEVEL    GetLogLevel() const { return m_logLevel; }
    bool              IsFileLogEnabled() const { return m_enableFileLog; }
    bool              IsConsoleLogEnabled() const { return m_enableConsoleLog; }
    string            GetLogFileName() const { return m_logFileName; }
    int               GetBufferCount() const { return m_bufferIndex; }
    
    //--- Logging methods
    void              Trace(string category, string message, string details = "");
    void              Debug(string category, string message, string details = "");
    void              Info(string category, string message, string details = "");
    void              Warn(string category, string message, string details = "");
    void              Error(string category, string message, string details = "");
    void              Fatal(string category, string message, string details = "");
    void              Log(ENUM_LOG_LEVEL level, string category, string message, string details = "");
    
    //--- Buffer management
    void              FlushBuffer();
    void              ClearBuffer();
    bool              IsBufferFull() const;
    
    //--- File management
    bool              RotateLogFile();
    bool              DeleteOldLogFiles();
    string            GetCurrentLogFilePath();
    long              GetLogFileSize();
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;
    virtual void      OnCleanup() override;
    
    //--- Utility methods
    string            LogLevelToString(ENUM_LOG_LEVEL level);
    string            FormatLogEntry(const LogEntry& entry);
    string            GetTimestampString(datetime time);
    bool              ShouldLog(ENUM_LOG_LEVEL level) const;
    
private:
    //--- Internal methods
    void              WriteToFile(const LogEntry& entry);
    void              WriteToConsole(const LogEntry& entry);
    void              WriteToAlert(const LogEntry& entry);
    void              AddToBuffer(const LogEntry& entry);
    bool              CreateLogDirectory();
    string            GenerateLogFileName();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
Logger::Logger(string name = "Logger", ENUM_LOG_LEVEL level = LOG_LEVEL_INFO) : BaseComponent(name)
{
    m_logLevel = level;
    m_enableFileLog = true;
    m_enableConsoleLog = true;
    m_enableAlertLog = false;
    m_logFileName = "";
    m_logDirectory = "Logs";
    m_maxFileSize = 1024; // 1MB
    m_maxFiles = 10;
    m_includeTimestamp = true;
    m_includeLevel = true;
    m_includeCategory = true;
    
    m_bufferSize = 100;
    m_bufferIndex = 0;
    m_autoFlush = true;
    m_flushInterval = 60; // 60 seconds
    m_lastFlush = TimeCurrent();
    
    ArrayResize(m_logBuffer, m_bufferSize);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
Logger::~Logger()
{
    FlushBuffer();
    ArrayFree(m_logBuffer);
}

//+------------------------------------------------------------------+
//| Initialize logger                                                |
//+------------------------------------------------------------------+
bool Logger::OnInitialize()
{
    if (m_logFileName == "")
    {
        m_logFileName = GenerateLogFileName();
    }
    
    if (m_enableFileLog)
    {
        if (!CreateLogDirectory())
        {
            SetError(1401, "Failed to create log directory");
            return false;
        }
    }
    
    Info("Logger", "Logger initialized", "Level: " + LogLevelToString(m_logLevel));
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate logger parameters                                       |
//+------------------------------------------------------------------+
bool Logger::OnValidate()
{
    if (m_bufferSize <= 0)
    {
        SetError(1402, "Invalid buffer size");
        return false;
    }
    
    if (m_maxFileSize <= 0)
    {
        SetError(1403, "Invalid maximum file size");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update logger                                                    |
//+------------------------------------------------------------------+
bool Logger::OnUpdate()
{
    if (m_autoFlush && TimeCurrent() - m_lastFlush >= m_flushInterval)
    {
        FlushBuffer();
    }
    
    // Check if log file needs rotation
    if (m_enableFileLog && GetLogFileSize() > m_maxFileSize * 1024)
    {
        RotateLogFile();
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Cleanup logger                                                   |
//+------------------------------------------------------------------+
void Logger::OnCleanup()
{
    FlushBuffer();
    Info("Logger", "Logger cleanup completed");
}

//+------------------------------------------------------------------+
//| Set buffer size                                                  |
//+------------------------------------------------------------------+
void Logger::SetBufferSize(int size)
{
    if (size > 0)
    {
        FlushBuffer(); // Flush current buffer
        m_bufferSize = size;
        ArrayResize(m_logBuffer, m_bufferSize);
        m_bufferIndex = 0;
    }
}

//+------------------------------------------------------------------+
//| Set auto flush                                                   |
//+------------------------------------------------------------------+
void Logger::SetAutoFlush(bool enable, int intervalSeconds = 60)
{
    m_autoFlush = enable;
    m_flushInterval = MathMax(1, intervalSeconds);
}

//+------------------------------------------------------------------+
//| Trace level logging                                              |
//+------------------------------------------------------------------+
void Logger::Trace(string category, string message, string details = "")
{
    Log(LOG_LEVEL_TRACE, category, message, details);
}

//+------------------------------------------------------------------+
//| Debug level logging                                              |
//+------------------------------------------------------------------+
void Logger::Debug(string category, string message, string details = "")
{
    Log(LOG_LEVEL_DEBUG, category, message, details);
}

//+------------------------------------------------------------------+
//| Info level logging                                               |
//+------------------------------------------------------------------+
void Logger::Info(string category, string message, string details = "")
{
    Log(LOG_LEVEL_INFO, category, message, details);
}

//+------------------------------------------------------------------+
//| Warning level logging                                            |
//+------------------------------------------------------------------+
void Logger::Warn(string category, string message, string details = "")
{
    Log(LOG_LEVEL_WARN, category, message, details);
}

//+------------------------------------------------------------------+
//| Error level logging                                              |
//+------------------------------------------------------------------+
void Logger::Error(string category, string message, string details = "")
{
    Log(LOG_LEVEL_ERROR, category, message, details);
}

//+------------------------------------------------------------------+
//| Fatal level logging                                              |
//+------------------------------------------------------------------+
void Logger::Fatal(string category, string message, string details = "")
{
    Log(LOG_LEVEL_FATAL, category, message, details);
}

//+------------------------------------------------------------------+
//| Generic logging method                                           |
//+------------------------------------------------------------------+
void Logger::Log(ENUM_LOG_LEVEL level, string category, string message, string details = "")
{
    if (!ShouldLog(level))
        return;
    
    LogEntry entry;
    entry.timestamp = TimeCurrent();
    entry.level = level;
    entry.category = category;
    entry.message = message;
    entry.details = details;
    
    // Write to outputs
    if (m_enableConsoleLog)
        WriteToConsole(entry);
    
    if (m_enableAlertLog && level >= LOG_LEVEL_ERROR)
        WriteToAlert(entry);
    
    if (m_enableFileLog)
    {
        if (m_bufferSize > 1)
            AddToBuffer(entry);
        else
            WriteToFile(entry);
    }
}

//+------------------------------------------------------------------+
//| Flush buffer to file                                             |
//+------------------------------------------------------------------+
void Logger::FlushBuffer()
{
    if (!m_enableFileLog || m_bufferIndex == 0)
        return;
    
    for (int i = 0; i < m_bufferIndex; i++)
    {
        WriteToFile(m_logBuffer[i]);
    }
    
    m_bufferIndex = 0;
    m_lastFlush = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Clear buffer                                                     |
//+------------------------------------------------------------------+
void Logger::ClearBuffer()
{
    m_bufferIndex = 0;
}

//+------------------------------------------------------------------+
//| Check if buffer is full                                          |
//+------------------------------------------------------------------+
bool Logger::IsBufferFull() const
{
    return (m_bufferIndex >= m_bufferSize);
}

//+------------------------------------------------------------------+
//| Rotate log file                                                  |
//+------------------------------------------------------------------+
bool Logger::RotateLogFile()
{
    FlushBuffer();
    
    string currentFile = GetCurrentLogFilePath();
    string backupFile = StringSubstr(currentFile, 0, StringLen(currentFile) - 4) + "_" + 
                       TimeToString(TimeCurrent(), TIME_DATE) + ".log";
    
    // Move current file to backup
    if (FileIsExist(currentFile))
    {
        // Note: MQL4 doesn't have file rename, so we'll create a new file
        // The old file will be overwritten on next write
    }
    
    DeleteOldLogFiles();
    
    return true;
}

//+------------------------------------------------------------------+
//| Delete old log files                                             |
//+------------------------------------------------------------------+
bool Logger::DeleteOldLogFiles()
{
    // Note: MQL4 has limited file system operations
    // This is a placeholder for file cleanup logic
    return true;
}

//+------------------------------------------------------------------+
//| Get current log file path                                        |
//+------------------------------------------------------------------+
string Logger::GetCurrentLogFilePath()
{
    return m_logDirectory + "\\" + m_logFileName;
}

//+------------------------------------------------------------------+
//| Get log file size                                                |
//+------------------------------------------------------------------+
long Logger::GetLogFileSize()
{
    string filePath = GetCurrentLogFilePath();
    
    int handle = FileOpen(filePath, FILE_READ | FILE_BIN);
    if (handle != INVALID_HANDLE)
    {
        long size = FileSize(handle);
        FileClose(handle);
        return size;
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| Convert log level to string                                      |
//+------------------------------------------------------------------+
string Logger::LogLevelToString(ENUM_LOG_LEVEL level)
{
    switch(level)
    {
        case LOG_LEVEL_TRACE: return "TRACE";
        case LOG_LEVEL_DEBUG: return "DEBUG";
        case LOG_LEVEL_INFO:  return "INFO";
        case LOG_LEVEL_WARN:  return "WARN";
        case LOG_LEVEL_ERROR: return "ERROR";
        case LOG_LEVEL_FATAL: return "FATAL";
        default:              return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Format log entry                                                 |
//+------------------------------------------------------------------+
string Logger::FormatLogEntry(const LogEntry& entry)
{
    string formatted = "";
    
    if (m_includeTimestamp)
        formatted += "[" + GetTimestampString(entry.timestamp) + "] ";
    
    if (m_includeLevel)
        formatted += "[" + LogLevelToString(entry.level) + "] ";
    
    if (m_includeCategory)
        formatted += "[" + entry.category + "] ";
    
    formatted += entry.message;
    
    if (entry.details != "")
        formatted += " | " + entry.details;
    
    return formatted;
}

//+------------------------------------------------------------------+
//| Get timestamp string                                             |
//+------------------------------------------------------------------+
string Logger::GetTimestampString(datetime time)
{
    return TimeToString(time, TIME_DATE | TIME_SECONDS);
}

//+------------------------------------------------------------------+
//| Check if should log at level                                     |
//+------------------------------------------------------------------+
bool Logger::ShouldLog(ENUM_LOG_LEVEL level) const
{
    return (level >= m_logLevel);
}

//+------------------------------------------------------------------+
//| Write to file                                                    |
//+------------------------------------------------------------------+
void Logger::WriteToFile(const LogEntry& entry)
{
    string filePath = GetCurrentLogFilePath();
    
    int handle = FileOpen(filePath, FILE_WRITE | FILE_TXT);
    if (handle != INVALID_HANDLE)
    {
        FileSeek(handle, 0, SEEK_END);
        FileWrite(handle, FormatLogEntry(entry));
        FileClose(handle);
    }
}

//+------------------------------------------------------------------+
//| Write to console                                                 |
//+------------------------------------------------------------------+
void Logger::WriteToConsole(const LogEntry& entry)
{
    Print(FormatLogEntry(entry));
}

//+------------------------------------------------------------------+
//| Write to alert                                                   |
//+------------------------------------------------------------------+
void Logger::WriteToAlert(const LogEntry& entry)
{
    Alert(FormatLogEntry(entry));
}

//+------------------------------------------------------------------+
//| Add to buffer                                                    |
//+------------------------------------------------------------------+
void Logger::AddToBuffer(const LogEntry& entry)
{
    if (m_bufferIndex < m_bufferSize)
    {
        m_logBuffer[m_bufferIndex] = entry;
        m_bufferIndex++;
    }
    
    if (IsBufferFull())
    {
        FlushBuffer();
    }
}

//+------------------------------------------------------------------+
//| Create log directory                                             |
//+------------------------------------------------------------------+
bool Logger::CreateLogDirectory()
{
    // Note: MQL4 has limited directory operations
    // Directories are typically created automatically when files are written
    return true;
}

//+------------------------------------------------------------------+
//| Generate log file name                                           |
//+------------------------------------------------------------------+
string Logger::GenerateLogFileName()
{
    return "EA_Wizard_" + TimeToString(TimeCurrent(), TIME_DATE) + ".log";
}

#endif // LOGGER_MQH

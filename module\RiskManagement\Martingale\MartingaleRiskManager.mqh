//+------------------------------------------------------------------+
//|                                        MartingaleRiskManager.mqh |
//|                                      獨立馬丁格爾風險管理模組     |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_RISK_MANAGER_MQH
#define MARTINGALE_RISK_MANAGER_MQH

#property strict

//+------------------------------------------------------------------+
//| 馬丁格爾風險管理器枚舉定義                                        |
//+------------------------------------------------------------------+
enum ENUM_MARTINGALE_ACTION
{
    MARTINGALE_CLOSE_IMMEDIATELY,    // 到達上限立即平倉 (建議)
    MARTINGALE_WAIT_FOR_BREAKEVEN    // 等待回本 (高風險)
};

//+------------------------------------------------------------------+
//| 馬丁格爾層級管理器類別                                            |
//| 負責加倉層級管理和計算 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleLevelManager
{
private:
    // 層級管理參數
    int                     m_currentLevel;         // 當前加倉層級
    int                     m_maxLevels;            // 最大加倉層級
    double                  m_initialLot;           // 初始手數
    int                     m_gridStepPoints;       // 加倉間隔點數
    double                  m_lotMultiplier;        // 手數乘數
    double                  m_lastEntryPrice;       // 最後進場價格

public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleLevelManager();

    //+------------------------------------------------------------------+
    //| 參數配置方法                                                     |
    //+------------------------------------------------------------------+
    bool                    SetMaxLevels(int levels);
    bool                    SetInitialLot(double lot);
    bool                    SetGridStepPoints(int points);
    bool                    SetLotMultiplier(double multiplier);

    //+------------------------------------------------------------------+
    //| 層級管理方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CanAddNextLevel();
    double                  CalculateNextLevelPrice(int direction);
    double                  CalculateNextLotSize();
    bool                    IsGridStepReached(double currentPrice);
    void                    AddLevel(double entryPrice);
    void                    ResetLevel();

    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    int                     GetCurrentLevel() const { return m_currentLevel; }
    int                     GetMaxLevels() const { return m_maxLevels; }
    double                  GetLastEntryPrice() const { return m_lastEntryPrice; }
    bool                    IsMaxLevelReached() const { return m_currentLevel >= m_maxLevels; }

private:
    //+------------------------------------------------------------------+
    //| 輔助方法                                                         |
    //+------------------------------------------------------------------+
    double                  PointsToPrice(int points);
    bool                    ValidateParameters();
};

//+------------------------------------------------------------------+
//| 馬丁格爾淨值保護器類別                                            |
//| 負責帳戶淨值保護機制 (單一責任原則)                               |
//+------------------------------------------------------------------+
class MartingaleEquityProtector
{
private:
    // 淨值保護參數
    double                  m_initialEquity;        // 初始帳戶淨值
    double                  m_equityStopPercent;    // 淨值保護百分比
    double                  m_currentDrawdownPercent; // 當前回撤百分比
    double                  m_maxDrawdownPercent;   // 最大回撤百分比

public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleEquityProtector();

    //+------------------------------------------------------------------+
    //| 初始化和配置方法                                                 |
    //+------------------------------------------------------------------+
    bool                    Initialize();
    bool                    SetEquityStopPercent(double percent);

    //+------------------------------------------------------------------+
    //| 保護檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckEquityProtection();
    void                    UpdateDrawdownTracking();

    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    double                  GetCurrentDrawdownPercent() const { return m_currentDrawdownPercent; }
    double                  GetMaxDrawdownPercent() const { return m_maxDrawdownPercent; }
    double                  GetEquityStopPercent() const { return m_equityStopPercent; }
    double                  GetInitialEquity() const { return m_initialEquity; }

    //+------------------------------------------------------------------+
    //| 重置方法                                                         |
    //+------------------------------------------------------------------+
    void                    Reset();
};

//+------------------------------------------------------------------+
//| 馬丁格爾熔斷器類別                                                |
//| 負責熔斷機制和緊急停止 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleCircuitBreaker
{
private:
    // 熔斷機制參數
    bool                    m_emergencyStop;        // 緊急停止狀態
    ENUM_MARTINGALE_ACTION  m_actionOnMaxLevel;     // 到達上限後的操作

public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleCircuitBreaker();

    //+------------------------------------------------------------------+
    //| 配置方法                                                         |
    //+------------------------------------------------------------------+
    void                    SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action);

    //+------------------------------------------------------------------+
    //| 熔斷檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckEmergencyStop() const { return m_emergencyStop; }
    void                    ActivateEmergencyStop();
    bool                    ShouldExecuteCircuitBreaker(bool maxLevelReached, bool equityProtectionTriggered);

    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    bool                    IsEmergencyStop() const { return m_emergencyStop; }
    ENUM_MARTINGALE_ACTION  GetActionOnMaxLevel() const { return m_actionOnMaxLevel; }

    //+------------------------------------------------------------------+
    //| 重置方法                                                         |
    //+------------------------------------------------------------------+
    void                    Reset();
};

//+------------------------------------------------------------------+
//| 馬丁格爾盈利追蹤器類別                                            |
//| 負責盈利目標監控和計算 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleProfitTracker
{
private:
    // 盈利追蹤參數
    double                  m_overallProfitTarget;  // 整體盈利目標

public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleProfitTracker();

    //+------------------------------------------------------------------+
    //| 配置方法                                                         |
    //+------------------------------------------------------------------+
    bool                    SetOverallProfitTarget(double target);

    //+------------------------------------------------------------------+
    //| 盈利檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckProfitTarget();
    double                  CalculateTotalProfit();
    double                  CalculateBreakEvenPrice();

    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    double                  GetOverallProfitTarget() const { return m_overallProfitTarget; }
};

//+------------------------------------------------------------------+
//| 馬丁格爾風險協調器類別                                            |
//| 協調各個子模組，提供統一介面 (協調器模式)                         |
//+------------------------------------------------------------------+
class MartingaleRiskCoordinator
{
private:
    // 子模組指標
    MartingaleLevelManager*     m_levelManager;
    MartingaleEquityProtector*  m_equityProtector;
    MartingaleCircuitBreaker*   m_circuitBreaker;
    MartingaleProfitTracker*    m_profitTracker;

    // 序列狀態
    bool                        m_sequenceActive;
    datetime                    m_lastUpdateTime;

public:
    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+
                                MartingaleRiskCoordinator();
                               ~MartingaleRiskCoordinator();

    //+------------------------------------------------------------------+
    //| 初始化和配置方法                                                 |
    //+------------------------------------------------------------------+
    bool                        Initialize();
    bool                        Update();
//+------------------------------------------------------------------+
//|                                           MartingaleStrategy.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_STRATEGY_MQH
#define MARTINGALE_STRATEGY_MQH

#include "../../Base/BaseStrategy.mqh"

//+------------------------------------------------------------------+
//| MartingaleStrategy Class                                         |
//| Implementation of 4-level Martingale position scaling system    |
//+------------------------------------------------------------------+
class MartingaleStrategy : public BaseStrategy
{
private:
    // Martingale configuration
    double            m_lotSequence[4];       // Fixed lot sequence [0.01, 0.01, 0.02, 0.04]
    double            m_levelDistance;        // Distance between levels (300 points)
    double            m_profitTarget;         // Profit target per 0.01 lot (300 points)
    int               m_maxLevels;            // Maximum Martingale levels
    int               m_currentLevel;         // Current Martingale level
    
    // Position tracking
    double            m_firstEntryPrice;      // First entry price
    double            m_totalLotSize;         // Total lot size across all levels
    double            m_totalCost;            // Total cost of all positions
    bool              m_martingaleActive;     // Martingale sequence active
    
    // Level management
    double            m_levelPrices[4];       // Entry prices for each level
    bool              m_levelFilled[4];       // Level filled status
    datetime          m_levelTimes[4];        // Entry times for each level

public:
    //--- Constructor and Destructor
                      MartingaleStrategy(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                        int magicNumber = 12345);
    virtual          ~MartingaleStrategy();
    
    //--- Configuration methods
    void              SetLevelDistance(double distance) { m_levelDistance = MathMax(100.0, distance); }
    void              SetProfitTarget(double target) { m_profitTarget = MathMax(100.0, target); }
    void              SetMaxLevels(int levels) { m_maxLevels = MathMax(1, MathMin(4, levels)); }
    
    //--- Information methods
    double            GetLevelDistance() const { return m_levelDistance; }
    double            GetProfitTarget() const { return m_profitTarget; }
    int               GetMaxLevels() const { return m_maxLevels; }
    int               GetCurrentLevel() const { return m_currentLevel; }
    bool              IsMartingaleActive() const { return m_martingaleActive; }
    double            GetTotalLotSize() const { return m_totalLotSize; }
    
    //--- Martingale logic methods
    bool              ShouldOpenNextLevel(double currentPrice);
    double            GetNextLevelPrice(ENUM_TRADE_DIRECTION direction);
    double            GetNextLevelLotSize();
    double            CalculateTotalProfit();
    double            CalculateBreakEvenPrice();
    bool              ShouldCloseAllPositions(double currentPrice);
    
    //--- Position management
    void              StartMartingaleSequence(ENUM_TRADE_DIRECTION direction, double entryPrice);
    void              AddLevel(double entryPrice, double lotSize);
    void              ResetMartingaleSequence();
    bool              IsLevelTriggered(int level, double currentPrice, ENUM_TRADE_DIRECTION direction);
    
    //--- Override base class methods
    virtual bool      AnalyzeMarket() override;
    virtual TradeSignal GenerateSignal() override;
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual void      OnReset() override;
    
    //--- Utility methods
    double            PointsToPrice(double points);
    double            PriceToPoints(double priceDistance);
    void              UpdatePositionTracking();
    string            GetMartingaleStatus();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
MartingaleStrategy::MartingaleStrategy(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                      int magicNumber = 12345) : BaseStrategy("MartingaleStrategy", symbol, timeframe, magicNumber)
{
    // Initialize fixed lot sequence
    m_lotSequence[0] = 0.01;
    m_lotSequence[1] = 0.01;
    m_lotSequence[2] = 0.02;
    m_lotSequence[3] = 0.04;
    
    m_levelDistance = 300.0;      // 300 points between levels
    m_profitTarget = 300.0;       // 300 points profit per 0.01 lot
    m_maxLevels = 4;              // Maximum 4 levels
    m_currentLevel = 0;
    
    m_firstEntryPrice = 0.0;
    m_totalLotSize = 0.0;
    m_totalCost = 0.0;
    m_martingaleActive = false;
    
    // Initialize arrays
    ArrayInitialize(m_levelPrices, 0.0);
    ArrayInitialize(m_levelFilled, false);
    ArrayInitialize(m_levelTimes, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
MartingaleStrategy::~MartingaleStrategy()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize Martingale strategy                                   |
//+------------------------------------------------------------------+
bool MartingaleStrategy::OnInitialize()
{
    if (!BaseStrategy::OnInitialize())
        return false;
    
    if (m_levelDistance <= 0.0)
    {
        SetError(1001, "Invalid level distance");
        return false;
    }
    
    if (m_profitTarget <= 0.0)
    {
        SetError(1002, "Invalid profit target");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate strategy parameters                                     |
//+------------------------------------------------------------------+
bool MartingaleStrategy::OnValidate()
{
    if (!BaseStrategy::OnValidate())
        return false;
    
    // Validate lot sequence
    for (int i = 0; i < m_maxLevels; i++)
    {
        if (m_lotSequence[i] <= 0.0)
        {
            SetError(1003, "Invalid lot size in sequence");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Reset strategy                                                   |
//+------------------------------------------------------------------+
void MartingaleStrategy::OnReset()
{
    BaseStrategy::OnReset();
    ResetMartingaleSequence();
}

//+------------------------------------------------------------------+
//| Analyze market conditions                                        |
//+------------------------------------------------------------------+
bool MartingaleStrategy::AnalyzeMarket()
{
    UpdatePositionTracking();
    
    // If Martingale is active, check for level triggers or profit targets
    if (m_martingaleActive)
    {
        double currentPrice = (GetSymbol() == Symbol()) ? Bid : MarketInfo(GetSymbol(), MODE_BID);
        
        // Check if we should close all positions (profit target reached)
        if (ShouldCloseAllPositions(currentPrice))
        {
            SetState(STRATEGY_SIGNALED);
            return true;
        }
        
        // Check if next level should be opened
        if (ShouldOpenNextLevel(currentPrice))
        {
            SetState(STRATEGY_SIGNALED);
            return true;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Generate trade signal                                            |
//+------------------------------------------------------------------+
TradeSignal MartingaleStrategy::GenerateSignal()
{
    TradeSignal signal;
    signal.direction = TRADE_NONE;
    signal.entryPrice = 0.0;
    signal.stopLoss = 0.0;
    signal.takeProfit = 0.0;
    signal.lotSize = 0.0;
    signal.confidence = 0.0;
    signal.timestamp = TimeCurrent();
    signal.comment = "";
    signal.magicNumber = GetMagicNumber();
    
    double currentPrice = (GetSymbol() == Symbol()) ? Bid : MarketInfo(GetSymbol(), MODE_BID);
    
    if (m_martingaleActive)
    {
        // Check for profit target
        if (ShouldCloseAllPositions(currentPrice))
        {
            signal.direction = TRADE_NONE; // Special signal to close all positions
            signal.confidence = 1.0;
            signal.comment = "Martingale Profit Target Reached";
            return signal;
        }
        
        // Check for next level
        if (ShouldOpenNextLevel(currentPrice))
        {
            ENUM_TRADE_DIRECTION direction = (m_firstEntryPrice > currentPrice) ? TRADE_BUY : TRADE_SELL;
            
            signal.direction = direction;
            signal.entryPrice = currentPrice;
            signal.lotSize = GetNextLevelLotSize();
            signal.stopLoss = 0.0; // No stop loss in Martingale
            signal.takeProfit = CalculateBreakEvenPrice();
            signal.confidence = 0.8;
            signal.comment = "Martingale Level " + IntegerToString(m_currentLevel + 1);
        }
    }
    else
    {
        // Martingale not active - this would be handled by external signal system
        // This strategy focuses on Martingale management, not initial signal generation
    }
    
    return signal;
}

//+------------------------------------------------------------------+
//| Check if next level should be opened                            |
//+------------------------------------------------------------------+
bool MartingaleStrategy::ShouldOpenNextLevel(double currentPrice)
{
    if (!m_martingaleActive || m_currentLevel >= m_maxLevels)
        return false;
    
    ENUM_TRADE_DIRECTION direction = (m_firstEntryPrice > currentPrice) ? TRADE_BUY : TRADE_SELL;
    
    return IsLevelTriggered(m_currentLevel, currentPrice, direction);
}

//+------------------------------------------------------------------+
//| Get next level entry price                                       |
//+------------------------------------------------------------------+
double MartingaleStrategy::GetNextLevelPrice(ENUM_TRADE_DIRECTION direction)
{
    if (m_currentLevel >= m_maxLevels)
        return 0.0;
    
    double levelDistance = PointsToPrice(m_levelDistance);
    
    if (direction == TRADE_BUY)
    {
        return m_firstEntryPrice - (levelDistance * (m_currentLevel + 1));
    }
    else if (direction == TRADE_SELL)
    {
        return m_firstEntryPrice + (levelDistance * (m_currentLevel + 1));
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Get next level lot size                                          |
//+------------------------------------------------------------------+
double MartingaleStrategy::GetNextLevelLotSize()
{
    if (m_currentLevel >= m_maxLevels)
        return 0.0;
    
    return m_lotSequence[m_currentLevel];
}

//+------------------------------------------------------------------+
//| Calculate total profit of all positions                         |
//+------------------------------------------------------------------+
double MartingaleStrategy::CalculateTotalProfit()
{
    double totalProfit = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderMagicNumber() == GetMagicNumber() && OrderSymbol() == GetSymbol())
            {
                totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            }
        }
    }
    
    return totalProfit;
}

//+------------------------------------------------------------------+
//| Calculate break-even price                                       |
//+------------------------------------------------------------------+
double MartingaleStrategy::CalculateBreakEvenPrice()
{
    if (m_totalLotSize <= 0.0)
        return 0.0;
    
    return m_totalCost / m_totalLotSize;
}

//+------------------------------------------------------------------+
//| Check if all positions should be closed                         |
//+------------------------------------------------------------------+
bool MartingaleStrategy::ShouldCloseAllPositions(double currentPrice)
{
    if (!m_martingaleActive)
        return false;
    
    double totalProfit = CalculateTotalProfit();
    double targetProfit = m_totalLotSize * PointsToPrice(m_profitTarget) * 100; // Adjust for lot size
    
    return (totalProfit >= targetProfit);
}

//+------------------------------------------------------------------+
//| Start Martingale sequence                                        |
//+------------------------------------------------------------------+
void MartingaleStrategy::StartMartingaleSequence(ENUM_TRADE_DIRECTION direction, double entryPrice)
{
    ResetMartingaleSequence();
    
    m_martingaleActive = true;
    m_firstEntryPrice = entryPrice;
    m_currentLevel = 0;
    
    AddLevel(entryPrice, m_lotSequence[0]);
}

//+------------------------------------------------------------------+
//| Add level to Martingale sequence                                 |
//+------------------------------------------------------------------+
void MartingaleStrategy::AddLevel(double entryPrice, double lotSize)
{
    if (m_currentLevel >= m_maxLevels)
        return;
    
    m_levelPrices[m_currentLevel] = entryPrice;
    m_levelFilled[m_currentLevel] = true;
    m_levelTimes[m_currentLevel] = TimeCurrent();
    
    m_totalLotSize += lotSize;
    m_totalCost += entryPrice * lotSize;
    
    m_currentLevel++;
}

//+------------------------------------------------------------------+
//| Reset Martingale sequence                                        |
//+------------------------------------------------------------------+
void MartingaleStrategy::ResetMartingaleSequence()
{
    m_martingaleActive = false;
    m_currentLevel = 0;
    m_firstEntryPrice = 0.0;
    m_totalLotSize = 0.0;
    m_totalCost = 0.0;
    
    ArrayInitialize(m_levelPrices, 0.0);
    ArrayInitialize(m_levelFilled, false);
    ArrayInitialize(m_levelTimes, 0);
}

//+------------------------------------------------------------------+
//| Check if level is triggered                                      |
//+------------------------------------------------------------------+
bool MartingaleStrategy::IsLevelTriggered(int level, double currentPrice, ENUM_TRADE_DIRECTION direction)
{
    if (level >= m_maxLevels || m_levelFilled[level])
        return false;
    
    double triggerPrice = GetNextLevelPrice(direction);
    
    if (direction == TRADE_BUY)
    {
        return (currentPrice <= triggerPrice);
    }
    else if (direction == TRADE_SELL)
    {
        return (currentPrice >= triggerPrice);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Convert points to price                                          |
//+------------------------------------------------------------------+
double MartingaleStrategy::PointsToPrice(double points)
{
    double point = MarketInfo(GetSymbol(), MODE_POINT);
    return points * point;
}

//+------------------------------------------------------------------+
//| Convert price distance to points                                |
//+------------------------------------------------------------------+
double MartingaleStrategy::PriceToPoints(double priceDistance)
{
    double point = MarketInfo(GetSymbol(), MODE_POINT);
    return (point > 0.0) ? priceDistance / point : 0.0;
}

//+------------------------------------------------------------------+
//| Update position tracking                                         |
//+------------------------------------------------------------------+
void MartingaleStrategy::UpdatePositionTracking()
{
    // Count current positions with our magic number
    int positionCount = 0;
    double totalLots = 0.0;
    double totalCost = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderMagicNumber() == GetMagicNumber() && OrderSymbol() == GetSymbol())
            {
                positionCount++;
                totalLots += OrderLots();
                totalCost += OrderOpenPrice() * OrderLots();
            }
        }
    }
    
    // If no positions, reset Martingale
    if (positionCount == 0 && m_martingaleActive)
    {
        ResetMartingaleSequence();
    }
    
    m_totalLotSize = totalLots;
    m_totalCost = totalCost;
}

//+------------------------------------------------------------------+
//| Get Martingale status description                                |
//+------------------------------------------------------------------+
string MartingaleStrategy::GetMartingaleStatus()
{
    if (!m_martingaleActive)
        return "Inactive";
    
    return "Level " + IntegerToString(m_currentLevel) + "/" + IntegerToString(m_maxLevels) + 
           " | Lots: " + DoubleToString(m_totalLotSize, 2) + 
           " | Profit: " + DoubleToString(CalculateTotalProfit(), 2);
}

#endif // MARTINGALE_STRATEGY_MQH

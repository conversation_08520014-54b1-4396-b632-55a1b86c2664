//+------------------------------------------------------------------+
//|                                        MartingaleProfitTracker.mqh |
//|                                      馬丁格爾盈利追蹤器類別        |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_PROFIT_TRACKER_MQH
#define MARTINGALE_PROFIT_TRACKER_MQH

#property strict

//+------------------------------------------------------------------+
//| 馬丁格爾盈利追蹤器類別                                            |
//| 負責盈利目標監控和計算 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleProfitTracker
{
private:
    // 盈利追蹤參數
    double                  m_overallProfitTarget;  // 整體盈利目標
    
public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleProfitTracker();
    
    //+------------------------------------------------------------------+
    //| 配置方法                                                         |
    //+------------------------------------------------------------------+
    bool                    SetOverallProfitTarget(double target);
    
    //+------------------------------------------------------------------+
    //| 盈利檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckProfitTarget();
    double                  CalculateTotalProfit();
    double                  CalculateBreakEvenPrice();
    
    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    double                  GetOverallProfitTarget() const { return m_overallProfitTarget; }
    
    //+------------------------------------------------------------------+
    //| 統計方法                                                         |
    //+------------------------------------------------------------------+
    int                     GetTotalPositions();
    double                  GetTotalLotSize();
    string                  GetProfitSummary();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleProfitTracker::MartingaleProfitTracker()
{
    m_overallProfitTarget = 10.0;       // 預設$10盈利目標
    
    Print("[MartingaleProfitTracker] 盈利追蹤器初始化完成 - 預設目標: $", DoubleToString(m_overallProfitTarget, 2));
}

//+------------------------------------------------------------------+
//| 設定整體盈利目標                                                 |
//+------------------------------------------------------------------+
bool MartingaleProfitTracker::SetOverallProfitTarget(double target)
{
    if (target <= 0.0)
    {
        Print("[MartingaleProfitTracker] 錯誤: 盈利目標必須大於0，當前值: ", DoubleToString(target, 2));
        return false;
    }
    m_overallProfitTarget = target;
    Print("[MartingaleProfitTracker] 盈利目標設定為: $", DoubleToString(target, 2));
    return true;
}

//+------------------------------------------------------------------+
//| 檢查盈利目標                                                     |
//+------------------------------------------------------------------+
bool MartingaleProfitTracker::CheckProfitTarget()
{
    double totalProfit = CalculateTotalProfit();
    bool targetReached = (totalProfit >= m_overallProfitTarget);
    
    if (targetReached)
    {
        Print("[MartingaleProfitTracker] *** 盈利目標已達成 ***");
        Print("  當前盈利: $", DoubleToString(totalProfit, 2));
        Print("  目標盈利: $", DoubleToString(m_overallProfitTarget, 2));
        Print("  超額盈利: $", DoubleToString(totalProfit - m_overallProfitTarget, 2));
    }
    
    return targetReached;
}

//+------------------------------------------------------------------+
//| 計算總盈利                                                       |
//+------------------------------------------------------------------+
double MartingaleProfitTracker::CalculateTotalProfit()
{
    double totalProfit = 0.0;
    int positionCount = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == Symbol())
            {
                double orderProfit = OrderProfit() + OrderSwap() + OrderCommission();
                totalProfit += orderProfit;
                positionCount++;
            }
        }
    }
    
    // 記錄詳細資訊（僅在有持倉時）
    if (positionCount > 0)
    {
        static datetime lastLogTime = 0;
        datetime currentTime = TimeCurrent();
        
        // 每30秒記錄一次，避免日誌過多
        if (currentTime - lastLogTime >= 30)
        {
            Print("[MartingaleProfitTracker] 盈利統計 - 持倉數: ", positionCount, 
                  ", 總盈利: $", DoubleToString(totalProfit, 2));
            lastLogTime = currentTime;
        }
    }
    
    return totalProfit;
}

//+------------------------------------------------------------------+
//| 計算盈虧平衡價格                                                 |
//+------------------------------------------------------------------+
double MartingaleProfitTracker::CalculateBreakEvenPrice()
{
    double totalCost = 0.0;
    double totalLots = 0.0;
    int positionCount = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == Symbol())
            {
                totalCost += OrderOpenPrice() * OrderLots();
                totalLots += OrderLots();
                positionCount++;
            }
        }
    }
    
    double breakEvenPrice = (totalLots > 0.0) ? totalCost / totalLots : 0.0;
    
    if (positionCount > 0 && breakEvenPrice > 0.0)
    {
        Print("[MartingaleProfitTracker] 盈虧平衡價格: ", DoubleToString(breakEvenPrice, 5),
              " (基於 ", positionCount, " 個持倉, 總手數: ", DoubleToString(totalLots, 2), ")");
    }
    
    return breakEvenPrice;
}

//+------------------------------------------------------------------+
//| 取得總持倉數量                                                   |
//+------------------------------------------------------------------+
int MartingaleProfitTracker::GetTotalPositions()
{
    int count = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == Symbol())
            {
                count++;
            }
        }
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| 取得總手數                                                       |
//+------------------------------------------------------------------+
double MartingaleProfitTracker::GetTotalLotSize()
{
    double totalLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == Symbol())
            {
                totalLots += OrderLots();
            }
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| 取得盈利摘要                                                     |
//+------------------------------------------------------------------+
string MartingaleProfitTracker::GetProfitSummary()
{
    double totalProfit = CalculateTotalProfit();
    int totalPositions = GetTotalPositions();
    double totalLots = GetTotalLotSize();
    double breakEvenPrice = CalculateBreakEvenPrice();
    
    string summary = StringFormat("盈利追蹤摘要 - 持倉: %d | 總手數: %.2f | 總盈利: $%.2f | 目標: $%.2f",
                                 totalPositions, totalLots, totalProfit, m_overallProfitTarget);
    
    if (breakEvenPrice > 0.0)
    {
        summary += StringFormat(" | 平衡價: %.5f", breakEvenPrice);
    }
    
    if (totalProfit >= m_overallProfitTarget)
    {
        summary += " | *** 目標達成 ***";
    }
    
    return summary;
}

#endif // MARTINGALE_PROFIT_TRACKER_MQH

//+------------------------------------------------------------------+
//|                                          AccountProtection.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_MQH
#define ACCOUNT_PROTECTION_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountMonitor.mqh"
#include "RiskController.mqh"

//+------------------------------------------------------------------+
//| AccountProtection Class (Immutable Configuration Pattern)       |
//| Refactored to follow SRP with reduced complexity                 |
//| Main responsibility: Coordinate 3 core components               |
//|                                                                  |
//| IMMUTABLE PATTERN:                                               |
//| - All configuration must be set during construction             |
//| - No configuration setter methods available after creation      |
//| - Two constructor options:                                       |
//|   1. Protection level constructor (creates config internally)   |
//|   2. External config constructor (accepts config pointer)       |
//| - Configuration cannot be modified during object lifetime       |
//|                                                                  |
//| Framework Compatibility: Maintains EA_Wizard framework patterns |
//+------------------------------------------------------------------+
class AccountProtection : public BaseComponent
{
private:
    static const BaseErrorDescriptor CODE_ERRORS[]; // Component specific error codes
    static bool                      g_lockdownError;   // Lockdown error handling

    // Core components (simplified architecture)
    AccountProtectionConfig*    m_config;           // Configuration management
    AccountMonitor*            m_monitor;           // Monitoring + Status management
    RiskController*            m_riskController;    // Risk checking + Trading control

    // Component management
    bool                       m_componentsOwned;   // Whether we own the components

protected:
    //--- Override base class methods
    virtual void            SetLockDownError(bool lockdown = true) override;
    virtual bool            IsErrorLockedDown() override;

public:
    //--- Constructor and Destructor
                            AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE);
                            AccountProtection(AccountProtectionConfig* config);
    virtual                ~AccountProtection();

    //--- Component access methods (for advanced usage)
    AccountProtectionConfig*    GetConfig() const { return m_config; }
    AccountMonitor*            GetMonitor() const { return m_monitor; }
    RiskController*            GetRiskController() const { return m_riskController; }

    //--- Configuration methods removed for immutable pattern
    //--- All configuration must be set during construction

    //--- Information methods (delegated to appropriate components)
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const;
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const;
    double                  GetMaxLossPercent() const;
    double                  GetCurrentLossPercent() const;
    double                  GetCurrentDrawdownPercent() const;
    bool                    IsTradingHalted() const;

    //--- Protection check methods (delegated to risk controller)
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0, string symbol = "");
    bool                    IsSpreadAcceptable(string symbol = "");
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();

    //--- Status update methods (delegated to monitor and risk controller)
    void                    UpdateStatus();
    void                    ResetDailyCounters();
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");

    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;

    //--- Utility methods (internal data access + delegation)
    double                  GetAccountBalance() const { return AccountBalance(); }
    double                  GetAccountEquity() const { return AccountEquity(); }
    double                  GetAccountProfit() const { return AccountProfit(); }
    int                     GetOpenOrdersCount() const { return OrdersTotal(); }
    double                  GetTotalLotSize() const;
    double                  GetCurrentSpread(string symbol = "") const;
    bool                    IsNewTradingDay() const;
    string                  GetStatusDescription() const;

    //--- Summary methods
    string                  GetProtectionSummary() const;
};

//+------------------------------------------------------------------+
//| Constructor with protection level (immutable pattern)           |
//+------------------------------------------------------------------+
AccountProtection::AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE)
    : BaseComponent("AccountProtection")
{
    // Create simplified component architecture with immutable config
    m_config = new AccountProtectionConfig(level);
    m_monitor = new AccountMonitor();
    m_riskController = new RiskController(m_config, m_monitor);

    m_componentsOwned = true;

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Constructor with external config (immutable pattern)            |
//+------------------------------------------------------------------+
AccountProtection::AccountProtection(AccountProtectionConfig* config)
    : BaseComponent("AccountProtection")
{
    // Use externally provided immutable config
    // Configuration is managed externally and passed to constructor
    m_config = config;
    m_monitor = new AccountMonitor();
    m_riskController = new RiskController(m_config, m_monitor);

    // Config is externally managed, we only own monitor and risk controller
    m_componentsOwned = false;

    if(!IsErrorLockedDown())
    {
        AppendError(CODE_ERRORS);
    }
    SetLockDownError(true);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtection::~AccountProtection()
{
    // Always cleanup monitor and risk controller as we own them
    if (m_riskController != NULL) delete m_riskController;
    if (m_monitor != NULL) delete m_monitor;

    // Only cleanup config if we own it (protection level constructor)
    if (m_componentsOwned && m_config != NULL)
    {
        delete m_config;
    }
    // Note: External config (from config pointer constructor) is not deleted
    // as it's managed externally
}

//+------------------------------------------------------------------+
//| Initialize account protection                                    |
//+------------------------------------------------------------------+
bool AccountProtection::OnInitialize()
{
    // Initialize components in dependency order
    if (!m_config.Initialize())
    {
        HandleError(10700, GetErrorDescription(10700));
        return false;
    }

    if (!m_monitor.Initialize())
    {
        HandleError(10702, GetErrorDescription(10702));
        return false;
    }

    if (!m_riskController.Initialize())
    {
        HandleError(10703, GetErrorDescription(10703));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool AccountProtection::OnValidate()
{
    // Validate all components
    if (!m_config.Validate())
    {
        HandleError(10701, GetErrorDescription(10701));
        return false;
    }

    if (!m_monitor.Validate())
    {
        HandleError(10702, GetErrorDescription(10702));
        return false;
    }

    if (!m_riskController.Validate())
    {
        HandleError(10703, GetErrorDescription(10703));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
bool AccountProtection::OnUpdate()
{
    // Update all components
    if (!m_monitor.Update())
    {
        HandleError(10708, GetErrorDescription(10708));
        return false;
    }

    // Update status based on current metrics
    m_monitor.UpdateStatus(m_config.GetMaxLossPercent(), m_config.GetMaxDrawdownPercent());

    if (!m_riskController.Update())
    {
        HandleError(10703, GetErrorDescription(10703));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Configuration methods removed for immutable pattern             |
//| All configuration must be set during construction               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Information methods (delegated to appropriate components)        |
//+------------------------------------------------------------------+
ENUM_PROTECTION_LEVEL AccountProtection::GetProtectionLevel() const
{
    return (m_config != NULL) ? m_config.GetProtectionLevel() : PROTECTION_MODERATE;
}

ENUM_PROTECTION_STATUS AccountProtection::GetCurrentStatus() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentStatus() : STATUS_NORMAL;
}

double AccountProtection::GetMaxLossPercent() const
{
    return (m_config != NULL) ? m_config.GetMaxLossPercent() : 20.0;
}

double AccountProtection::GetCurrentLossPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentLossPercent() : 0.0;
}

double AccountProtection::GetCurrentDrawdownPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentDrawdownPercent() : 0.0;
}

bool AccountProtection::IsTradingHalted() const
{
    return (m_riskController != NULL) ? m_riskController.IsTradingHalted() : false;
}

//+------------------------------------------------------------------+
//| Protection check methods (delegated to risk controller)         |
//+------------------------------------------------------------------+
bool AccountProtection::IsTradingAllowed()
{
    return (m_riskController != NULL) ? m_riskController.IsTradingAllowed() : false;
}

bool AccountProtection::IsNewOrderAllowed(double lotSize = 0.0, string symbol = "")
{
    return (m_riskController != NULL) ? m_riskController.IsNewOrderAllowed(lotSize, symbol) : false;
}

bool AccountProtection::IsSpreadAcceptable(string symbol = "")
{
    return (m_riskController != NULL) ? m_riskController.CheckSpreadLimits(symbol) : false;
}

bool AccountProtection::CheckAccountLimits()
{
    return (m_riskController != NULL) ? m_riskController.CheckAccountLimits() : false;
}

bool AccountProtection::CheckPositionLimits(double lotSize = 0.0)
{
    return (m_riskController != NULL) ? m_riskController.CheckPositionLimits(lotSize) : false;
}

bool AccountProtection::CheckDailyLimits()
{
    return (m_riskController != NULL) ? m_riskController.CheckDailyLimits() : false;
}

//+------------------------------------------------------------------+
//| Status update methods (delegated to components)                 |
//+------------------------------------------------------------------+
void AccountProtection::UpdateStatus()
{
    if (m_monitor != NULL && m_config != NULL)
        m_monitor.UpdateStatus(m_config.GetMaxLossPercent(), m_config.GetMaxDrawdownPercent());
}

void AccountProtection::ResetDailyCounters()
{
    if (m_monitor != NULL)
        m_monitor.ResetDailyCounters();
}

void AccountProtection::HaltTrading(string reason = "")
{
    if (m_riskController != NULL)
        m_riskController.HaltTrading(reason);
}

void AccountProtection::ResumeTrading()
{
    if (m_riskController != NULL)
        m_riskController.ResumeTrading();
}

void AccountProtection::EmergencyStop(string reason = "")
{
    if (m_riskController != NULL)
        m_riskController.EmergencyStop(reason);
}

//+------------------------------------------------------------------+
//| Utility methods                                                  |
//+------------------------------------------------------------------+
double AccountProtection::GetTotalLotSize() const
{
    double totalLots = 0.0;
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
            totalLots += OrderLots();
    }
    return totalLots;
}

double AccountProtection::GetCurrentSpread(string symbol = "") const
{
    if (symbol == "") symbol = Symbol();
    return MarketInfo(symbol, MODE_SPREAD);
}

bool AccountProtection::IsNewTradingDay() const
{
    return (m_monitor != NULL) ? m_monitor.IsNewTradingDay() : false;
}

string AccountProtection::GetStatusDescription() const
{
    return (m_monitor != NULL) ? m_monitor.GetStatusDescription() : "Unknown";
}

//+------------------------------------------------------------------+
//| Get comprehensive protection summary                             |
//+------------------------------------------------------------------+
string AccountProtection::GetProtectionSummary() const
{
    string summary = "=== Account Protection Summary (Simplified) ===\n";

    if (m_config != NULL)
        summary += "Config: " + m_config.GetConfigSummary() + "\n\n";

    if (m_monitor != NULL)
        summary += "Monitor: " + m_monitor.GetMonitoringSummary() + "\n\n";

    if (m_riskController != NULL)
        summary += "Control: " + m_riskController.GetControlSummary();

    return summary;
}

const BaseErrorDescriptor AccountProtection::CODE_ERRORS[] =   // Component specific error codes
{
    // Account protection specific errors
    {10700, "Account protection initialization failed"},
    {10701, "Account protection configuration error"},
    {10702, "Account protection monitor error"},
    {10703, "Account protection risk controller error"},
    {10704, "Account protection validation failed"},
    {10705, "Account protection component error"},
    {10706, "Account protection trading halt error"},
    {10707, "Account protection emergency stop error"},
    {10708, "Account protection status update error"},
    {10709, "Account protection parameter error"}
};

bool AccountProtection::g_lockdownError = false;  // Lockdown error handling

//+------------------------------------------------------------------+
//| Set lockdown error handling                                      |
//+------------------------------------------------------------------+
void AccountProtection::SetLockDownError(bool lockdown)
{
    BaseComponent::SetLockDownError(lockdown);
    g_lockdownError = lockdown;
}

//+------------------------------------------------------------------+
//| Check if error handling is locked down                           |
//+------------------------------------------------------------------+
bool AccountProtection::IsErrorLockedDown()
{
    if(g_lockdownError && BaseComponent::IsErrorLockedDown())
    {
        return true;
    }
    return false;
}

#endif // ACCOUNT_PROTECTION_MQH

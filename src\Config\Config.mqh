//+------------------------------------------------------------------+
//|                                                       Config.mqh |
//|                                            EA_Wizard Framework  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef CONFIG_MQH
#define CONFIG_MQH

//+------------------------------------------------------------------+
//| Configuration Management System                                  |
//+------------------------------------------------------------------+
//| This file contains the core configuration management system for  |
//| the EA_Wizard framework. It provides centralized configuration  |
//| handling for all EA parameters and settings.                    |
//|                                                                  |
//| Purpose:                                                         |
//| - Define configuration parameters and their default values      |
//| - Provide parameter validation logic                            |
//| - Manage configuration state throughout EA lifecycle            |
//|                                                                  |
//| Future Implementation:                                           |
//| - Configuration parameter definitions                           |
//| - Parameter validation functions                                |
//| - Configuration loading and saving mechanisms                   |
//| - Runtime configuration updates                                 |
//+------------------------------------------------------------------+

// TODO: Add configuration parameter definitions
// TODO: Add parameter validation functions
// TODO: Add configuration management classes/functions

// Account Protection Settings
#define EA_OBJ_ACCOUNT_PROTECTION "AccountProtection"   // Account Protection Object Name
#define EA_OBJ_ACCOUNT_PROTECTION_DESC "Account Protection System"  // Account Protection Object Description
#define EA_OBJ_ACCOUNT_PROTECTION_ENABLED true   // Enable Account Protection System
#define EA_OBJ_ACCOUNT_PROTECTION_LOG_EVENTS true       // Log Protection Events

#endif // CONFIG_MQH

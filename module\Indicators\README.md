# Indicators Module

## Overview

The Indicators module provides technical indicator implementations that extend or utilize MQL4's built-in indicator functions. All indicators inherit from the `BaseIndicator` class and follow consistent patterns for calculation, signal generation, and parameter management.

## Module Structure

```
Indicators/
├── README.md                    # This documentation file
├── BollingerBands/             # Bollinger Bands implementation
│   └── BollingerBands.mqh      # Main Bollinger Bands class
├── MACD/                       # MACD indicator implementation
│   └── MACD.mqh                # Main MACD class
├── RSI/                        # RSI indicator implementation
│   └── RSI.mqh                 # Main RSI class
└── Common/                     # Shared indicator utilities
    └── IndicatorUtils.mqh      # Common utility functions
```

## Base Classes

### BaseIndicator

All indicator classes inherit from `BaseIndicator` which provides:

- **Initialization lifecycle**: `Initialize()`, `Validate()`, `Update()`
- **Signal generation**: `GenerateSignal()` method with standardized `SignalInfo` structure
- **Data access**: Price and time utilities with shift support
- **Error handling**: Built-in error management and logging
- **Buffer management**: Optional value buffering for performance

## Available Indicators

### 1. Bollinger Bands (`BollingerBands.mqh`)

**Purpose**: Volatility-based indicator for overbought/oversold detection

**Default Parameters**:
- Period: 20
- Deviation: 2.0
- MA Method: Simple Moving Average
- Applied Price: Close

**Key Methods**:
- `GetUpperBand(shift)` - Get upper band value
- `GetLowerBand(shift)` - Get lower band value  
- `GetMiddleBand(shift)` - Get middle band (MA) value
- `GetBandwidth(shift)` - Get bandwidth percentage
- `GetPercentB(shift)` - Get %B indicator value
- `IsPriceAboveUpperBand(shift)` - Check price position
- `IsPriceBelowLowerBand(shift)` - Check price position

**Signal Generation**:
- **Buy Signal**: Price below lower band (oversold)
- **Sell Signal**: Price above upper band (overbought)
- **Confidence**: Based on distance from bands and bandwidth expansion

### 2. MACD (`MACD.mqh`)

**Purpose**: Trend direction and momentum confirmation

**Default Parameters**:
- Fast EMA: 12
- Slow EMA: 26
- Signal SMA: 9
- Applied Price: Close

**Key Methods**:
- `GetMACDMain(shift)` - Get MACD main line
- `GetMACDSignal(shift)` - Get MACD signal line
- `GetMACDHistogram(shift)` - Get MACD histogram
- `IsBullishCrossover(shift)` - Check for bullish crossover
- `IsBearishCrossover(shift)` - Check for bearish crossover
- `IsAboveZero(shift)` - Check if MACD is above zero line
- `IsBelowZero(shift)` - Check if MACD is below zero line

**Signal Generation**:
- **Buy Signal**: Bullish crossover (main line crosses above signal line)
- **Sell Signal**: Bearish crossover (main line crosses below signal line)
- **Confidence**: Higher when crossover occurs below/above zero line

### 3. RSI (`RSI.mqh`)

**Purpose**: Momentum oscillator for overbought/oversold conditions

**Default Parameters**:
- Period: 14
- Overbought Level: 70
- Oversold Level: 30
- Applied Price: Close

**Key Methods**:
- `GetRSI(shift)` - Get RSI value
- `IsOverbought(shift)` - Check if RSI is overbought
- `IsOversold(shift)` - Check if RSI is oversold
- `IsNeutral(shift)` - Check if RSI is in neutral zone
- `IsBullishDivergence(shift, lookback)` - Check for bullish divergence
- `IsBearishDivergence(shift, lookback)` - Check for bearish divergence
- `CrossedAbove(level, shift)` - Check level crossover
- `CrossedBelow(level, shift)` - Check level crossover

**Signal Generation**:
- **Buy Signal**: RSI oversold or bullish divergence
- **Sell Signal**: RSI overbought or bearish divergence
- **Confidence**: Higher with divergence confirmation

## Common Utilities (`IndicatorUtils.mqh`)

The `IndicatorUtils` class provides static utility methods for:

### Price Calculations
- `CalculateTypicalPrice()` - (H+L+C)/3
- `CalculateWeightedPrice()` - (H+L+2*C)/4
- `CalculateMedianPrice()` - (H+L)/2
- `CalculateTrueRange()` - True Range calculation

### Moving Averages
- `CalculateSimpleMA()` - Simple moving average
- `CalculateExponentialMA()` - Exponential moving average
- `CalculateSmoothedMA()` - Smoothed moving average
- `CalculateLinearWeightedMA()` - Linear weighted moving average

### Statistical Functions
- `CalculateStandardDeviation()` - Standard deviation
- `CalculateVariance()` - Variance calculation
- `CalculateCorrelation()` - Correlation between arrays

### Signal Processing
- `IsArrayIncreasing()` - Check if array values are increasing
- `IsArrayDecreasing()` - Check if array values are decreasing
- `FindArrayPeak()` - Find maximum or minimum in array
- `CalculateSlope()` - Calculate array slope

## Usage Examples

### Basic Indicator Usage

```mql4
#include "module/Indicators/BollingerBands/BollingerBands.mqh"

// Create and initialize Bollinger Bands
BollingerBands* bb = new BollingerBands(Symbol(), Period(), 20, 2.0);
if (!bb.Initialize()) {
    Print("Failed to initialize Bollinger Bands");
    delete bb;
    return;
}

// Generate signal
SignalInfo signal = bb.GenerateSignal(0);
if (signal.type == SIGNAL_BUY) {
    Print("Bollinger Bands Buy Signal: ", signal.description);
    Print("Confidence: ", DoubleToString(signal.confidence, 2));
}

// Get specific values
double upperBand = bb.GetUpperBand(0);
double lowerBand = bb.GetLowerBand(0);
double currentPrice = Close[0];

if (currentPrice < lowerBand) {
    Print("Price is below lower Bollinger Band - Potential buy opportunity");
}

// Cleanup
delete bb;
```

### Multi-Indicator Analysis

```mql4
#include "module/Indicators/BollingerBands/BollingerBands.mqh"
#include "module/Indicators/MACD/MACD.mqh"
#include "module/Indicators/RSI/RSI.mqh"

// Initialize indicators
BollingerBands* bb = new BollingerBands();
MACD* macd = new MACD();
RSI* rsi = new RSI();

bb.Initialize();
macd.Initialize();
rsi.Initialize();

// Get signals from all indicators
SignalInfo bbSignal = bb.GenerateSignal(0);
SignalInfo macdSignal = macd.GenerateSignal(0);
SignalInfo rsiSignal = rsi.GenerateSignal(0);

// Check for signal alignment
if (bbSignal.type == SIGNAL_BUY && 
    macdSignal.type == SIGNAL_BUY && 
    rsiSignal.type == SIGNAL_BUY) {
    Print("Triple confirmation BUY signal!");
}

// Cleanup
delete bb;
delete macd;
delete rsi;
```

## Signal Information Structure

All indicators return signals using the standardized `SignalInfo` structure:

```mql4
struct SignalInfo {
    ENUM_SIGNAL_TYPE      type;           // SIGNAL_BUY, SIGNAL_SELL, SIGNAL_NONE
    ENUM_SIGNAL_STRENGTH  strength;       // STRENGTH_WEAK, STRENGTH_MEDIUM, STRENGTH_STRONG
    double                confidence;     // Confidence level (0.0 - 1.0)
    datetime              timestamp;      // Signal timestamp
    double                value;          // Indicator value
    string                description;    // Signal description
};
```

## Best Practices

### 1. Initialization
- Always call `Initialize()` before using indicators
- Check initialization return value for errors
- Use `IsInitialized()` to verify status

### 2. Parameter Validation
- Use `Validate()` method to check parameters
- Set appropriate periods based on timeframe
- Validate symbol and timeframe compatibility

### 3. Signal Processing
- Check signal confidence levels before acting
- Combine multiple indicators for confirmation
- Consider signal strength in decision making

### 4. Performance
- Reuse indicator instances when possible
- Use appropriate shift values for historical analysis
- Consider buffering for frequently accessed values

### 5. Error Handling
- Check for `EMPTY_VALUE` returns
- Handle insufficient data conditions
- Use error handling methods from base class

## Integration with EA_Wizard Framework

Indicators integrate seamlessly with the EA_Wizard framework:

- **OnInit Module**: Initialize indicators during EA startup
- **OnTick Module**: Generate signals on each tick
- **Signal Generation**: Combine multiple indicators for trading decisions
- **Risk Management**: Use indicator signals for position sizing and risk control

## Contributing

When adding new indicators:

1. Inherit from `BaseIndicator` class
2. Implement required virtual methods
3. Follow naming conventions (PascalCase)
4. Include comprehensive documentation
5. Add usage examples
6. Update this README.md file

## Dependencies

- `BaseIndicator.mqh` - Base indicator class
- MQL4 Standard Library - Built-in indicator functions
- EA_Wizard Framework - Integration components

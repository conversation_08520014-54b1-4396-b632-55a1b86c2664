# EA_Wizard-dev2 Product Requirements Document (PRD)

## 1. Project Overview

EA_Wizard-dev2 is an advanced MQL4 Expert Advisor (EA) trading automation framework designed to implement sophisticated algorithmic trading strategies. The project focuses on creating a modular, maintainable, and scalable trading system that combines Martingale position management with multi-indicator technical analysis for automated forex trading.

The framework follows a standardized directory structure with separate modules for initialization (OnInit), tick processing (OnTick), cleanup (OnDeinit), and configuration (Config), ensuring clear separation of concerns and maintainable code architecture.

## 2. Objectives

### Primary Objectives:
- Develop a robust Martingale-based trading strategy with 4-level position scaling
- Implement multi-indicator signal system using Bollinger Bands, MACD, and RSI
- Create comprehensive risk management system with account protection mechanisms
- Establish modular framework architecture for easy maintenance and extension
- Ensure reliable automated trading with minimal manual intervention

### Secondary Objectives:
- Provide configurable parameters for strategy customization
- Implement comprehensive monitoring and alert system
- Maintain code quality with proper documentation and testing
- Optimize performance for real-time trading environments
- Support multiple currency pairs through Symbol() function

## 3. Features & Requirements

### 3.1 Core Trading Strategy
- **Martingale System**: 4-level position scaling with fixed lot sequence [0.01, 0.01, 0.02, 0.04]
- **Entry Triggers**: Loss-based position scaling at 300 points per level
- **Profit Calculation**: Proportional take-profit based on lot size (300 points per 0.01 lot)
- **No Stop Loss**: Risk managed through account-level controls

### 3.2 Technical Analysis System
- **Bollinger Bands**: 20-period with 2.0 standard deviation for overbought/oversold detection
- **MACD**: 12/26/9 configuration for trend direction and momentum confirmation
- **RSI**: 14-period with 70/30 thresholds for additional overbought/oversold signals
- **Signal Logic**: Triple confirmation system requiring all indicators to align

### 3.3 Risk Management
- **Account Protection**: Maximum 20% account balance loss limit
- **Position Limits**: Maximum 20 concurrent orders
- **Spread Control**: Configurable maximum spread threshold (default 5 points)
- **Real-time Monitoring**: Continuous risk assessment on every tick

### 3.4 Framework Architecture
- **Modular Design**: Separate directories for OnInit, OnTick, OnDeinit, and Config
- **Index Files**: Unified entry points (index.mqh) for each module directory
- **Template System**: Reusable templates for rapid development
- **Standardized Structure**: Consistent organization across all modules

## 4. Technical Specifications

### 4.1 MQL4/Expert Advisor Requirements
- **Platform**: MetaTrader 4 (MQL4)
- **Execution Model**: Event-driven architecture with OnInit, OnTick, OnDeinit handlers
- **Memory Management**: Efficient resource allocation and cleanup
- **Error Handling**: Comprehensive error checking and recovery mechanisms
- **Performance**: Optimized for real-time tick processing

### 4.2 Directory Structure
```
src/
├── OnInit/                 # Initialization Pipeline Classes
│   ├── index.mqh          # OnInit unified entry point
│   └── *.mqh              # Initialization modules
├── OnTick/                 # Tick Processing Pipeline Classes
│   ├── index.mqh          # OnTick unified entry point
│   └── *.mqh              # Trading logic modules
├── OnDeinit/               # Cleanup Pipeline Classes
│   ├── index.mqh          # OnDeinit unified entry point
│   └── *.mqh              # Cleanup modules
├── Config/                 # Configuration Files
│   ├── index.mqh          # Config unified entry point
│   ├── Config.mqh         # Configuration initialization
│   └── Input.mqh          # Input parameter definitions
└── StandardEA.mqh         # Main EA file
```

### 4.3 Configuration Parameters
- **Magic Number**: Unique EA identifier (default: 12345)
- **Spread Control**: Maximum allowed spread (default: 5 points)
- **Slippage Tolerance**: Acceptable slippage range (default: 3 points)
- **Risk Parameters**: Configurable loss limits and position sizes
- **Indicator Settings**: Customizable technical indicator parameters

## 5. User Stories

### 5.1 Trader Stories
- As a trader, I want the EA to automatically identify trading opportunities using multiple technical indicators
- As a trader, I want the system to manage position sizing using Martingale strategy for recovery
- As a trader, I want comprehensive risk controls to protect my account from excessive losses
- As a trader, I want configurable parameters to adapt the strategy to different market conditions

### 5.2 Developer Stories
- As a developer, I want a modular architecture that allows easy maintenance and feature additions
- As a developer, I want clear separation of concerns between initialization, trading logic, and cleanup
- As a developer, I want standardized entry points for each module to simplify integration
- As a developer, I want comprehensive documentation and examples for rapid development

### 5.3 System Administrator Stories
- As a system admin, I want monitoring capabilities to track EA performance and health
- As a system admin, I want alert systems to notify of critical events or errors
- As a system admin, I want logging capabilities for troubleshooting and analysis

## 6. Success Criteria

### 6.1 Functional Success Criteria
- EA successfully executes Martingale strategy with 4-level position scaling
- Technical indicators correctly identify entry and exit signals
- Risk management system prevents account losses exceeding 20%
- All modules integrate seamlessly through standardized interfaces
- Configuration system allows parameter customization without code changes

### 6.2 Technical Success Criteria
- Code follows MQL4 best practices and coding standards
- All modules have proper error handling and resource management
- Performance meets real-time trading requirements (< 100ms per tick)
- Memory usage remains stable during extended operation
- System passes comprehensive testing including backtesting and forward testing

### 6.3 Quality Criteria
- Code coverage > 80% through unit and integration tests
- Documentation covers all public interfaces and configuration options
- Code review process ensures maintainability and readability
- Version control tracks all changes with meaningful commit messages

## 7. Dependencies

### 7.1 External Dependencies
- **MetaTrader 4 Platform**: Required runtime environment
- **MQL4 Standard Library**: Core MQL4 functions and classes
- **Market Data Feed**: Real-time price data for technical analysis
- **Broker API**: Order execution and account management

### 7.2 Internal Dependencies
- **mql4_module Framework**: Base framework components and utilities
- **EA_Wizard Framework**: Core trading framework and pipeline classes
- **Configuration System**: Parameter management and validation
- **Logging System**: Event tracking and debugging support

### 7.3 Development Dependencies
- **MQL4 Compiler**: Code compilation and optimization
- **MetaEditor**: Development environment and debugging tools
- **Version Control**: Git repository for source code management
- **Testing Framework**: Unit testing and validation tools

## 8. Implementation Phases

### Phase 1: Core Framework Setup
- Establish directory structure and index files
- Implement basic configuration system
- Create template files and standardized interfaces

### Phase 2: Trading Logic Implementation
- Develop Martingale position management system
- Implement technical indicator analysis
- Create signal generation and validation logic

### Phase 3: Risk Management Integration
- Implement account protection mechanisms
- Add position and spread controls
- Integrate real-time monitoring system

### Phase 4: Testing and Optimization
- Comprehensive unit and integration testing
- Backtesting with historical data
- Performance optimization and debugging

### Phase 5: Documentation and Deployment
- Complete technical documentation
- User guide and configuration examples
- Production deployment and monitoring setup

## 9. 主要路徑

- EA_Wizard 項目根目錄:
  C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard

- 開發目標路徑:
  C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard\src

EA_Wizard 框架路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\mql4_module\EA_Wizard

mql4-lib 庫路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\mql4_module\mql4-lib

MQL4 標準 Include 路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Include

## 10. 專案要求

- 嚴謹遵循 EA_Wizard 框架內的README.md 文件指引進行開發
- EA_Wizard/src/ 目錄標準化結構中,建議開發專案的代碼分別在OnInit/, OnTick/, OnDeinit/, Config/ 目錄下,避免和其他專案的代碼混淆
- 在OnInit/, OnTick/, OnDeinit/ 目錄下,建議相關的代碼壓縮在一個 mqh 文件,避免和其他mqh 文件混淆
- 因應開發需求,可在mqh 文件建立模組

//+------------------------------------------------------------------+
//|                                           PositionTracker.mqh |
//|                                    EA_Wizard Framework Component |
//|                                  Refactored for SRP Compliance  |
//+------------------------------------------------------------------+
#ifndef POSITION_TRACKER_MQH
#define POSITION_TRACKER_MQH

#include "../../Base/BaseComponent.mqh"
#include "PositionCollector.mqh"
#include "PositionStatistics.mqh"

//+------------------------------------------------------------------+
//| PositionTracker Class (Refactored)                              |
//| Orchestrates specialized position tracking components           |
//| Following Single Responsibility Principle                       |
//+------------------------------------------------------------------+
class PositionTracker : public BaseComponent
{
private:
    // Core components (composition pattern)
    PositionCollector*      m_collector;        // Position data collection
    PositionStatistics*     m_statistics;       // Statistical calculations

    // Component ownership flag
    bool                    m_componentsOwned;  // Whether to delete components in destructor

    // Error codes specific to PositionTracker
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionTracker(string symbol = "", int magicNumber = 0);
    virtual          ~PositionTracker();

    //--- Component access methods
    PositionCollector*      GetCollector() { return m_collector; }
    PositionStatistics*     GetStatistics() { return m_statistics; }

    //--- Configuration methods
    void              SetSymbol(string symbol);
    void              SetMagicNumber(int magic);
    void              SetTrackAllSymbols(bool track);
    void              SetTrackAllMagics(bool track);
    void              SetUpdateInterval(int seconds);

    //--- Information methods
    string            GetSymbol() const;
    int               GetMagicNumber() const;
    PositionStats     GetCurrentStats() const;
    PositionStats     GetDailyStats() const;
    int               GetPositionCount() const;

    //--- Position tracking methods
    void              UpdatePositions();
    void              UpdateStatistics();
    PositionInfo      GetPosition(int index);
    PositionInfo      GetPositionByTicket(int ticket);
    bool              IsPositionOpen(int ticket);

    //--- Position analysis methods
    double            GetAverageOpenPrice(int orderType = -1);
    double            GetLargestPosition();
    double            GetSmallestPosition();
    datetime          GetOldestPositionTime();
    datetime          GetNewestPositionTime();
    double            GetPositionExposure();

    //--- Built-in reporting methods (simplified)
    void              PrintPositionSummary();
    void              PrintDetailedPositions();
    void              PrintBasicReport();

    //--- Utility methods
    string            PositionTypeToString(int type);
    void              ResetDailyStats();

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;

private:
    //--- Internal methods
    void              InitializeComponents(string symbol, int magicNumber);
    void              CleanupComponents();
    bool              ValidateComponents();
};

// Error codes for PositionTracker
const BaseErrorDescriptor PositionTracker::CODE_ERRORS[] = {
    {1351, "Failed to initialize position tracking components"},
    {1352, "Invalid component reference"},
    {1353, "Component validation failed"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionTracker::PositionTracker(string symbol = "", int magicNumber = 0) : BaseComponent("PositionTracker")
{
    m_collector = NULL;
    m_statistics = NULL;
    m_componentsOwned = true;

    InitializeComponents(symbol, magicNumber);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionTracker::~PositionTracker()
{
    CleanupComponents();
}

//+------------------------------------------------------------------+
//| Initialize position tracker                                      |
//+------------------------------------------------------------------+
bool PositionTracker::OnInitialize()
{
    if (!ValidateComponents())
    {
        HandleError(1351, GetErrorDescription(1351));
        return false;
    }

    // Initialize all components
    if (m_collector && !m_collector.Initialize())
    {
        HandleError(1351, "Failed to initialize position collector");
        return false;
    }

    if (m_statistics && !m_statistics.Initialize())
    {
        HandleError(1351, "Failed to initialize position statistics");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionTracker::OnValidate()
{
    return ValidateComponents();
}

//+------------------------------------------------------------------+
//| Update position tracker                                          |
//+------------------------------------------------------------------+
bool PositionTracker::OnUpdate()
{
    if (!ValidateComponents()) return false;

    // Update core components
    if (m_collector) m_collector.Update();
    if (m_statistics) m_statistics.Update();

    // Update statistics with latest position data
    UpdateStatistics();

    return true;
}

//+------------------------------------------------------------------+
//| Initialize components                                             |
//+------------------------------------------------------------------+
void PositionTracker::InitializeComponents(string symbol, int magicNumber)
{
    if (m_componentsOwned)
    {
        m_collector = new PositionCollector(symbol, magicNumber);
        m_statistics = new PositionStatistics();
    }
}

//+------------------------------------------------------------------+
//| Cleanup components                                               |
//+------------------------------------------------------------------+
void PositionTracker::CleanupComponents()
{
    if (m_componentsOwned)
    {
        if (m_collector) { delete m_collector; m_collector = NULL; }
        if (m_statistics) { delete m_statistics; m_statistics = NULL; }
    }
}

//+------------------------------------------------------------------+
//| Validate components                                              |
//+------------------------------------------------------------------+
bool PositionTracker::ValidateComponents()
{
    if (m_collector == NULL || m_statistics == NULL)
    {
        HandleError(1352, GetErrorDescription(1352));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Configuration methods (delegated to components)                 |
//+------------------------------------------------------------------+
void PositionTracker::SetSymbol(string symbol)
{
    if (m_collector) m_collector.SetSymbol(symbol);
}

void PositionTracker::SetMagicNumber(int magic)
{
    if (m_collector) m_collector.SetMagicNumber(magic);
}

void PositionTracker::SetTrackAllSymbols(bool track)
{
    if (m_collector) m_collector.SetTrackAllSymbols(track);
}

void PositionTracker::SetTrackAllMagics(bool track)
{
    if (m_collector) m_collector.SetTrackAllMagics(track);
}

void PositionTracker::SetUpdateInterval(int seconds)
{
    if (m_collector) m_collector.SetUpdateInterval(seconds);
}

//+------------------------------------------------------------------+
//| Information methods (delegated to components)                   |
//+------------------------------------------------------------------+
string PositionTracker::GetSymbol() const
{
    return (m_collector) ? m_collector.GetSymbol() : "";
}

int PositionTracker::GetMagicNumber() const
{
    return (m_collector) ? m_collector.GetMagicNumber() : 0;
}

PositionStats PositionTracker::GetCurrentStats() const
{
    PositionStats emptyStats;
    ZeroMemory(emptyStats);
    return (m_statistics) ? m_statistics.GetCurrentStats() : emptyStats;
}

PositionStats PositionTracker::GetDailyStats() const
{
    PositionStats emptyStats;
    ZeroMemory(emptyStats);
    return (m_statistics) ? m_statistics.GetDailyStats() : emptyStats;
}

int PositionTracker::GetPositionCount() const
{
    return (m_collector) ? m_collector.GetPositionCount() : 0;
}

//+------------------------------------------------------------------+
//| Position tracking methods (delegated to collector)              |
//+------------------------------------------------------------------+
void PositionTracker::UpdatePositions()
{
    if (m_collector) m_collector.UpdatePositions();
}

void PositionTracker::UpdateStatistics()
{
    if (m_statistics && m_collector)
    {
        m_statistics.UpdateStatistics(m_collector);
    }
}

PositionInfo PositionTracker::GetPosition(int index)
{
    PositionInfo emptyPos;
    ZeroMemory(emptyPos);
    return (m_collector) ? m_collector.GetPosition(index) : emptyPos;
}

PositionInfo PositionTracker::GetPositionByTicket(int ticket)
{
    PositionInfo emptyPos;
    ZeroMemory(emptyPos);
    return (m_collector) ? m_collector.GetPositionByTicket(ticket) : emptyPos;
}

bool PositionTracker::IsPositionOpen(int ticket)
{
    return (m_collector) ? m_collector.IsPositionOpen(ticket) : false;
}

//+------------------------------------------------------------------+
//| Position analysis methods (delegated to statistics)             |
//+------------------------------------------------------------------+
double PositionTracker::GetAverageOpenPrice(int orderType = -1)
{
    if (!m_statistics || !m_collector) return 0.0;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();
    return m_statistics.GetAverageOpenPrice(positions, count, orderType);
}

double PositionTracker::GetLargestPosition()
{
    if (!m_statistics || !m_collector) return 0.0;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();
    return m_statistics.GetLargestPosition(positions, count);
}

double PositionTracker::GetSmallestPosition()
{
    if (!m_statistics || !m_collector) return 0.0;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();
    return m_statistics.GetSmallestPosition(positions, count);
}

datetime PositionTracker::GetOldestPositionTime()
{
    if (!m_statistics || !m_collector) return 0;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();
    return m_statistics.GetOldestPositionTime(positions, count);
}

datetime PositionTracker::GetNewestPositionTime()
{
    if (!m_statistics || !m_collector) return 0;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();
    return m_statistics.GetNewestPositionTime(positions, count);
}

double PositionTracker::GetPositionExposure()
{
    return (m_statistics) ? m_statistics.GetPositionExposure() : 0.0;
}

//+------------------------------------------------------------------+
//| Built-in reporting methods (simplified)                         |
//+------------------------------------------------------------------+
void PositionTracker::PrintPositionSummary()
{
    if (!m_statistics) return;

    PositionStats stats = m_statistics.GetCurrentStats();

    Print("=== Position Summary ===");
    Print("Total Positions: ", stats.totalPositions);
    Print("Buy Positions: ", stats.buyPositions, " (", DoubleToString(stats.buyLots, 2), " lots)");
    Print("Sell Positions: ", stats.sellPositions, " (", DoubleToString(stats.sellLots, 2), " lots)");
    Print("Total P&L: ", DoubleToString(stats.totalProfit, 2), " ", AccountCurrency());
    Print("Net Exposure: ", DoubleToString(GetPositionExposure(), 2), " lots");
    Print("========================");
}

void PositionTracker::PrintDetailedPositions()
{
    if (!m_collector) return;

    PositionInfo positions[];
    m_collector.GetAllPositions(positions);
    int count = m_collector.GetPositionCount();

    Print("=== Detailed Positions ===");

    for (int i = 0; i < count; i++)
    {
        PositionInfo pos = positions[i];
        Print("Ticket: ", pos.ticket,
              " | Type: ", PositionTypeToString(pos.type),
              " | Lots: ", DoubleToString(pos.lotSize, 2),
              " | Open: ", DoubleToString(pos.openPrice, Digits),
              " | Current: ", DoubleToString(pos.currentPrice, Digits),
              " | P&L: ", DoubleToString(pos.totalPL, 2));
    }
    Print("===========================");
}

void PositionTracker::PrintBasicReport()
{
    Print("=== Basic Position Report ===");
    Print("Report Time: ", TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES));
    Print("");

    PrintPositionSummary();
    Print("");

    PrintDetailedPositions();
    Print("==============================");
}

//+------------------------------------------------------------------+
//| Utility methods                                                  |
//+------------------------------------------------------------------+
string PositionTracker::PositionTypeToString(int type)
{
    switch(type)
    {
        case OP_BUY:  return "BUY";
        case OP_SELL: return "SELL";
        default:      return "UNKNOWN";
    }
}

void PositionTracker::ResetDailyStats()
{
    if (m_statistics) m_statistics.ResetDailyStats();
}

#endif // POSITION_TRACKER_MQH

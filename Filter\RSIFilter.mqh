
#property strict

#include "../mql4-lib/History/Filter.mqh"

//+------------------------------------------------------------------+
//| 設計思路                                                         |
//| Filter：用RSI判斷市場狀態（強勢、弱勢、震盪）                     |
//+------------------------------------------------------------------+
//
// Pseudocode:
// 定義 RSIFilter，繼承 FilterAdapter，根據RSI區間判斷 isLong()/isShort()/isRanging()
// 利用 MQL4 內建 iRSI() 函數取得RSI值，並比較其與超買/超賣線的關係以判斷市場狀態
//
// 多頭趨勢 (isLong): RSI > 超買線 (如70) - 表示強勢上漲趨勢
// 空頭趨勢 (isShort): RSI < 超賣線 (如30) - 表示強勢下跌趨勢
// 震盪區間 (isRanging): 超賣線 <= RSI <= 超買線 - 表示震盪整理狀態
//

//+------------------------------------------------------------------+
//| RSIFilter 類別                                                  |
//| 實作 RSI 趨勢/震盪狀態過濾器                                      |
//| 繼承自 FilterAdapter，提供市場狀態判斷功能                         |
//+------------------------------------------------------------------+
class RSIFilter : public FilterAdapter
{
    //+------------------------------------------------------------------+
    //| 靜態常數定義 - 所有硬編碼數值集中管理                             |
    //+------------------------------------------------------------------+
private:
    // 參數驗證常數
    static const int      CURRENT_BAR_SHIFT;            // 當前 K 線位移

    // RSI 預設參數常數
    static const double   DEFAULT_OVERSOLD_LEVEL;       // 預設超賣線
    static const double   DEFAULT_OVERBOUGHT_LEVEL;     // 預設超買線

    // 字串常數
    static const string   EMPTY_STRING;                 // 空字串常數

private:
    // RSI 參數設定
    string            m_symbol;          // 交易品種
    ENUM_TIMEFRAMES   m_timeframe;       // 時間週期
    int               m_period;          // RSI 週期
    int               m_appliedPrice;    // 應用價格類型
    double            m_oversoldLevel;   // 超賣線水平
    double            m_overboughtLevel; // 超買線水平

public:
    // 建構函數與解構函數
    RSIFilter(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
              int period = 14, int appliedPrice = PRICE_CLOSE);
    virtual          ~RSIFilter();

    // 覆寫 FilterAdapter 方法
    virtual bool      isLong() override;
    virtual bool      isShort() override;

    // 參數存取方法
    string            GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES   GetTimeframe() const { return m_timeframe; }
    int               GetPeriod() const { return m_period; }
    int               GetAppliedPrice() const { return m_appliedPrice; }
    double            GetOversoldLevel() const { return m_oversoldLevel; }
    double            GetOverboughtLevel() const { return m_overboughtLevel; }
};

//+------------------------------------------------------------------+
//| 靜態常數定義 - 所有硬編碼數值的實際定義                           |
//+------------------------------------------------------------------+

// 參數驗證常數定義
static const int RSIFilter::CURRENT_BAR_SHIFT = 0;

// RSI 預設參數常數定義
static const double RSIFilter::DEFAULT_OVERSOLD_LEVEL = 30.0;
static const double RSIFilter::DEFAULT_OVERBOUGHT_LEVEL = 70.0;

// 靜態字串常數定義
static const string RSIFilter::EMPTY_STRING = "";

//+------------------------------------------------------------------+
//| 建構函數                                                         |
//| 初始化 RSI 過濾器參數並進行驗證                                   |
//+------------------------------------------------------------------+
RSIFilter::RSIFilter(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                     int period = 14, int appliedPrice = PRICE_CLOSE)
{
    // 設定交易品種，如果為空則使用當前品種
    m_symbol = (symbol == EMPTY_STRING) ? Symbol() : symbol;
    m_timeframe = timeframe;
    m_period = period;
    m_appliedPrice = appliedPrice;
    m_oversoldLevel = DEFAULT_OVERSOLD_LEVEL;
    m_overboughtLevel = DEFAULT_OVERBOUGHT_LEVEL;
}

//+------------------------------------------------------------------+
//| 解構函數                                                         |
//| 清理資源                                                         |
//+------------------------------------------------------------------+
RSIFilter::~RSIFilter()
{
    // 清理工作（如果需要）
}

//+------------------------------------------------------------------+
//| 判斷多頭趨勢                                                     |
//| 檢測 RSI 是否處於超買區域，表示強勢上漲趨勢                        |
//+------------------------------------------------------------------+
bool RSIFilter::isLong()
{
    // 取得當前的 RSI 值
    double currentRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, CURRENT_BAR_SHIFT);

    // 多頭趨勢：RSI > 超買線，表示強勢上漲
    return (currentRSI > m_overboughtLevel);
}

//+------------------------------------------------------------------+
//| 判斷空頭趨勢                                                     |
//| 檢測 RSI 是否處於超賣區域，表示強勢下跌趨勢                        |
//+------------------------------------------------------------------+
bool RSIFilter::isShort()
{
    // 取得當前的 RSI 值
    double currentRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, CURRENT_BAR_SHIFT);

    // 空頭趨勢：RSI < 超賣線，表示強勢下跌
    return (currentRSI < m_oversoldLevel);
}


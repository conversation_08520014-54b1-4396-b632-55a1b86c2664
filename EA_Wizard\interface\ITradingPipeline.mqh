#property strict

//+------------------------------------------------------------------+
//| ITradingPipeline.mqh                                             |
//| 交易流水線介面定義                                               |
//| 定義了所有交易流水線必須實現的基本功能                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 交易流水線介面                                                   |
//| 遵循介面隔離原則，只包含核心的流水線操作方法                     |
//| 不包含任何setter方法，支持不可變設計                             |
//+------------------------------------------------------------------+
interface ITradingPipeline
{
    //+------------------------------------------------------------------+
    //| 執行流水線                                                       |
    //| 執行流水線的主要邏輯，實現類應該在此方法中完成具體的業務處理     |
    //+------------------------------------------------------------------+
    void Execute();

    //+------------------------------------------------------------------+
    //| 獲取流水線名稱                                                   |
    //| 返回流水線的唯一標識名稱，用於識別和管理                         |
    //| @return string 流水線名稱                                        |
    //+------------------------------------------------------------------+
    string GetName();

    //+------------------------------------------------------------------+
    //| 獲取流水線類型                                                   |
    //| 返回流水線的類型標識，用於分類和組織                             |
    //| @return string 流水線類型                                        |
    //+------------------------------------------------------------------+
    string GetType();

    //+------------------------------------------------------------------+
    //| 檢查是否已執行                                                   |
    //| 返回流水線的執行狀態，用於避免重複執行和狀態管理                 |
    //| @return bool true表示已執行，false表示未執行                     |
    //+------------------------------------------------------------------+
    bool IsExecuted();

    //+------------------------------------------------------------------+
    //| 重置流水線狀態                                                   |
    //| 將流水線重置為未執行狀態，允許重新執行                           |
    //| 實現類應該清理所有執行相關的狀態和資源                           |
    //+------------------------------------------------------------------+
    void Restore();
};

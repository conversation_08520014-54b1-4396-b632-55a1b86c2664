//+------------------------------------------------------------------+
//|                                        PositionAlertManager.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_ALERT_MANAGER_MQH
#define POSITION_ALERT_MANAGER_MQH

#include "../../Base/BaseComponent.mqh"
#include "PositionStatistics.mqh"
#include "PositionRiskAnalyzer.mqh"

//+------------------------------------------------------------------+
//| Alert Types Enumeration                                          |
//+------------------------------------------------------------------+
enum ENUM_ALERT_TYPE
{
    ALERT_PROFIT_THRESHOLD,     // Profit threshold reached
    ALERT_LOSS_THRESHOLD,       // Loss threshold reached
    ALERT_DRAWDOWN_WARNING,     // Drawdown warning
    ALERT_RISK_LIMIT,           // Risk limit exceeded
    ALERT_EXPOSURE_WARNING,     // Exposure warning
    ALERT_CUSTOM                // Custom alert
};

//+------------------------------------------------------------------+
//| Alert Configuration Structure                                    |
//+------------------------------------------------------------------+
struct AlertConfig
{
    bool              enabled;              // Alert enabled status
    double            profitThreshold;      // Profit alert threshold
    double            lossThreshold;        // Loss alert threshold
    double            drawdownWarning;      // Drawdown warning level
    double            exposureWarning;      // Exposure warning level
    bool              soundAlert;           // Enable sound alerts
    bool              emailAlert;           // Enable email alerts
    bool              pushNotification;     // Enable push notifications
    int               alertCooldown;        // Cooldown period in seconds
};

//+------------------------------------------------------------------+
//| Alert History Structure                                          |
//+------------------------------------------------------------------+
struct AlertHistory
{
    ENUM_ALERT_TYPE   type;                 // Alert type
    datetime          timestamp;            // Alert timestamp
    string            message;              // Alert message
    double            value;                // Alert trigger value
};

//+------------------------------------------------------------------+
//| PositionAlertManager Class                                       |
//| Responsible for managing position-related alerts                 |
//+------------------------------------------------------------------+
class PositionAlertManager : public BaseComponent
{
private:
    AlertConfig       m_config;             // Alert configuration
    AlertHistory      m_alertHistory[];     // Alert history
    datetime          m_lastAlertTime[];    // Last alert time for each type
    int               m_maxHistorySize;     // Maximum history size
    
    // Error codes specific to PositionAlertManager
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionAlertManager();
    virtual          ~PositionAlertManager();
    
    //--- Configuration methods
    void              SetAlertThresholds(double profitAlert, double lossAlert);
    void              SetDrawdownWarning(double warningLevel) { m_config.drawdownWarning = warningLevel; }
    void              SetExposureWarning(double warningLevel) { m_config.exposureWarning = warningLevel; }
    void              SetSoundAlert(bool enabled) { m_config.soundAlert = enabled; }
    void              SetEmailAlert(bool enabled) { m_config.emailAlert = enabled; }
    void              SetPushNotification(bool enabled) { m_config.pushNotification = enabled; }
    void              SetAlertCooldown(int seconds) { m_config.alertCooldown = MathMax(0, seconds); }
    void              EnableAlerts(bool enabled) { m_config.enabled = enabled; }
    
    //--- Alert checking methods
    void              CheckAlerts(const PositionStats& stats, const RiskAnalysisResult& riskAnalysis);
    void              CheckProfitLossAlerts(const PositionStats& stats);
    void              CheckRiskAlerts(const RiskAnalysisResult& riskAnalysis);
    void              SendCustomAlert(string message, double value = 0.0);
    
    //--- Alert history methods
    int               GetAlertHistoryCount() const { return ArraySize(m_alertHistory); }
    AlertHistory      GetAlertHistory(int index);
    void              ClearAlertHistory();
    void              PrintAlertHistory();
    
    //--- Configuration access methods
    AlertConfig       GetAlertConfig() const { return m_config; }
    bool              IsAlertsEnabled() const { return m_config.enabled; }
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;
    
private:
    //--- Internal alert methods
    void              TriggerAlert(ENUM_ALERT_TYPE type, string message, double value = 0.0);
    bool              CanSendAlert(ENUM_ALERT_TYPE type);
    void              AddToHistory(ENUM_ALERT_TYPE type, string message, double value);
    void              SendAlert(string message);
    string            AlertTypeToString(ENUM_ALERT_TYPE type);
    void              InitializeAlertTimes();
    void              TrimHistory();
};

// Error codes for PositionAlertManager
const BaseErrorDescriptor PositionAlertManager::CODE_ERRORS[] = {
    {1331, "Invalid alert configuration"},
    {1332, "Failed to send alert"},
    {1333, "Alert history overflow"},
    {1334, "Invalid alert type"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionAlertManager::PositionAlertManager() : BaseComponent("PositionAlertManager")
{
    // Initialize default configuration
    m_config.enabled = false;
    m_config.profitThreshold = 1000.0;
    m_config.lossThreshold = -500.0;
    m_config.drawdownWarning = 15.0;
    m_config.exposureWarning = 40.0;
    m_config.soundAlert = true;
    m_config.emailAlert = false;
    m_config.pushNotification = false;
    m_config.alertCooldown = 300; // 5 minutes
    
    m_maxHistorySize = 100;
    InitializeAlertTimes();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionAlertManager::~PositionAlertManager()
{
    ArrayFree(m_alertHistory);
    ArrayFree(m_lastAlertTime);
}

//+------------------------------------------------------------------+
//| Initialize alert manager                                         |
//+------------------------------------------------------------------+
bool PositionAlertManager::OnInitialize()
{
    InitializeAlertTimes();
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionAlertManager::OnValidate()
{
    if (m_config.profitThreshold <= m_config.lossThreshold)
    {
        HandleError(1331, GetErrorDescription(1331));
        return false;
    }
    
    if (m_config.drawdownWarning < 0.0 || m_config.drawdownWarning > 100.0)
    {
        HandleError(1331, GetErrorDescription(1331));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update alert manager                                             |
//+------------------------------------------------------------------+
bool PositionAlertManager::OnUpdate()
{
    TrimHistory();
    return true;
}

//+------------------------------------------------------------------+
//| Set alert thresholds                                             |
//+------------------------------------------------------------------+
void PositionAlertManager::SetAlertThresholds(double profitAlert, double lossAlert)
{
    m_config.profitThreshold = profitAlert;
    m_config.lossThreshold = lossAlert;
    m_config.enabled = true;
}

//+------------------------------------------------------------------+
//| Check all alerts                                                 |
//+------------------------------------------------------------------+
void PositionAlertManager::CheckAlerts(const PositionStats& stats, const RiskAnalysisResult& riskAnalysis)
{
    if (!m_config.enabled) return;
    
    CheckProfitLossAlerts(stats);
    CheckRiskAlerts(riskAnalysis);
}

//+------------------------------------------------------------------+
//| Check profit and loss alerts                                    |
//+------------------------------------------------------------------+
void PositionAlertManager::CheckProfitLossAlerts(const PositionStats& stats)
{
    // Check profit threshold
    if (stats.totalProfit >= m_config.profitThreshold)
    {
        string message = StringFormat("PROFIT ALERT: Total profit reached %s", 
                                    DoubleToString(stats.totalProfit, 2));
        TriggerAlert(ALERT_PROFIT_THRESHOLD, message, stats.totalProfit);
    }
    
    // Check loss threshold
    if (stats.totalProfit <= m_config.lossThreshold)
    {
        string message = StringFormat("LOSS ALERT: Total loss reached %s", 
                                    DoubleToString(stats.totalProfit, 2));
        TriggerAlert(ALERT_LOSS_THRESHOLD, message, stats.totalProfit);
    }
}

//+------------------------------------------------------------------+
//| Check risk-related alerts                                       |
//+------------------------------------------------------------------+
void PositionAlertManager::CheckRiskAlerts(const RiskAnalysisResult& riskAnalysis)
{
    // Check drawdown warning
    if (riskAnalysis.currentDrawdown >= m_config.drawdownWarning)
    {
        string message = StringFormat("DRAWDOWN WARNING: Current drawdown %s%%", 
                                    DoubleToString(riskAnalysis.currentDrawdown, 2));
        TriggerAlert(ALERT_DRAWDOWN_WARNING, message, riskAnalysis.currentDrawdown);
    }
    
    // Check exposure warning
    if (riskAnalysis.exposureRatio >= m_config.exposureWarning)
    {
        string message = StringFormat("EXPOSURE WARNING: Current exposure %s%%", 
                                    DoubleToString(riskAnalysis.exposureRatio, 2));
        TriggerAlert(ALERT_EXPOSURE_WARNING, message, riskAnalysis.exposureRatio);
    }
    
    // Check risk limit exceeded
    if (riskAnalysis.riskLimitExceeded)
    {
        string message = StringFormat("RISK LIMIT EXCEEDED: Risk level %s", riskAnalysis.riskLevel);
        TriggerAlert(ALERT_RISK_LIMIT, message, 0.0);
    }
}

//+------------------------------------------------------------------+
//| Send custom alert                                                |
//+------------------------------------------------------------------+
void PositionAlertManager::SendCustomAlert(string message, double value = 0.0)
{
    if (!m_config.enabled) return;
    
    TriggerAlert(ALERT_CUSTOM, message, value);
}

//+------------------------------------------------------------------+
//| Get alert history by index                                       |
//+------------------------------------------------------------------+
AlertHistory PositionAlertManager::GetAlertHistory(int index)
{
    AlertHistory emptyHistory;
    ZeroMemory(emptyHistory);
    
    if (index >= 0 && index < ArraySize(m_alertHistory))
    {
        return m_alertHistory[index];
    }
    
    return emptyHistory;
}

//+------------------------------------------------------------------+
//| Clear alert history                                              |
//+------------------------------------------------------------------+
void PositionAlertManager::ClearAlertHistory()
{
    ArrayFree(m_alertHistory);
}

//+------------------------------------------------------------------+
//| Print alert history                                              |
//+------------------------------------------------------------------+
void PositionAlertManager::PrintAlertHistory()
{
    Print("=== Alert History ===");
    
    for (int i = 0; i < ArraySize(m_alertHistory); i++)
    {
        AlertHistory alert = m_alertHistory[i];
        Print(TimeToString(alert.timestamp), " | ", 
              AlertTypeToString(alert.type), " | ", 
              alert.message);
    }
}

//+------------------------------------------------------------------+
//| Trigger alert                                                    |
//+------------------------------------------------------------------+
void PositionAlertManager::TriggerAlert(ENUM_ALERT_TYPE type, string message, double value = 0.0)
{
    if (!CanSendAlert(type)) return;
    
    SendAlert(message);
    AddToHistory(type, message, value);
    
    // Update last alert time
    if (ArraySize(m_lastAlertTime) > (int)type)
    {
        m_lastAlertTime[type] = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Check if alert can be sent (cooldown check)                     |
//+------------------------------------------------------------------+
bool PositionAlertManager::CanSendAlert(ENUM_ALERT_TYPE type)
{
    if (ArraySize(m_lastAlertTime) <= (int)type) return true;
    
    datetime currentTime = TimeCurrent();
    datetime lastTime = m_lastAlertTime[type];
    
    return (currentTime - lastTime >= m_config.alertCooldown);
}

//+------------------------------------------------------------------+
//| Add alert to history                                             |
//+------------------------------------------------------------------+
void PositionAlertManager::AddToHistory(ENUM_ALERT_TYPE type, string message, double value)
{
    int size = ArraySize(m_alertHistory);
    ArrayResize(m_alertHistory, size + 1);
    
    m_alertHistory[size].type = type;
    m_alertHistory[size].timestamp = TimeCurrent();
    m_alertHistory[size].message = message;
    m_alertHistory[size].value = value;
}

//+------------------------------------------------------------------+
//| Send alert using configured methods                              |
//+------------------------------------------------------------------+
void PositionAlertManager::SendAlert(string message)
{
    if (m_config.soundAlert)
    {
        Alert(message);
    }
    
    Print(message);
    
    if (m_config.emailAlert)
    {
        SendMail("Position Alert", message);
    }
    
    if (m_config.pushNotification)
    {
        SendNotification(message);
    }
}

//+------------------------------------------------------------------+
//| Convert alert type to string                                     |
//+------------------------------------------------------------------+
string PositionAlertManager::AlertTypeToString(ENUM_ALERT_TYPE type)
{
    switch(type)
    {
        case ALERT_PROFIT_THRESHOLD:  return "PROFIT";
        case ALERT_LOSS_THRESHOLD:    return "LOSS";
        case ALERT_DRAWDOWN_WARNING:  return "DRAWDOWN";
        case ALERT_RISK_LIMIT:        return "RISK";
        case ALERT_EXPOSURE_WARNING:  return "EXPOSURE";
        case ALERT_CUSTOM:            return "CUSTOM";
        default:                      return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Initialize alert times array                                     |
//+------------------------------------------------------------------+
void PositionAlertManager::InitializeAlertTimes()
{
    ArrayResize(m_lastAlertTime, 6); // Number of alert types
    ArrayInitialize(m_lastAlertTime, 0);
}

//+------------------------------------------------------------------+
//| Trim history to maximum size                                     |
//+------------------------------------------------------------------+
void PositionAlertManager::TrimHistory()
{
    int size = ArraySize(m_alertHistory);
    
    if (size > m_maxHistorySize)
    {
        int removeCount = size - m_maxHistorySize;
        
        // Shift array to remove oldest entries
        for (int i = 0; i < m_maxHistorySize; i++)
        {
            m_alertHistory[i] = m_alertHistory[i + removeCount];
        }
        
        ArrayResize(m_alertHistory, m_maxHistorySize);
    }
}

#endif // POSITION_ALERT_MANAGER_MQH

//+------------------------------------------------------------------+
//|                                      MartingaleRiskCoordinator.mqh |
//|                                      馬丁格爾風險協調器類別        |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_RISK_COORDINATOR_MQH
#define MARTINGALE_RISK_COORDINATOR_MQH

#property strict

// 引入所有子模組
#include "MartingaleLevelManager.mqh"
#include "MartingaleEquityProtector.mqh"
#include "MartingaleCircuitBreaker.mqh"
#include "MartingaleProfitTracker.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾風險協調器類別                                            |
//| 協調各個子模組，提供統一介面 (協調器模式)                         |
//+------------------------------------------------------------------+
class MartingaleRiskCoordinator
{
private:
    // 子模組指標
    MartingaleLevelManager*     m_levelManager;
    MartingaleEquityProtector*  m_equityProtector;
    MartingaleCircuitBreaker*   m_circuitBreaker;
    MartingaleProfitTracker*    m_profitTracker;

    // 序列狀態
    bool                        m_sequenceActive;
    datetime                    m_lastUpdateTime;

public:
    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+
                                MartingaleRiskCoordinator();
                               ~MartingaleRiskCoordinator();

    //+------------------------------------------------------------------+
    //| 初始化和配置方法                                                 |
    //+------------------------------------------------------------------+
    bool                        Initialize();
    bool                        Update();

    //+------------------------------------------------------------------+
    //| 層級管理代理方法                                                 |
    //+------------------------------------------------------------------+
    bool                        SetMaxLevels(int levels);
    bool                        SetInitialLot(double lot);
    bool                        SetGridStepPoints(int points);
    bool                        SetLotMultiplier(double multiplier);
    bool                        CanAddNextLevel(double currentPrice);
    double                      CalculateNextLevelPrice(int direction);
    double                      CalculateNextLotSize();

    //+------------------------------------------------------------------+
    //| 淨值保護代理方法                                                 |
    //+------------------------------------------------------------------+
    bool                        SetEquityStopPercent(double percent);
    bool                        CheckEquityProtection();

    //+------------------------------------------------------------------+
    //| 熔斷機制代理方法                                                 |
    //+------------------------------------------------------------------+
    void                        SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action);
    bool                        ShouldExecuteCircuitBreaker();

    //+------------------------------------------------------------------+
    //| 盈利追蹤代理方法                                                 |
    //+------------------------------------------------------------------+
    bool                        SetOverallProfitTarget(double target);
    bool                        CheckProfitTarget();

    //+------------------------------------------------------------------+
    //| 序列管理方法                                                     |
    //+------------------------------------------------------------------+
    void                        StartSequence(double entryPrice);
    void                        AddLevel(double entryPrice);
    void                        ResetSequence();

    //+------------------------------------------------------------------+
    //| 狀態查詢方法                                                     |
    //+------------------------------------------------------------------+
    bool                        IsSequenceActive() const { return m_sequenceActive; }
    int                         GetCurrentLevel();
    string                      GetRiskStatus();

private:
    //+------------------------------------------------------------------+
    //| 內部輔助方法                                                     |
    //+------------------------------------------------------------------+
    void                        LogEvent(string message);
    bool                        ValidateSubModules();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleRiskCoordinator::MartingaleRiskCoordinator()
{
    // 創建所有子模組
    m_levelManager = new MartingaleLevelManager();
    m_equityProtector = new MartingaleEquityProtector();
    m_circuitBreaker = new MartingaleCircuitBreaker();
    m_profitTracker = new MartingaleProfitTracker();

    // 初始化序列狀態
    m_sequenceActive = false;
    m_lastUpdateTime = 0;

    LogEvent("風險協調器建構完成");
}

//+------------------------------------------------------------------+
//| 解構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleRiskCoordinator::~MartingaleRiskCoordinator()
{
    // 清理所有子模組
    if (m_levelManager != NULL)
    {
        delete m_levelManager;
        m_levelManager = NULL;
    }

    if (m_equityProtector != NULL)
    {
        delete m_equityProtector;
        m_equityProtector = NULL;
    }

    if (m_circuitBreaker != NULL)
    {
        delete m_circuitBreaker;
        m_circuitBreaker = NULL;
    }

    if (m_profitTracker != NULL)
    {
        delete m_profitTracker;
        m_profitTracker = NULL;
    }

    LogEvent("風險協調器已清理");
}

//+------------------------------------------------------------------+
//| 初始化協調器                                                     |
//+------------------------------------------------------------------+
bool MartingaleRiskCoordinator::Initialize()
{
    // 驗證所有子模組
    if (!ValidateSubModules())
    {
        LogEvent("錯誤: 子模組驗證失敗");
        return false;
    }

    // 初始化淨值保護器
    if (!m_equityProtector.Initialize())
    {
        LogEvent("錯誤: 淨值保護器初始化失敗");
        return false;
    }

    // 重置所有狀態
    ResetSequence();

    LogEvent("風險協調器初始化完成");
    return true;
}

//+------------------------------------------------------------------+
//| 更新協調器                                                       |
//+------------------------------------------------------------------+
bool MartingaleRiskCoordinator::Update()
{
    if (!ValidateSubModules())
    {
        return false;
    }

    // 更新回撤追蹤
    m_equityProtector.UpdateDrawdownTracking();

    // 檢查熔斷條件
    if (ShouldExecuteCircuitBreaker())
    {
        LogEvent("觸發熔斷機制");
        return false; // 表示需要執行熔斷操作
    }

    m_lastUpdateTime = TimeCurrent();
    return true;
}

//+------------------------------------------------------------------+
//| 層級管理代理方法實作                                             |
//+------------------------------------------------------------------+
bool MartingaleRiskCoordinator::SetMaxLevels(int levels)
{
    return (m_levelManager != NULL) ? m_levelManager.SetMaxLevels(levels) : false;
}

bool MartingaleRiskCoordinator::SetInitialLot(double lot)
{
    return (m_levelManager != NULL) ? m_levelManager.SetInitialLot(lot) : false;
}

bool MartingaleRiskCoordinator::SetGridStepPoints(int points)
{
    return (m_levelManager != NULL) ? m_levelManager.SetGridStepPoints(points) : false;
}

bool MartingaleRiskCoordinator::SetLotMultiplier(double multiplier)
{
    return (m_levelManager != NULL) ? m_levelManager.SetLotMultiplier(multiplier) : false;
}

bool MartingaleRiskCoordinator::CanAddNextLevel(double currentPrice)
{
    if (m_levelManager == NULL || !m_sequenceActive)
        return false;

    // 檢查層級限制
    if (!m_levelManager.CanAddNextLevel())
        return false;

    // 檢查加倉距離
    if (!m_levelManager.IsGridStepReached(currentPrice))
        return false;

    // 檢查熔斷狀態
    if (m_circuitBreaker != NULL && m_circuitBreaker.CheckEmergencyStop())
        return false;

    return true;
}

double MartingaleRiskCoordinator::CalculateNextLevelPrice(int direction)
{
    return (m_levelManager != NULL) ? m_levelManager.CalculateNextLevelPrice(direction) : 0.0;
}

double MartingaleRiskCoordinator::CalculateNextLotSize()
{
    return (m_levelManager != NULL) ? m_levelManager.CalculateNextLotSize() : 0.0;
}

//+------------------------------------------------------------------+
//| 淨值保護代理方法實作                                             |
//+------------------------------------------------------------------+
bool MartingaleRiskCoordinator::SetEquityStopPercent(double percent)
{
    return (m_equityProtector != NULL) ? m_equityProtector.SetEquityStopPercent(percent) : false;
}

bool MartingaleRiskCoordinator::CheckEquityProtection()
{
    return (m_equityProtector != NULL) ? m_equityProtector.CheckEquityProtection() : true;
}

//+------------------------------------------------------------------+
//| 熔斷機制代理方法實作                                             |
//+------------------------------------------------------------------+
void MartingaleRiskCoordinator::SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action)
{
    if (m_circuitBreaker != NULL)
    {
        m_circuitBreaker.SetActionOnMaxLevel(action);
    }
}

bool MartingaleRiskCoordinator::ShouldExecuteCircuitBreaker()
{
    if (m_circuitBreaker == NULL || m_levelManager == NULL || m_equityProtector == NULL)
        return false;

    bool maxLevelReached = m_levelManager.IsMaxLevelReached();
    bool equityProtectionTriggered = !m_equityProtector.CheckEquityProtection();

    return m_circuitBreaker.ShouldExecuteCircuitBreaker(maxLevelReached, equityProtectionTriggered);
}

//+------------------------------------------------------------------+
//| 盈利追蹤代理方法實作                                             |
//+------------------------------------------------------------------+
bool MartingaleRiskCoordinator::SetOverallProfitTarget(double target)
{
    return (m_profitTracker != NULL) ? m_profitTracker.SetOverallProfitTarget(target) : false;
}

bool MartingaleRiskCoordinator::CheckProfitTarget()
{
    return (m_profitTracker != NULL) ? m_profitTracker.CheckProfitTarget() : false;
}

//+------------------------------------------------------------------+
//| 序列管理方法實作                                                 |
//+------------------------------------------------------------------+
void MartingaleRiskCoordinator::StartSequence(double entryPrice)
{
    if (m_levelManager == NULL)
    {
        LogEvent("錯誤: 層級管理器未初始化");
        return;
    }

    // 重置所有狀態
    ResetSequence();

    // 啟動序列
    m_sequenceActive = true;
    m_levelManager.AddLevel(entryPrice);

    LogEvent(StringFormat("馬丁格爾序列已啟動 - 進場價格: %.5f", entryPrice));
}

void MartingaleRiskCoordinator::AddLevel(double entryPrice)
{
    if (m_levelManager == NULL || !m_sequenceActive)
    {
        LogEvent("錯誤: 無法添加層級 - 序列未啟動或管理器未初始化");
        return;
    }

    m_levelManager.AddLevel(entryPrice);
    LogEvent(StringFormat("添加層級 - 當前層級: %d, 進場價格: %.5f",
                         GetCurrentLevel(), entryPrice));
}

void MartingaleRiskCoordinator::ResetSequence()
{
    m_sequenceActive = false;

    if (m_levelManager != NULL)
    {
        m_levelManager.ResetLevel();
    }

    if (m_circuitBreaker != NULL)
    {
        m_circuitBreaker.Reset();
    }

    LogEvent("馬丁格爾序列已重置");
}

//+------------------------------------------------------------------+
//| 狀態查詢方法實作                                                 |
//+------------------------------------------------------------------+
int MartingaleRiskCoordinator::GetCurrentLevel()
{
    return (m_levelManager != NULL) ? m_levelManager.GetCurrentLevel() : 0;
}

string MartingaleRiskCoordinator::GetRiskStatus()
{
    if (!ValidateSubModules())
    {
        return "風險協調器狀態異常 - 子模組未初始化";
    }

    string status = StringFormat("馬丁格爾風險狀態 - 層級: %d/%d",
                                GetCurrentLevel(), m_levelManager.GetMaxLevels());

    if (m_sequenceActive)
    {
        status += " | 活躍";

        // 添加淨值資訊
        double drawdown = m_equityProtector.GetCurrentDrawdownPercent();
        status += StringFormat(" | 回撤: %.2f%%", drawdown);

        // 添加持倉資訊
        int positions = m_profitTracker.GetTotalPositions();
        double totalLots = m_profitTracker.GetTotalLotSize();
        status += StringFormat(" | 持倉: %d | 總手數: %.2f", positions, totalLots);

        // 添加盈利資訊
        double totalProfit = m_profitTracker.CalculateTotalProfit();
        double profitTarget = m_profitTracker.GetOverallProfitTarget();
        status += StringFormat(" | 盈利: $%.2f/$%.2f", totalProfit, profitTarget);
    }
    else
    {
        status += " | 非活躍";
    }

    // 添加緊急停止狀態
    if (m_circuitBreaker.IsEmergencyStop())
    {
        status += " | *** 緊急停止 ***";
    }

    return status;
}

//+------------------------------------------------------------------+
//| 內部輔助方法實作                                                 |
//+------------------------------------------------------------------+
void MartingaleRiskCoordinator::LogEvent(string message)
{
    string logMessage = StringFormat("[MartingaleRiskCoordinator] %s", message);
    Print(logMessage);
}

bool MartingaleRiskCoordinator::ValidateSubModules()
{
    if (m_levelManager == NULL)
    {
        LogEvent("錯誤: 層級管理器未初始化");
        return false;
    }

    if (m_equityProtector == NULL)
    {
        LogEvent("錯誤: 淨值保護器未初始化");
        return false;
    }

    if (m_circuitBreaker == NULL)
    {
        LogEvent("錯誤: 熔斷器未初始化");
        return false;
    }

    if (m_profitTracker == NULL)
    {
        LogEvent("錯誤: 盈利追蹤器未初始化");
        return false;
    }

    return true;
}

#endif // MARTINGALE_RISK_COORDINATOR_MQH

#property strict

//+------------------------------------------------------------------+
//| TradingPipelineContainerLoggerDecorator.mqh                     |
//| 交易流水線容器日誌裝飾者                                         |
//| 為 TradingPipelineContainer 添加日誌記錄功能                    |
//+------------------------------------------------------------------+

#include "../TradingPipelineContainerBase.mqh"
#include "../../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| 日誌記錄器常量配置                                               |
//+------------------------------------------------------------------+
#ifdef _DEBUG
// 調試模式下的日誌設置
#define LOGGER_DEFAULT_FILE_NAME "TradingPipelineContainer_Debug.log"
#define LOGGER_DEFAULT_LEVEL DEBUG
#define LOGGER_DEFAULT_PRINT_TO_CONSOLE true
#define LOGGER_DEFAULT_APPEND_TO_EXISTING false
#else
// 正常模式下的日誌設置
#define LOGGER_DEFAULT_FILE_NAME "TradingPipelineContainer.log"
#define LOGGER_DEFAULT_LEVEL INFO
#define LOGGER_DEFAULT_PRINT_TO_CONSOLE false
#define LOGGER_DEFAULT_APPEND_TO_EXISTING true
#endif

//+------------------------------------------------------------------+
//| 交易流水線容器日誌裝飾者                                         |
//| 實現裝飾者模式，為流水線容器添加詳細的日誌記錄功能               |
//| 記錄執行過程、狀態變化和錯誤信息                                 |
//+------------------------------------------------------------------+
class TradingPipelineContainerLoggerDecorator : public TradingPipelineContainerBase
{
private:
    CFileLog* m_logger;                 // 日誌記錄器實例
    bool m_enableDetailedLogging;       // 是否啟用詳細日誌記錄

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //| @param name 容器名稱                                            |
    //| @param description 容器描述                                      |
    //| @param logger 日誌記錄器（必須傳入）                             |
    //| @param type 容器類型                                            |
    //| @param owned 是否擁有子流水線                                    |
    //| @param maxPipelines 最大子流水線數量                             |
    //| @param enableDetailedLogging 是否啟用詳細日誌記錄               |
    //+------------------------------------------------------------------+
    TradingPipelineContainerLoggerDecorator(string name,
                                           string description,
                                           CFileLog* logger,
                                           string type = "TradingPipelineContainerLoggerDecorator",
                                           bool owned = false,
                                           int maxPipelines = 50,
                                           bool enableDetailedLogging = true)
        : TradingPipelineContainerBase(name, description, type, owned, maxPipelines),
          m_logger(logger),
          m_enableDetailedLogging(enableDetailedLogging)
    {
        LogConstructor();
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~TradingPipelineContainerLoggerDecorator()
    {
        LogDestructor();
    }

    //+------------------------------------------------------------------+
    //| 覆寫 TradingPipelineContainerBase 方法以添加日誌記錄             |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 獲取流水線名稱 - 添加裝飾者標識                                 |
    //+------------------------------------------------------------------+
    virtual string GetName() override
    {
        string originalName = TradingPipelineContainerBase::GetName();
        return StringFormat("[Logger]%s", originalName);
    }

    //+------------------------------------------------------------------+
    //| 覆寫執行方法以添加日誌記錄                                       |
    //+------------------------------------------------------------------+
    virtual void Execute() override
    {
        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 開始執行流水線容器: %s (類型: %s)",
                                        GetName(),
                                        GetName(),
                                        GetType());
            m_logger.Info(message);

            if(m_enableDetailedLogging)
            {
                string detailMessage = StringFormat("[%s] 執行前狀態: %s, 子流水線數量: %d",
                                                  GetName(),
                                                  IsExecuted() ? "已執行" : "未執行",
                                                  GetPipelineCount());
                m_logger.Debug(detailMessage);
            }
        }

        // 調用基類的執行方法
        TradingPipelineContainerBase::Execute();

        if(m_logger != NULL)
        {
            bool isExecuted = IsExecuted();
            string message = StringFormat("[%s] 流水線容器執行完成: %s (結果: %s)",
                                        GetName(),
                                        GetName(),
                                        isExecuted ? "成功" : "失敗");

            if(isExecuted)
            {
                m_logger.Info(message);
            }
            else
            {
                m_logger.Warning(message);
            }

            if(m_enableDetailedLogging)
            {
                LogExecutionDetails();
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 覆寫重置方法以添加日誌記錄                                       |
    //+------------------------------------------------------------------+
    virtual void Restore() override
    {
        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 開始重置流水線容器: %s", GetName(), GetName());
            m_logger.Info(message);

            if(m_enableDetailedLogging)
            {
                string detailMessage = StringFormat("[%s] 重置前狀態: %s, 子流水線數量: %d",
                                                  GetName(),
                                                  IsExecuted() ? "已執行" : "未執行",
                                                  GetPipelineCount());
                m_logger.Debug(detailMessage);
            }
        }

        // 調用基類的重置方法
        TradingPipelineContainerBase::Restore();

        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 流水線容器重置完成: %s (狀態: %s)",
                                        GetName(),
                                        GetName(),
                                        IsExecuted() ? "仍已執行" : "已重置");
            m_logger.Info(message);
        }
    }

protected:
    //+------------------------------------------------------------------+
    //| 覆寫模板方法以添加日誌記錄                                       |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 執行前置檢查 - 添加日誌記錄                                     |
    //+------------------------------------------------------------------+
    virtual bool PreExecuteCheck() override
    {
        bool result = TradingPipelineContainerBase::PreExecuteCheck();

        if(m_logger != NULL && m_enableDetailedLogging)
        {
            string message = StringFormat("[%s] 執行前置檢查結果: %s", GetName(), result ? "通過" : "失敗");
            m_logger.Debug(message);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 執行後置處理 - 添加日誌記錄                                     |
    //+------------------------------------------------------------------+
    virtual void PostExecuteProcess() override
    {
        TradingPipelineContainerBase::PostExecuteProcess();

        if(m_logger != NULL && m_enableDetailedLogging)
        {
            string message = StringFormat("[%s] 執行後置處理完成", GetName());
            m_logger.Debug(message);
        }
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法 - 執行具體邏輯                                     |
    //+------------------------------------------------------------------+
    virtual void ExecuteInternal() override
    {
        if(m_logger != NULL && m_enableDetailedLogging)
        {
            string message = StringFormat("[%s] 開始執行內部邏輯", GetName());
            m_logger.Debug(message);
        }

        bool allSuccess = true;
        int executedCount = 0;

        foreachv(TradingPipelineBase*, pipeline, GetPointer(m_pipelines))
        {
            if(pipeline != NULL)
            {
                if(m_logger != NULL && m_enableDetailedLogging)
                {
                    string message = StringFormat("[%s] 執行子流水線: %s", GetName(), pipeline.GetName());
                    m_logger.Debug(message);
                }

                pipeline.Execute();
                executedCount++;

                // 如果子流水線執行失敗，記錄但繼續執行其他流水線
                if(!pipeline.IsExecuted())
                {
                    allSuccess = false;
                    if(m_logger != NULL)
                    {
                        string message = StringFormat("[%s] 子流水線執行失敗: %s", GetName(), pipeline.GetName());
                        m_logger.Warning(message);
                    }
                }
                else if(m_logger != NULL && m_enableDetailedLogging)
                {
                    string message = StringFormat("[%s] 子流水線執行成功: %s", GetName(), pipeline.GetName());
                    m_logger.Debug(message);
                }
            }
        }

        string resultMessage = StringFormat("執行完成，共執行 %d 個子流水線，%s",
                                          executedCount,
                                          allSuccess ? "全部成功" : "部分失敗");

        SetResult(allSuccess, resultMessage, allSuccess ? ERROR_LEVEL_INFO : ERROR_LEVEL_WARNING);

        if(m_logger != NULL)
        {
            string logMessage = StringFormat("[%s] %s", GetName(), resultMessage);
            if(allSuccess)
            {
                m_logger.Info(logMessage);
            }
            else
            {
                m_logger.Warning(logMessage);
            }
        }
    }

private:
    //+------------------------------------------------------------------+
    //| 私有輔助方法                                                     |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 記錄構造函數信息                                                 |
    //+------------------------------------------------------------------+
    void LogConstructor()
    {
        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 創建日誌裝飾者容器: %s (類型: %s)",
                                        GetName(),
                                        GetName(),
                                        GetType());
            m_logger.Info(message);
        }
    }

    //+------------------------------------------------------------------+
    //| 記錄析構函數信息                                                 |
    //+------------------------------------------------------------------+
    void LogDestructor()
    {
        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 銷毀日誌裝飾者容器", GetName());
            m_logger.Info(message);
        }
    }

    //+------------------------------------------------------------------+
    //| 記錄詳細執行信息                                                 |
    //+------------------------------------------------------------------+
    void LogExecutionDetails()
    {
        if(m_logger != NULL)
        {
            string details = StringFormat("[%s] 執行詳情 - 狀態: %s, 子流水線數量: %d",
                                        GetName(),
                                        IsExecuted() ? "已執行" : "未執行",
                                        GetPipelineCount());
            m_logger.Debug(details);
        }
    }

public:
    //+------------------------------------------------------------------+
    //| 日誌配置方法                                                     |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 設置是否啟用詳細日誌記錄                                         |
    //| @param enabled 是否啟用詳細日誌記錄                              |
    //+------------------------------------------------------------------+
    void SetDetailedLogging(bool enabled)
    {
        m_enableDetailedLogging = enabled;
        if(m_logger != NULL)
        {
            string message = StringFormat("[%s] 詳細日誌記錄已%s", GetName(), enabled ? "啟用" : "禁用");
            m_logger.Info(message);
        }
    }

    //+------------------------------------------------------------------+
    //| 獲取日誌記錄器實例                                               |
    //| @return CFileLog* 日誌記錄器實例                                 |
    //+------------------------------------------------------------------+
    CFileLog* GetLogger() const
    {
        return m_logger;
    }

    //+------------------------------------------------------------------+
    //| 檢查是否啟用詳細日誌記錄                                         |
    //| @return bool 是否啟用詳細日誌記錄                                |
    //+------------------------------------------------------------------+
    bool IsDetailedLoggingEnabled() const
    {
        return m_enableDetailedLogging;
    }
};

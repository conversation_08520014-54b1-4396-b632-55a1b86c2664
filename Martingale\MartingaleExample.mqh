//+------------------------------------------------------------------+
//|                                            MartingaleExample.mqh |
//|                                      馬丁格爾風險管理使用範例      |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_EXAMPLE_MQH
#define MARTINGALE_EXAMPLE_MQH

#property strict

#include "MartingaleRiskManager.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾風險管理使用範例類別                                      |
//| 展示如何使用重構後的馬丁格爾風險管理系統                          |
//+------------------------------------------------------------------+
class MartingaleExample
{
private:
    MartingaleRiskManager*      m_riskManager;      // 風險管理器
    
public:
    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+
                                MartingaleExample();
                               ~MartingaleExample();
    
    //+------------------------------------------------------------------+
    //| 範例方法                                                         |
    //+------------------------------------------------------------------+
    void                        RunBasicExample();
    void                        RunAdvancedExample();
    void                        RunRiskScenarioTest();
    
private:
    //+------------------------------------------------------------------+
    //| 輔助方法                                                         |
    //+------------------------------------------------------------------+
    void                        PrintSeparator(string title);
    void                        SimulateTrading();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleExample::MartingaleExample()
{
    m_riskManager = new MartingaleRiskManager();
    Print("[MartingaleExample] 範例系統初始化完成");
}

//+------------------------------------------------------------------+
//| 解構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleExample::~MartingaleExample()
{
    if (m_riskManager != NULL)
    {
        delete m_riskManager;
        m_riskManager = NULL;
    }
    Print("[MartingaleExample] 範例系統已清理");
}

//+------------------------------------------------------------------+
//| 基本使用範例                                                     |
//+------------------------------------------------------------------+
void MartingaleExample::RunBasicExample()
{
    PrintSeparator("基本使用範例");
    
    // 1. 初始化風險管理器
    if (!m_riskManager.Initialize())
    {
        Print("錯誤: 風險管理器初始化失敗");
        return;
    }
    
    // 2. 使用快速設定配置參數
    bool setupSuccess = m_riskManager.QuickSetup(
        6,          // 最大6層
        0.01,       // 初始0.01手
        300,        // 300點間隔
        1.8,        // 1.8倍乘數
        10.0,       // $10盈利目標
        30.0        // 30%淨值保護
    );
    
    if (!setupSuccess)
    {
        Print("錯誤: 快速設定失敗");
        return;
    }
    
    // 3. 設定到達上限後的操作
    m_riskManager.SetActionOnMaxLevel(MARTINGALE_CLOSE_IMMEDIATELY);
    
    // 4. 啟動馬丁格爾序列
    double entryPrice = Ask;
    m_riskManager.StartSequence(entryPrice);
    
    // 5. 顯示初始狀態
    Print("初始狀態: ", m_riskManager.GetRiskStatus());
    
    // 6. 模擬加倉場景
    Print("\n=== 模擬加倉場景 ===");
    double currentPrice = entryPrice - 0.0030; // 模擬價格下跌30點
    
    if (m_riskManager.CanAddNextLevel(currentPrice))
    {
        double nextLotSize = m_riskManager.CalculateNextLotSize();
        Print("可以加倉 - 下一手數: ", DoubleToString(nextLotSize, 2));
        
        m_riskManager.AddLevel(currentPrice);
        Print("加倉後狀態: ", m_riskManager.GetRiskStatus());
    }
    
    // 7. 檢查盈利目標
    if (m_riskManager.CheckProfitTarget())
    {
        Print("盈利目標已達成，建議平倉");
    }
    
    // 8. 檢查熔斷條件
    if (m_riskManager.ShouldExecuteCircuitBreaker())
    {
        Print("觸發熔斷機制，需要執行風險控制");
    }
}

//+------------------------------------------------------------------+
//| 進階使用範例                                                     |
//+------------------------------------------------------------------+
void MartingaleExample::RunAdvancedExample()
{
    PrintSeparator("進階使用範例");
    
    // 初始化
    if (!m_riskManager.Initialize())
    {
        Print("錯誤: 風險管理器初始化失敗");
        return;
    }
    
    // 分別配置各個參數
    Print("=== 分別配置風險參數 ===");
    m_riskManager.SetMaxLevels(8);
    m_riskManager.SetInitialLot(0.02);
    m_riskManager.SetGridStepPoints(250);
    m_riskManager.SetLotMultiplier(2.0);
    m_riskManager.SetOverallProfitTarget(20.0);
    m_riskManager.SetEquityStopPercent(25.0);
    
    // 顯示詳細狀態
    Print("\n", m_riskManager.GetDetailedStatus());
    
    // 模擬完整的交易序列
    SimulateTrading();
}

//+------------------------------------------------------------------+
//| 風險場景測試                                                     |
//+------------------------------------------------------------------+
void MartingaleExample::RunRiskScenarioTest()
{
    PrintSeparator("風險場景測試");
    
    if (!m_riskManager.Initialize())
    {
        Print("錯誤: 風險管理器初始化失敗");
        return;
    }
    
    // 設定高風險參數進行測試
    m_riskManager.QuickSetup(3, 0.01, 200, 3.0, 5.0, 50.0);
    
    Print("=== 測試最大層級限制 ===");
    double entryPrice = Ask;
    m_riskManager.StartSequence(entryPrice);
    
    // 模擬連續加倉直到達到上限
    for (int i = 1; i <= 5; i++)
    {
        double testPrice = entryPrice - (i * 0.0020);
        
        Print("測試第", i, "次加倉 - 價格: ", DoubleToString(testPrice, 5));
        
        if (m_riskManager.CanAddNextLevel(testPrice))
        {
            m_riskManager.AddLevel(testPrice);
            Print("  成功加倉 - 當前層級: ", m_riskManager.GetCurrentLevel());
        }
        else
        {
            Print("  無法加倉 - 可能達到限制");
        }
        
        // 檢查熔斷條件
        if (m_riskManager.ShouldExecuteCircuitBreaker())
        {
            Print("  *** 觸發熔斷機制 ***");
            break;
        }
    }
    
    Print("\n最終狀態: ", m_riskManager.GetRiskStatus());
}

//+------------------------------------------------------------------+
//| 輔助方法實作                                                     |
//+------------------------------------------------------------------+
void MartingaleExample::PrintSeparator(string title)
{
    Print("\n" + StringFormat("=== %s ===", title));
}

void MartingaleExample::SimulateTrading()
{
    Print("\n=== 模擬交易序列 ===");
    
    double entryPrice = Ask;
    m_riskManager.StartSequence(entryPrice);
    
    // 模擬價格變動和加倉
    double prices[] = {entryPrice - 0.0025, entryPrice - 0.0050, entryPrice - 0.0075};
    
    for (int i = 0; i < ArraySize(prices); i++)
    {
        double currentPrice = prices[i];
        Print("價格變動至: ", DoubleToString(currentPrice, 5));
        
        // 更新風險管理器
        m_riskManager.Update();
        
        // 檢查是否可以加倉
        if (m_riskManager.CanAddNextLevel(currentPrice))
        {
            double nextLot = m_riskManager.CalculateNextLotSize();
            Print("  執行加倉 - 手數: ", DoubleToString(nextLot, 2));
            m_riskManager.AddLevel(currentPrice);
        }
        
        // 顯示當前狀態
        Print("  ", m_riskManager.GetRiskStatus());
        
        // 檢查退出條件
        if (m_riskManager.CheckProfitTarget())
        {
            Print("  達到盈利目標，序列結束");
            break;
        }
        
        if (m_riskManager.ShouldExecuteCircuitBreaker())
        {
            Print("  觸發熔斷機制，序列結束");
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| 全域函數 - 執行所有範例                                          |
//+------------------------------------------------------------------+
void RunMartingaleExamples()
{
    MartingaleExample* example = new MartingaleExample();
    
    example.RunBasicExample();
    example.RunAdvancedExample();
    example.RunRiskScenarioTest();
    
    delete example;
    
    Print("\n=== 所有馬丁格爾範例執行完成 ===");
}

#endif // MARTINGALE_EXAMPLE_MQH

//+------------------------------------------------------------------+
//|                                         MartingaleLevelManager.mqh |
//|                                      馬丁格爾層級管理器類別        |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_LEVEL_MANAGER_MQH
#define MARTINGALE_LEVEL_MANAGER_MQH

#property strict

//+------------------------------------------------------------------+
//| 馬丁格爾層級管理器類別                                            |
//| 負責加倉層級管理和計算 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleLevelManager
{
private:
    // 層級管理參數
    int                     m_currentLevel;         // 當前加倉層級
    int                     m_maxLevels;            // 最大加倉層級
    double                  m_initialLot;           // 初始手數
    int                     m_gridStepPoints;       // 加倉間隔點數
    double                  m_lotMultiplier;        // 手數乘數
    double                  m_lastEntryPrice;       // 最後進場價格
    
public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleLevelManager();
    
    //+------------------------------------------------------------------+
    //| 參數配置方法                                                     |
    //+------------------------------------------------------------------+
    bool                    SetMaxLevels(int levels);
    bool                    SetInitialLot(double lot);
    bool                    SetGridStepPoints(int points);
    bool                    SetLotMultiplier(double multiplier);
    
    //+------------------------------------------------------------------+
    //| 層級管理方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CanAddNextLevel();
    double                  CalculateNextLevelPrice(int direction);
    double                  CalculateNextLotSize();
    bool                    IsGridStepReached(double currentPrice);
    void                    AddLevel(double entryPrice);
    void                    ResetLevel();
    
    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    int                     GetCurrentLevel() const { return m_currentLevel; }
    int                     GetMaxLevels() const { return m_maxLevels; }
    double                  GetLastEntryPrice() const { return m_lastEntryPrice; }
    bool                    IsMaxLevelReached() const { return m_currentLevel >= m_maxLevels; }
    
private:
    //+------------------------------------------------------------------+
    //| 輔助方法                                                         |
    //+------------------------------------------------------------------+
    double                  PointsToPrice(int points);
    bool                    ValidateParameters();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleLevelManager::MartingaleLevelManager()
{
    m_currentLevel = 0;
    m_maxLevels = 6;                    // 預設最大6層
    m_initialLot = 0.01;                // 預設初始手數
    m_gridStepPoints = 300;             // 預設300點間隔
    m_lotMultiplier = 1.8;              // 預設1.8倍乘數
    m_lastEntryPrice = 0.0;
    
    Print("[MartingaleLevelManager] 層級管理器初始化完成");
}

//+------------------------------------------------------------------+
//| 設定最大層級                                                     |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::SetMaxLevels(int levels)
{
    if (levels <= 0)
    {
        Print("[MartingaleLevelManager] 錯誤: 最大加倉層級必須大於0，當前值: ", levels);
        return false;
    }
    m_maxLevels = levels;
    Print("[MartingaleLevelManager] 最大層級設定為: ", levels);
    return true;
}

//+------------------------------------------------------------------+
//| 設定初始手數                                                     |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::SetInitialLot(double lot)
{
    if (lot <= 0.0)
    {
        Print("[MartingaleLevelManager] 錯誤: 初始手數必須大於0，當前值: ", DoubleToString(lot, 2));
        return false;
    }
    m_initialLot = lot;
    Print("[MartingaleLevelManager] 初始手數設定為: ", DoubleToString(lot, 2));
    return true;
}

//+------------------------------------------------------------------+
//| 設定加倉間隔點數                                                 |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::SetGridStepPoints(int points)
{
    if (points <= 0)
    {
        Print("[MartingaleLevelManager] 錯誤: 加倉間隔點數必須大於0，當前值: ", points);
        return false;
    }
    m_gridStepPoints = points;
    Print("[MartingaleLevelManager] 加倉間隔設定為: ", points, " 點");
    return true;
}

//+------------------------------------------------------------------+
//| 設定手數乘數                                                     |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::SetLotMultiplier(double multiplier)
{
    if (multiplier <= 1.0)
    {
        Print("[MartingaleLevelManager] 錯誤: 手數乘數必須大於1.0，當前值: ", DoubleToString(multiplier, 2));
        return false;
    }
    m_lotMultiplier = multiplier;
    Print("[MartingaleLevelManager] 手數乘數設定為: ", DoubleToString(multiplier, 2));
    return true;
}

//+------------------------------------------------------------------+
//| 檢查是否可以加倉                                                 |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::CanAddNextLevel()
{
    if (m_currentLevel >= m_maxLevels)
    {
        Print("[MartingaleLevelManager] 警告: 已達到最大加倉層級限制 (", m_maxLevels, ")");
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 計算下一層級價格                                                 |
//+------------------------------------------------------------------+
double MartingaleLevelManager::CalculateNextLevelPrice(int direction)
{
    if (m_lastEntryPrice <= 0.0)
    {
        Print("[MartingaleLevelManager] 錯誤: 無效的最後進場價格");
        return 0.0;
    }
    
    double stepDistance = PointsToPrice(m_gridStepPoints);
    
    if (direction > 0) // 買入方向
    {
        return m_lastEntryPrice - stepDistance;
    }
    else // 賣出方向
    {
        return m_lastEntryPrice + stepDistance;
    }
}

//+------------------------------------------------------------------+
//| 計算下一手數                                                     |
//+------------------------------------------------------------------+
double MartingaleLevelManager::CalculateNextLotSize()
{
    if (m_currentLevel == 0)
    {
        return m_initialLot;
    }
    
    // 計算當前層級的手數 = 初始手數 * (乘數^層級)
    double nextLotSize = m_initialLot;
    for (int i = 0; i < m_currentLevel; i++)
    {
        nextLotSize *= m_lotMultiplier;
    }
    
    // 標準化手數到經紀商規則
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);
    
    nextLotSize = MathMax(minLot, MathMin(maxLot, nextLotSize));
    nextLotSize = MathRound(nextLotSize / lotStep) * lotStep;
    
    return nextLotSize;
}

//+------------------------------------------------------------------+
//| 檢查是否達到加倉距離                                             |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::IsGridStepReached(double currentPrice)
{
    if (m_lastEntryPrice <= 0.0)
        return false;
    
    double priceDistance = MathAbs(currentPrice - m_lastEntryPrice);
    double requiredDistance = PointsToPrice(m_gridStepPoints);
    
    return (priceDistance >= requiredDistance);
}

//+------------------------------------------------------------------+
//| 添加層級                                                         |
//+------------------------------------------------------------------+
void MartingaleLevelManager::AddLevel(double entryPrice)
{
    if (m_currentLevel < m_maxLevels)
    {
        m_currentLevel++;
        m_lastEntryPrice = entryPrice;
        Print("[MartingaleLevelManager] 添加第 ", m_currentLevel, " 層 - 進場價格: ", DoubleToString(entryPrice, 5));
    }
    else
    {
        Print("[MartingaleLevelManager] 警告: 無法添加層級，已達到最大限制");
    }
}

//+------------------------------------------------------------------+
//| 重置層級                                                         |
//+------------------------------------------------------------------+
void MartingaleLevelManager::ResetLevel()
{
    m_currentLevel = 0;
    m_lastEntryPrice = 0.0;
    Print("[MartingaleLevelManager] 層級管理器已重置");
}

//+------------------------------------------------------------------+
//| 點數轉價格                                                       |
//+------------------------------------------------------------------+
double MartingaleLevelManager::PointsToPrice(int points)
{
    double point = MarketInfo(Symbol(), MODE_POINT);
    int digits = (int)MarketInfo(Symbol(), MODE_DIGITS);
    
    // 處理5位小數的貨幣對
    if (digits == 5 || digits == 3)
    {
        point *= 10.0;
    }
    
    return points * point;
}

//+------------------------------------------------------------------+
//| 驗證參數                                                         |
//+------------------------------------------------------------------+
bool MartingaleLevelManager::ValidateParameters()
{
    if (m_maxLevels <= 0 || m_initialLot <= 0.0 || 
        m_gridStepPoints <= 0 || m_lotMultiplier <= 1.0)
    {
        Print("[MartingaleLevelManager] 錯誤: 層級管理器參數驗證失敗");
        return false;
    }
    return true;
}

#endif // MARTINGALE_LEVEL_MANAGER_MQH

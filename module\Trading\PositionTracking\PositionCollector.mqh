//+------------------------------------------------------------------+
//|                                           PositionCollector.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_COLLECTOR_MQH
#define POSITION_COLLECTOR_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Position Information Structure                                   |
//+------------------------------------------------------------------+
struct PositionInfo
{
    int               ticket;           // Order ticket
    int               type;             // Order type (OP_BUY/OP_SELL)
    double            lotSize;          // Position size
    double            openPrice;        // Open price
    datetime          openTime;         // Open time
    double            stopLoss;         // Stop loss level
    double            takeProfit;       // Take profit level
    double            currentPrice;     // Current market price
    double            profit;           // Current profit/loss
    double            swap;             // Swap charges
    double            commission;       // Commission
    double            totalPL;          // Total P&L (profit + swap + commission)
    string            comment;          // Order comment
    int               magicNumber;      // Magic number
};

//+------------------------------------------------------------------+
//| PositionCollector Class                                          |
//| Responsible for collecting and updating position data           |
//+------------------------------------------------------------------+
class PositionCollector : public BaseComponent
{
private:
    string            m_symbol;             // Trading symbol
    int               m_magicNumber;        // Magic number filter
    bool              m_trackAllSymbols;    // Track all symbols or just specified
    bool              m_trackAllMagics;     // Track all magic numbers or just specified

    PositionInfo      m_positions[];        // Array of position information
    datetime          m_lastUpdate;         // Last update time
    int               m_updateInterval;     // Update interval in seconds

    // Error codes specific to PositionCollector
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionCollector(string symbol = "", int magicNumber = 0);
    virtual          ~PositionCollector();

    //--- Configuration methods
    void              SetSymbol(string symbol) { m_symbol = symbol; }
    void              SetMagicNumber(int magic) { m_magicNumber = magic; }
    void              SetTrackAllSymbols(bool track) { m_trackAllSymbols = track; }
    void              SetTrackAllMagics(bool track) { m_trackAllMagics = track; }
    void              SetUpdateInterval(int seconds) { m_updateInterval = MathMax(1, seconds); }

    //--- Information methods
    string            GetSymbol() const { return m_symbol; }
    int               GetMagicNumber() const { return m_magicNumber; }
    int               GetPositionCount() const { return ArraySize(m_positions); }
    datetime          GetLastUpdate() const { return m_lastUpdate; }

    //--- Position collection methods
    void              UpdatePositions();
    PositionInfo      GetPosition(int index);
    PositionInfo      GetPositionByTicket(int ticket);
    bool              IsPositionOpen(int ticket);
    void              GetAllPositions(PositionInfo& positions[]);

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;

private:
    //--- Internal methods
    bool              ShouldTrackPosition(const PositionInfo& position);
    void              CalculatePositionPL(PositionInfo& position);
    bool              ShouldUpdate();
};

// Error codes for PositionCollector
const BaseErrorDescriptor PositionCollector::CODE_ERRORS[] = {
    {1301, "Invalid symbol for position tracking"},
    {1302, "Failed to update positions"},
    {1303, "Position not found"},
    {1304, "Invalid position index"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionCollector::PositionCollector(string symbol = "", int magicNumber = 0) : BaseComponent("PositionCollector")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_magicNumber = magicNumber;
    m_trackAllSymbols = (symbol == "");
    m_trackAllMagics = (magicNumber == 0);

    m_lastUpdate = 0;
    m_updateInterval = 5; // 5 seconds
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionCollector::~PositionCollector()
{
    ArrayFree(m_positions);
}

//+------------------------------------------------------------------+
//| Initialize position collector                                    |
//+------------------------------------------------------------------+
bool PositionCollector::OnInitialize()
{
    UpdatePositions();
    m_lastUpdate = TimeCurrent();
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionCollector::OnValidate()
{
    if (!m_trackAllSymbols && m_symbol == "")
    {
        HandleError(1301, GetErrorDescription(1301));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update position collector                                        |
//+------------------------------------------------------------------+
bool PositionCollector::OnUpdate()
{
    if (ShouldUpdate())
    {
        UpdatePositions();
        m_lastUpdate = TimeCurrent();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if update is needed                                        |
//+------------------------------------------------------------------+
bool PositionCollector::ShouldUpdate()
{
    datetime currentTime = TimeCurrent();
    return (currentTime - m_lastUpdate >= m_updateInterval);
}

//+------------------------------------------------------------------+
//| Update positions array                                           |
//+------------------------------------------------------------------+
void PositionCollector::UpdatePositions()
{
    ArrayFree(m_positions);

    int totalOrders = OrdersTotal();
    int positionCount = 0;

    // Count relevant positions first
    for (int i = 0; i < totalOrders; i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            PositionInfo tempPos;
            tempPos.ticket = OrderTicket();
            tempPos.type = OrderType();
            tempPos.lotSize = OrderLots();
            tempPos.openPrice = OrderOpenPrice();
            tempPos.openTime = OrderOpenTime();
            tempPos.stopLoss = OrderStopLoss();
            tempPos.takeProfit = OrderTakeProfit();
            tempPos.profit = OrderProfit();
            tempPos.swap = OrderSwap();
            tempPos.commission = OrderCommission();
            tempPos.comment = OrderComment();
            tempPos.magicNumber = OrderMagicNumber();

            if (ShouldTrackPosition(tempPos))
            {
                positionCount++;
            }
        }
    }

    // Resize array and populate
    ArrayResize(m_positions, positionCount);
    int index = 0;

    for (int i = 0; i < totalOrders; i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            PositionInfo pos;
            pos.ticket = OrderTicket();
            pos.type = OrderType();
            pos.lotSize = OrderLots();
            pos.openPrice = OrderOpenPrice();
            pos.openTime = OrderOpenTime();
            pos.stopLoss = OrderStopLoss();
            pos.takeProfit = OrderTakeProfit();
            pos.profit = OrderProfit();
            pos.swap = OrderSwap();
            pos.commission = OrderCommission();
            pos.comment = OrderComment();
            pos.magicNumber = OrderMagicNumber();

            if (ShouldTrackPosition(pos))
            {
                // Calculate current price and P&L
                CalculatePositionPL(pos);

                m_positions[index] = pos;
                index++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get position by index                                            |
//+------------------------------------------------------------------+
PositionInfo PositionCollector::GetPosition(int index)
{
    PositionInfo emptyPos;
    ZeroMemory(emptyPos);

    if (index >= 0 && index < ArraySize(m_positions))
    {
        return m_positions[index];
    }

    HandleError(1304, GetErrorDescription(1304));
    return emptyPos;
}

//+------------------------------------------------------------------+
//| Get position by ticket                                           |
//+------------------------------------------------------------------+
PositionInfo PositionCollector::GetPositionByTicket(int ticket)
{
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (m_positions[i].ticket == ticket)
        {
            return m_positions[i];
        }
    }

    PositionInfo emptyPos;
    ZeroMemory(emptyPos);
    HandleError(1303, GetErrorDescription(1303));
    return emptyPos;
}

//+------------------------------------------------------------------+
//| Check if position is open                                        |
//+------------------------------------------------------------------+
bool PositionCollector::IsPositionOpen(int ticket)
{
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (m_positions[i].ticket == ticket)
        {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if position should be tracked                             |
//+------------------------------------------------------------------+
bool PositionCollector::ShouldTrackPosition(const PositionInfo& position)
{
    // Check symbol filter
    if (!m_trackAllSymbols && OrderSymbol() != m_symbol)
        return false;

    // Check magic number filter
    if (!m_trackAllMagics && position.magicNumber != m_magicNumber)
        return false;

    // Only track market orders (OP_BUY and OP_SELL)
    if (position.type != OP_BUY && position.type != OP_SELL)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Calculate position P&L                                          |
//+------------------------------------------------------------------+
void PositionCollector::CalculatePositionPL(PositionInfo& position)
{
    string symbol = OrderSymbol();

    if (position.type == OP_BUY)
    {
        position.currentPrice = MarketInfo(symbol, MODE_BID);
    }
    else if (position.type == OP_SELL)
    {
        position.currentPrice = MarketInfo(symbol, MODE_ASK);
    }

    position.totalPL = position.profit + position.swap + position.commission;
}

//+------------------------------------------------------------------+
//| Get all positions array                                          |
//+------------------------------------------------------------------+
void PositionCollector::GetAllPositions(PositionInfo& positions[])
{
    int count = ArraySize(m_positions);
    ArrayResize(positions, count);

    for (int i = 0; i < count; i++)
    {
        positions[i] = m_positions[i];
    }
}

#endif // POSITION_COLLECTOR_MQH

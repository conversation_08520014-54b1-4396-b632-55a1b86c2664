//+------------------------------------------------------------------+
//|                                           DynamicStopLoss.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef DYNAMIC_STOP_LOSS_MQH
#define DYNAMIC_STOP_LOSS_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Stop Loss Type Enumeration                                       |
//+------------------------------------------------------------------+
enum ENUM_STOP_LOSS_TYPE
{
    SL_NONE = 0,            // No stop loss
    SL_FIXED_POINTS = 1,    // Fixed points stop loss
    SL_ATR_BASED = 2,       // ATR-based stop loss
    SL_SUPPORT_RESISTANCE = 3, // Support/Resistance based
    SL_TRAILING = 4         // Trailing stop loss
};

//+------------------------------------------------------------------+
//| DynamicStopLoss Class                                            |
//| Implementation of dynamic stop loss management                  |
//+------------------------------------------------------------------+
class DynamicStopLoss : public BaseComponent
{
private:
    string                m_symbol;           // Trading symbol
    ENUM_TIMEFRAMES       m_timeframe;        // Chart timeframe
    ENUM_STOP_LOSS_TYPE   m_stopLossType;     // Stop loss type
    double                m_fixedPoints;      // Fixed points for SL
    int                   m_atrPeriod;        // ATR period
    double                m_atrMultiplier;    // ATR multiplier
    double                m_trailingDistance; // Trailing distance
    double                m_trailingStep;     // Trailing step
    int                   m_minStopLevel;     // Minimum stop level

public:
    //--- Constructor and Destructor
                          DynamicStopLoss(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                         ENUM_STOP_LOSS_TYPE type = SL_FIXED_POINTS);
    virtual              ~DynamicStopLoss();
    
    //--- Configuration methods
    void                  SetSymbol(string symbol) { m_symbol = symbol; }
    void                  SetTimeframe(ENUM_TIMEFRAMES timeframe) { m_timeframe = timeframe; }
    void                  SetStopLossType(ENUM_STOP_LOSS_TYPE type) { m_stopLossType = type; }
    void                  SetFixedPoints(double points) { m_fixedPoints = MathMax(0.0, points); }
    void                  SetATRParameters(int period, double multiplier);
    void                  SetTrailingParameters(double distance, double step);
    
    //--- Information methods
    string                GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES       GetTimeframe() const { return m_timeframe; }
    ENUM_STOP_LOSS_TYPE   GetStopLossType() const { return m_stopLossType; }
    double                GetFixedPoints() const { return m_fixedPoints; }
    int                   GetATRPeriod() const { return m_atrPeriod; }
    double                GetATRMultiplier() const { return m_atrMultiplier; }
    
    //--- Stop loss calculation methods
    double                CalculateStopLoss(int orderType, double entryPrice, int shift = 0);
    double                CalculateFixedStopLoss(int orderType, double entryPrice);
    double                CalculateATRStopLoss(int orderType, double entryPrice, int shift = 0);
    double                CalculateSupportResistanceStopLoss(int orderType, double entryPrice, int shift = 0);
    
    //--- Trailing stop methods
    double                CalculateTrailingStop(int orderType, double currentPrice, double currentStopLoss);
    bool                  ShouldUpdateTrailingStop(int orderType, double currentPrice, double currentStopLoss);
    
    //--- Validation methods
    bool                  IsValidStopLoss(int orderType, double entryPrice, double stopLoss);
    double                NormalizeStopLoss(int orderType, double entryPrice, double stopLoss);
    
    //--- Override base class methods
    virtual bool          OnInitialize() override;
    virtual bool          OnValidate() override;
    
    //--- Utility methods
    double                GetATR(int shift = 0);
    double                FindSupport(int shift = 0, int lookback = 20);
    double                FindResistance(int shift = 0, int lookback = 20);
    void                  UpdateMinStopLevel();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
DynamicStopLoss::DynamicStopLoss(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                ENUM_STOP_LOSS_TYPE type = SL_FIXED_POINTS) : BaseComponent("DynamicStopLoss")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_stopLossType = type;
    m_fixedPoints = 50.0;
    m_atrPeriod = 14;
    m_atrMultiplier = 2.0;
    m_trailingDistance = 30.0;
    m_trailingStep = 10.0;
    m_minStopLevel = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
DynamicStopLoss::~DynamicStopLoss()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize dynamic stop loss                                     |
//+------------------------------------------------------------------+
bool DynamicStopLoss::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(701, "Invalid symbol for stop loss calculation");
        return false;
    }
    
    UpdateMinStopLevel();
    
    if (m_stopLossType == SL_ATR_BASED && m_atrPeriod < 1)
    {
        SetError(702, "Invalid ATR period");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool DynamicStopLoss::OnValidate()
{
    if (m_fixedPoints < 0.0)
    {
        SetError(703, "Fixed points must be non-negative");
        return false;
    }
    
    if (m_atrMultiplier <= 0.0)
    {
        SetError(704, "ATR multiplier must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set ATR parameters                                               |
//+------------------------------------------------------------------+
void DynamicStopLoss::SetATRParameters(int period, double multiplier)
{
    m_atrPeriod = MathMax(1, period);
    m_atrMultiplier = MathMax(0.1, multiplier);
}

//+------------------------------------------------------------------+
//| Set trailing parameters                                          |
//+------------------------------------------------------------------+
void DynamicStopLoss::SetTrailingParameters(double distance, double step)
{
    m_trailingDistance = MathMax(0.0, distance);
    m_trailingStep = MathMax(0.0, step);
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on type                               |
//+------------------------------------------------------------------+
double DynamicStopLoss::CalculateStopLoss(int orderType, double entryPrice, int shift = 0)
{
    double stopLoss = 0.0;
    
    switch(m_stopLossType)
    {
        case SL_NONE:
            return 0.0;
            
        case SL_FIXED_POINTS:
            stopLoss = CalculateFixedStopLoss(orderType, entryPrice);
            break;
            
        case SL_ATR_BASED:
            stopLoss = CalculateATRStopLoss(orderType, entryPrice, shift);
            break;
            
        case SL_SUPPORT_RESISTANCE:
            stopLoss = CalculateSupportResistanceStopLoss(orderType, entryPrice, shift);
            break;
            
        case SL_TRAILING:
            // Trailing stop is calculated separately
            stopLoss = CalculateFixedStopLoss(orderType, entryPrice);
            break;
            
        default:
            stopLoss = CalculateFixedStopLoss(orderType, entryPrice);
            break;
    }
    
    return NormalizeStopLoss(orderType, entryPrice, stopLoss);
}

//+------------------------------------------------------------------+
//| Calculate fixed points stop loss                                |
//+------------------------------------------------------------------+
double DynamicStopLoss::CalculateFixedStopLoss(int orderType, double entryPrice)
{
    if (entryPrice <= 0.0 || m_fixedPoints <= 0.0)
        return 0.0;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    if (point <= 0.0)
        return 0.0;
    
    if (orderType == OP_BUY)
    {
        return entryPrice - (m_fixedPoints * point);
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice + (m_fixedPoints * point);
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based stop loss                                   |
//+------------------------------------------------------------------+
double DynamicStopLoss::CalculateATRStopLoss(int orderType, double entryPrice, int shift = 0)
{
    if (entryPrice <= 0.0)
        return 0.0;
    
    double atr = GetATR(shift);
    if (atr <= 0.0)
        return CalculateFixedStopLoss(orderType, entryPrice);
    
    double stopDistance = atr * m_atrMultiplier;
    
    if (orderType == OP_BUY)
    {
        return entryPrice - stopDistance;
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice + stopDistance;
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate support/resistance based stop loss                    |
//+------------------------------------------------------------------+
double DynamicStopLoss::CalculateSupportResistanceStopLoss(int orderType, double entryPrice, int shift = 0)
{
    if (entryPrice <= 0.0)
        return 0.0;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    double buffer = 10.0 * point; // Small buffer beyond support/resistance
    
    if (orderType == OP_BUY)
    {
        double support = FindSupport(shift);
        if (support > 0.0 && support < entryPrice)
        {
            return support - buffer;
        }
    }
    else if (orderType == OP_SELL)
    {
        double resistance = FindResistance(shift);
        if (resistance > 0.0 && resistance > entryPrice)
        {
            return resistance + buffer;
        }
    }
    
    // Fallback to fixed stop loss
    return CalculateFixedStopLoss(orderType, entryPrice);
}

//+------------------------------------------------------------------+
//| Calculate trailing stop                                          |
//+------------------------------------------------------------------+
double DynamicStopLoss::CalculateTrailingStop(int orderType, double currentPrice, double currentStopLoss)
{
    if (currentPrice <= 0.0 || m_trailingDistance <= 0.0)
        return currentStopLoss;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    if (point <= 0.0)
        return currentStopLoss;
    
    double trailingDistance = m_trailingDistance * point;
    
    if (orderType == OP_BUY)
    {
        double newStopLoss = currentPrice - trailingDistance;
        return (newStopLoss > currentStopLoss) ? newStopLoss : currentStopLoss;
    }
    else if (orderType == OP_SELL)
    {
        double newStopLoss = currentPrice + trailingDistance;
        return (newStopLoss < currentStopLoss || currentStopLoss == 0.0) ? newStopLoss : currentStopLoss;
    }
    
    return currentStopLoss;
}

//+------------------------------------------------------------------+
//| Check if trailing stop should be updated                        |
//+------------------------------------------------------------------+
bool DynamicStopLoss::ShouldUpdateTrailingStop(int orderType, double currentPrice, double currentStopLoss)
{
    if (currentPrice <= 0.0 || m_trailingStep <= 0.0)
        return false;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    if (point <= 0.0)
        return false;
    
    double trailingStep = m_trailingStep * point;
    
    if (orderType == OP_BUY)
    {
        double newStopLoss = currentPrice - (m_trailingDistance * point);
        return (newStopLoss - currentStopLoss >= trailingStep);
    }
    else if (orderType == OP_SELL)
    {
        double newStopLoss = currentPrice + (m_trailingDistance * point);
        return (currentStopLoss - newStopLoss >= trailingStep);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Validate stop loss level                                        |
//+------------------------------------------------------------------+
bool DynamicStopLoss::IsValidStopLoss(int orderType, double entryPrice, double stopLoss)
{
    if (entryPrice <= 0.0 || stopLoss <= 0.0)
        return false;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    if (point <= 0.0)
        return false;
    
    double distance = MathAbs(entryPrice - stopLoss) / point;
    
    if (distance < m_minStopLevel)
        return false;
    
    if (orderType == OP_BUY && stopLoss >= entryPrice)
        return false;
    
    if (orderType == OP_SELL && stopLoss <= entryPrice)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Normalize stop loss to valid level                              |
//+------------------------------------------------------------------+
double DynamicStopLoss::NormalizeStopLoss(int orderType, double entryPrice, double stopLoss)
{
    if (stopLoss <= 0.0)
        return 0.0;
    
    if (!IsValidStopLoss(orderType, entryPrice, stopLoss))
    {
        // Adjust to minimum stop level
        double point = MarketInfo(m_symbol, MODE_POINT);
        double minDistance = m_minStopLevel * point;
        
        if (orderType == OP_BUY)
        {
            stopLoss = entryPrice - minDistance;
        }
        else if (orderType == OP_SELL)
        {
            stopLoss = entryPrice + minDistance;
        }
    }
    
    return NormalizeDouble(stopLoss, Digits);
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double DynamicStopLoss::GetATR(int shift = 0)
{
    return iATR(m_symbol, m_timeframe, m_atrPeriod, shift);
}

//+------------------------------------------------------------------+
//| Find support level                                              |
//+------------------------------------------------------------------+
double DynamicStopLoss::FindSupport(int shift = 0, int lookback = 20)
{
    double minLow = iLow(m_symbol, m_timeframe, shift);
    
    for (int i = 1; i < lookback; i++)
    {
        double low = iLow(m_symbol, m_timeframe, shift + i);
        if (low < minLow)
            minLow = low;
    }
    
    return minLow;
}

//+------------------------------------------------------------------+
//| Find resistance level                                           |
//+------------------------------------------------------------------+
double DynamicStopLoss::FindResistance(int shift = 0, int lookback = 20)
{
    double maxHigh = iHigh(m_symbol, m_timeframe, shift);
    
    for (int i = 1; i < lookback; i++)
    {
        double high = iHigh(m_symbol, m_timeframe, shift + i);
        if (high > maxHigh)
            maxHigh = high;
    }
    
    return maxHigh;
}

//+------------------------------------------------------------------+
//| Update minimum stop level                                        |
//+------------------------------------------------------------------+
void DynamicStopLoss::UpdateMinStopLevel()
{
    m_minStopLevel = (int)MarketInfo(m_symbol, MODE_STOPLEVEL);
    if (m_minStopLevel <= 0)
        m_minStopLevel = 10; // Default minimum
}

#endif // DYNAMIC_STOP_LOSS_MQH

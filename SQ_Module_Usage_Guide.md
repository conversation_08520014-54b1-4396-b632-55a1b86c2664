# SQ.mq4 模組使用指南

[![Guide](https://img.shields.io/badge/Guide-Module%20Usage-blue.svg)](https://github.com)
[![Level](https://img.shields.io/badge/Level-Intermediate-orange.svg)](https://github.com)

## 快速導航

- [模組概覽](#模組概覽)
- [核心模組使用](#核心模組使用)
- [自定義修改指南](#自定義修改指南)
- [常見問題解決](#常見問題解決)
- [性能調優建議](#性能調優建議)

## 模組概覽

### 🎯 核心控制模組
**用途**: 主要執行流程控制  
**修改頻率**: 很少  
**風險等級**: 極高  

### 📈 交易策略模組
**用途**: 交易信號生成和執行  
**修改頻率**: 經常  
**風險等級**: 高  

### 📋 訂單管理模組
**用途**: 訂單生命週期管理  
**修改頻率**: 中等  
**風險等級**: 高  

### ⚠️ 風險管理模組
**用途**: 風險控制和保護  
**修改頻率**: 中等  
**風險等級**: 中等  

### 📊 技術指標模組
**用途**: 技術分析計算  
**修改頻率**: 中等  
**風險等級**: 低  

### 🔧 輔助功能模組
**用途**: 基礎工具函數  
**修改頻率**: 很少  
**風險等級**: 低  

### 🛡️ OrderReliable 庫
**用途**: 可靠訂單執行  
**修改頻率**: 從不  
**風險等級**: 極高  

## 核心模組使用

### 1. 修改交易策略

#### 當前策略邏輯
```mql4
// 位置：OnTick() 函數中
if (sqIsBarOpen()
    && ((sqMACD(...) > sqIndicatorHighest(...))
    && (iLow(...) < sqIndicatorLowest(...)))) {
    
    // 執行買入
    _ticket = sqOpenOrder(OP_BUY, ...);
}
```

#### 添加新的入場條件
```mql4
// 範例：添加 RSI 過賣條件
if (sqIsBarOpen()
    && ((sqMACD(...) > sqIndicatorHighest(...))
    && (iLow(...) < sqIndicatorLowest(...))
    && (iRSI(NULL, 0, 14, PRICE_CLOSE, 1) < 30))) { // 新增條件
    
    _ticket = sqOpenOrder(OP_BUY, ...);
}
```

#### 添加賣出策略
```mql4
// 在現有買入邏輯後添加
if (sqIsBarOpen()
    && ((sqMACD(...) < sqIndicatorLowest(...))
    && (iHigh(...) > sqIndicatorHighest(...)))) {
    
    _ticket = sqOpenOrder(OP_SELL, "Current", 0.1, 0, MagicNumber, "", 0, false, false, CLR_NONE);
    
    if(_ticket > 0 && OrderSelect(_ticket, SELECT_BY_TICKET)) {
        sqSetSLandPT(_ticket, 
                     sqGetSLLevel("Current", OP_SELL, 0, 1, StopLoss), 
                     sqGetPTLevel("Current", OP_SELL, 0, 1, ProfitTarget));
    }
}
```

### 2. 調整風險管理參數

#### 修改止損止盈設定
```mql4
// 在參數區域修改
extern int ProfitTarget = 100;  // 從 50 改為 100
extern int StopLoss = 30;       // 從 50 改為 30
```

#### 添加動態止損
```mql4
// 在 sqManageOrders() 中添加
void sqManageOrders(int magicNumber) {
    if(_sqIsBarOpen) {
        for(int i=OrdersTotal()-1; i>=0; i--) {
            if (OrderSelect(i,SELECT_BY_POS)==true) {
                // 現有邏輯...
                
                // 添加動態止損邏輯
                sqManageDynamicStopLoss(ticket);  // 新增函數
            }
        }
    }
}

// 新增動態止損函數
void sqManageDynamicStopLoss(int ticket) {
    if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;
    
    // 根據 ATR 調整止損
    double atr = iATR(OrderSymbol(), 0, 14, 1);
    double newSL = 0;
    
    if(OrderType() == OP_BUY) {
        newSL = Bid - (atr * 2);
        if(newSL > OrderStopLoss()) {
            sqOrderModifySL(ticket, newSL, SLPTTYPE_PRICE);
        }
    }
}
```

### 3. 添加新的技術指標

#### 添加 RSI 指標函數
```mql4
// 在技術指標模組中添加
double sqRSI(string symbol, int timeframe, int period, int applied_price, int shift) {
    period = sqFixRanges(period, 2, 100, 14);
    applied_price = sqFixRanges(applied_price, 0, 6, 0);
    
    return roundValue(iRSI(symbol, timeframe, period, applied_price, shift));
}
```

#### 添加布林帶指標
```mql4
double sqBollingerBands(string symbol, int timeframe, int period, double deviation, 
                        int applied_price, int mode, int shift) {
    period = sqFixRanges(period, 2, 100, 20);
    applied_price = sqFixRanges(applied_price, 0, 6, 0);
    mode = sqFixRanges(mode, 0, 2, 0); // 0=主線, 1=上軌, 2=下軌
    
    return roundValue(iBands(symbol, timeframe, period, deviation, 0, applied_price, mode, shift));
}
```

### 4. 自定義時間控制

#### 添加新聞時段避免交易
```mql4
// 在 sqHandleTradingOptions() 中添加
bool sqHandleTradingOptions() {
    // 現有檢查...
    
    // 新增新聞時段檢查
    if(sqIsNewsTime()) {
        return false;
    }
    
    return true;
}

// 新增新聞時段檢查函數
bool sqIsNewsTime() {
    // 定義新聞時段（例如：每週五 20:30-21:30 UTC）
    if(DayOfWeek() == FRIDAY) {
        int currentHour = Hour();
        int currentMinute = Minute();
        int currentTime = currentHour * 100 + currentMinute;
        
        if(currentTime >= 2030 && currentTime <= 2130) {
            return true;
        }
    }
    
    return false;
}
```

## 自定義修改指南

### 安全修改原則

#### ✅ 安全修改區域
1. **參數區域**: 修改 extern 變數
2. **交易策略邏輯**: OnTick() 中的條件判斷
3. **風險管理參數**: 止損止盈設定
4. **時間控制邏輯**: 交易時間限制

#### ⚠️ 謹慎修改區域
1. **訂單管理邏輯**: sqManageOrders() 函數
2. **技術指標計算**: 指標函數的核心邏輯
3. **全域變數管理**: sqSetGlobalVariable/sqGetGlobalVariable

#### ❌ 禁止修改區域
1. **OrderReliable 庫**: 任何 OrderReliable 相關函數
2. **核心初始化**: OnInit() 的基本結構
3. **K線檢測邏輯**: sqInitStart() 的核心邏輯

### 修改步驟建議

#### 1. 備份原始檔案
```bash
# 建議在修改前備份
copy SQ.mq4 SQ_backup_YYYYMMDD.mq4
```

#### 2. 小步驟修改
```mql4
// 不要一次修改太多，建議每次只修改一個功能
// 例如：先添加 RSI 指標，測試無誤後再添加其他功能
```

#### 3. 編譯測試
```mql4
// 每次修改後立即編譯
// 確保沒有語法錯誤
```

#### 4. 回測驗證
```mql4
// 在策略測試器中驗證修改效果
// 比較修改前後的表現
```

### 常見修改範例

#### 範例 1：添加多時間框架確認
```mql4
// 在交易策略中添加更高時間框架的趨勢確認
if (sqIsBarOpen()
    && ((sqMACD(NULL,0, MACDFast, MACDSlow, MACDSmooth, PRICE_CLOSE, 0, 1) 
         > sqIndicatorHighest(DivergencePeriod, 0, "sqMACD(...)"))
    && (iLow(NULL,0, 1) < sqIndicatorLowest(DivergencePeriod, 0, "iLow(...)"))
    && (sqIsUptrend(NULL, PERIOD_H4, 0)))) { // 添加 H4 趨勢確認
    
    _ticket = sqOpenOrder(OP_BUY, ...);
}
```

#### 範例 2：添加成交量確認
```mql4
// 添加成交量指標確認
if (sqIsBarOpen()
    && ((sqMACD(...) > sqIndicatorHighest(...))
    && (iLow(...) < sqIndicatorLowest(...))
    && (iVolume(NULL, 0, 1) > iMA(NULL, 0, 20, 0, MODE_SMA, PRICE_VOLUME, 1)))) { // 成交量確認
    
    _ticket = sqOpenOrder(OP_BUY, ...);
}
```

#### 範例 3：添加資金管理
```mql4
// 修改固定手數為動態手數
extern double RiskPercentage = 2.0; // 每筆交易風險百分比

// 在 sqOpenOrder 調用前計算動態手數
double dynamicLots = calculateLotSize(RiskPercentage, StopLoss);
_ticket = sqOpenOrder(OP_BUY, "Current", dynamicLots, 0, MagicNumber, "", 0, false, false, CLR_NONE);

// 新增動態手數計算函數
double calculateLotSize(double riskPercent, int stopLossPips) {
    double accountBalance = AccountBalance();
    double riskAmount = accountBalance * riskPercent / 100;
    double pipValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    
    if(Digits == 3 || Digits == 5) {
        pipValue *= 10;
    }
    
    double lotSize = riskAmount / (stopLossPips * pipValue);
    
    // 限制手數範圍
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    
    if(lotSize < minLot) lotSize = minLot;
    if(lotSize > maxLot) lotSize = maxLot;
    
    return NormalizeDouble(lotSize, 2);
}
```

## 常見問題解決

### 問題 1：編譯錯誤
**症狀**: 修改後無法編譯  
**解決方案**:
1. 檢查語法錯誤（缺少分號、括號不匹配）
2. 確認變數名稱拼寫正確
3. 檢查函數參數類型是否匹配

### 問題 2：EA 不執行交易
**症狀**: EA 載入正常但不開倉  
**解決方案**:
1. 檢查 `openingOrdersAllowed` 變數狀態
2. 確認交易條件是否過於嚴格
3. 檢查 `sqIsBarOpen()` 是否正常工作
4. 驗證技術指標計算是否正確

### 問題 3：訂單管理異常
**症狀**: 止損止盈設定失敗  
**解決方案**:
1. 檢查經紀商是否允許設定止損止盈
2. 確認價格計算是否正確
3. 檢查 OrderReliable 庫是否正常工作

### 問題 4：性能問題
**症狀**: EA 運行緩慢或卡頓  
**解決方案**:
1. 減少技術指標計算頻率
2. 優化循環邏輯
3. 避免在每個 Tick 執行重複計算

## 性能調優建議

### 1. 減少計算頻率
```mql4
// 將計算移到新K線時執行
static datetime lastBarTime = 0;
if(Time[0] != lastBarTime) {
    // 執行重計算
    lastBarTime = Time[0];
}
```

### 2. 快取計算結果
```mql4
// 快取技術指標值
static double cachedMACD = 0;
static datetime lastMACDTime = 0;

if(Time[0] != lastMACDTime) {
    cachedMACD = sqMACD(NULL, 0, MACDFast, MACDSlow, MACDSmooth, PRICE_CLOSE, 0, 1);
    lastMACDTime = Time[0];
}
```

### 3. 優化訂單循環
```mql4
// 使用反向循環避免索引問題
for(int i = OrdersTotal() - 1; i >= 0; i--) {
    if(!OrderSelect(i, SELECT_BY_POS)) continue;
    // 處理邏輯
}
```

### 4. 記憶體管理
```mql4
// 及時釋放不需要的物件
void OnDeinit(const int reason) {
    if(SQTime != NULL) {
        delete SQTime;
        SQTime = NULL;
    }
}
```

---

**使用建議**: 建議在修改前充分理解每個模組的功能和依賴關係，並在測試環境中驗證修改效果。

**技術支援**: 如遇到問題，請參考完整的模組化文檔或聯繫開發者。

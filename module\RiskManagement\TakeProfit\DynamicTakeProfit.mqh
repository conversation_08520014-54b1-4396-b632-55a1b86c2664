//+------------------------------------------------------------------+
//|                                         DynamicTakeProfit.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef DYNAMIC_TAKE_PROFIT_MQH
#define DYNAMIC_TAKE_PROFIT_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Take Profit Type Enumeration                                     |
//+------------------------------------------------------------------+
enum ENUM_TAKE_PROFIT_TYPE
{
    TP_NONE = 0,            // No take profit
    TP_FIXED_POINTS = 1,    // Fixed points take profit
    TP_RISK_REWARD = 2,     // Risk-reward ratio based
    TP_ATR_BASED = 3,       // ATR-based take profit
    TP_SUPPORT_RESISTANCE = 4, // Support/Resistance based
    TP_PARTIAL = 5          // Partial take profit
};

//+------------------------------------------------------------------+
//| DynamicTakeProfit Class                                          |
//| Implementation of dynamic take profit management                |
//+------------------------------------------------------------------+
class DynamicTakeProfit : public BaseComponent
{
private:
    string                  m_symbol;           // Trading symbol
    ENUM_TIMEFRAMES         m_timeframe;        // Chart timeframe
    ENUM_TAKE_PROFIT_TYPE   m_takeProfitType;   // Take profit type
    double                  m_fixedPoints;      // Fixed points for TP
    double                  m_riskRewardRatio;  // Risk-reward ratio
    int                     m_atrPeriod;        // ATR period
    double                  m_atrMultiplier;    // ATR multiplier
    double                  m_partialPercent;   // Partial TP percentage
    double                  m_partialRatio;     // Partial TP ratio

public:
    //--- Constructor and Destructor
                            DynamicTakeProfit(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                             ENUM_TAKE_PROFIT_TYPE type = TP_FIXED_POINTS);
    virtual                ~DynamicTakeProfit();
    
    //--- Configuration methods
    void                    SetSymbol(string symbol) { m_symbol = symbol; }
    void                    SetTimeframe(ENUM_TIMEFRAMES timeframe) { m_timeframe = timeframe; }
    void                    SetTakeProfitType(ENUM_TAKE_PROFIT_TYPE type) { m_takeProfitType = type; }
    void                    SetFixedPoints(double points) { m_fixedPoints = MathMax(0.0, points); }
    void                    SetRiskRewardRatio(double ratio) { m_riskRewardRatio = MathMax(0.1, ratio); }
    void                    SetATRParameters(int period, double multiplier);
    void                    SetPartialParameters(double percent, double ratio);
    
    //--- Information methods
    string                  GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES         GetTimeframe() const { return m_timeframe; }
    ENUM_TAKE_PROFIT_TYPE   GetTakeProfitType() const { return m_takeProfitType; }
    double                  GetFixedPoints() const { return m_fixedPoints; }
    double                  GetRiskRewardRatio() const { return m_riskRewardRatio; }
    
    //--- Take profit calculation methods
    double                  CalculateTakeProfit(int orderType, double entryPrice, double stopLoss = 0.0, int shift = 0);
    double                  CalculateFixedTakeProfit(int orderType, double entryPrice);
    double                  CalculateRiskRewardTakeProfit(int orderType, double entryPrice, double stopLoss);
    double                  CalculateATRTakeProfit(int orderType, double entryPrice, int shift = 0);
    double                  CalculateSupportResistanceTakeProfit(int orderType, double entryPrice, int shift = 0);
    
    //--- Partial take profit methods
    bool                    ShouldTakePartialProfit(int orderType, double entryPrice, double currentPrice);
    double                  CalculatePartialTakeProfitLevel(int orderType, double entryPrice, double stopLoss);
    double                  CalculatePartialLotSize(double originalLotSize);
    
    //--- Validation methods
    bool                    IsValidTakeProfit(int orderType, double entryPrice, double takeProfit);
    double                  NormalizeTakeProfit(int orderType, double entryPrice, double takeProfit);
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    
    //--- Utility methods
    double                  GetATR(int shift = 0);
    double                  FindSupport(int shift = 0, int lookback = 20);
    double                  FindResistance(int shift = 0, int lookback = 20);
    double                  CalculateRiskDistance(double entryPrice, double stopLoss);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
DynamicTakeProfit::DynamicTakeProfit(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                    ENUM_TAKE_PROFIT_TYPE type = TP_FIXED_POINTS) : BaseComponent("DynamicTakeProfit")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_takeProfitType = type;
    m_fixedPoints = 100.0;
    m_riskRewardRatio = 2.0;
    m_atrPeriod = 14;
    m_atrMultiplier = 3.0;
    m_partialPercent = 50.0;
    m_partialRatio = 1.0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
DynamicTakeProfit::~DynamicTakeProfit()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize dynamic take profit                                   |
//+------------------------------------------------------------------+
bool DynamicTakeProfit::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(801, "Invalid symbol for take profit calculation");
        return false;
    }
    
    if (m_takeProfitType == TP_ATR_BASED && m_atrPeriod < 1)
    {
        SetError(802, "Invalid ATR period");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool DynamicTakeProfit::OnValidate()
{
    if (m_fixedPoints < 0.0)
    {
        SetError(803, "Fixed points must be non-negative");
        return false;
    }
    
    if (m_riskRewardRatio <= 0.0)
    {
        SetError(804, "Risk-reward ratio must be positive");
        return false;
    }
    
    if (m_atrMultiplier <= 0.0)
    {
        SetError(805, "ATR multiplier must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set ATR parameters                                               |
//+------------------------------------------------------------------+
void DynamicTakeProfit::SetATRParameters(int period, double multiplier)
{
    m_atrPeriod = MathMax(1, period);
    m_atrMultiplier = MathMax(0.1, multiplier);
}

//+------------------------------------------------------------------+
//| Set partial take profit parameters                              |
//+------------------------------------------------------------------+
void DynamicTakeProfit::SetPartialParameters(double percent, double ratio)
{
    m_partialPercent = MathMax(10.0, MathMin(90.0, percent));
    m_partialRatio = MathMax(0.5, ratio);
}

//+------------------------------------------------------------------+
//| Calculate take profit based on type                             |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateTakeProfit(int orderType, double entryPrice, double stopLoss = 0.0, int shift = 0)
{
    double takeProfit = 0.0;
    
    switch(m_takeProfitType)
    {
        case TP_NONE:
            return 0.0;
            
        case TP_FIXED_POINTS:
            takeProfit = CalculateFixedTakeProfit(orderType, entryPrice);
            break;
            
        case TP_RISK_REWARD:
            takeProfit = CalculateRiskRewardTakeProfit(orderType, entryPrice, stopLoss);
            break;
            
        case TP_ATR_BASED:
            takeProfit = CalculateATRTakeProfit(orderType, entryPrice, shift);
            break;
            
        case TP_SUPPORT_RESISTANCE:
            takeProfit = CalculateSupportResistanceTakeProfit(orderType, entryPrice, shift);
            break;
            
        case TP_PARTIAL:
            takeProfit = CalculatePartialTakeProfitLevel(orderType, entryPrice, stopLoss);
            break;
            
        default:
            takeProfit = CalculateFixedTakeProfit(orderType, entryPrice);
            break;
    }
    
    return NormalizeTakeProfit(orderType, entryPrice, takeProfit);
}

//+------------------------------------------------------------------+
//| Calculate fixed points take profit                              |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateFixedTakeProfit(int orderType, double entryPrice)
{
    if (entryPrice <= 0.0 || m_fixedPoints <= 0.0)
        return 0.0;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    if (point <= 0.0)
        return 0.0;
    
    if (orderType == OP_BUY)
    {
        return entryPrice + (m_fixedPoints * point);
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice - (m_fixedPoints * point);
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate risk-reward based take profit                         |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateRiskRewardTakeProfit(int orderType, double entryPrice, double stopLoss)
{
    if (entryPrice <= 0.0 || stopLoss <= 0.0)
        return CalculateFixedTakeProfit(orderType, entryPrice);
    
    double riskDistance = CalculateRiskDistance(entryPrice, stopLoss);
    if (riskDistance <= 0.0)
        return CalculateFixedTakeProfit(orderType, entryPrice);
    
    double rewardDistance = riskDistance * m_riskRewardRatio;
    
    if (orderType == OP_BUY)
    {
        return entryPrice + rewardDistance;
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice - rewardDistance;
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based take profit                                 |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateATRTakeProfit(int orderType, double entryPrice, int shift = 0)
{
    if (entryPrice <= 0.0)
        return 0.0;
    
    double atr = GetATR(shift);
    if (atr <= 0.0)
        return CalculateFixedTakeProfit(orderType, entryPrice);
    
    double profitDistance = atr * m_atrMultiplier;
    
    if (orderType == OP_BUY)
    {
        return entryPrice + profitDistance;
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice - profitDistance;
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate support/resistance based take profit                  |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateSupportResistanceTakeProfit(int orderType, double entryPrice, int shift = 0)
{
    if (entryPrice <= 0.0)
        return 0.0;
    
    double point = MarketInfo(m_symbol, MODE_POINT);
    double buffer = 10.0 * point; // Small buffer before support/resistance
    
    if (orderType == OP_BUY)
    {
        double resistance = FindResistance(shift);
        if (resistance > 0.0 && resistance > entryPrice)
        {
            return resistance - buffer;
        }
    }
    else if (orderType == OP_SELL)
    {
        double support = FindSupport(shift);
        if (support > 0.0 && support < entryPrice)
        {
            return support + buffer;
        }
    }
    
    // Fallback to fixed take profit
    return CalculateFixedTakeProfit(orderType, entryPrice);
}

//+------------------------------------------------------------------+
//| Check if partial take profit should be triggered                |
//+------------------------------------------------------------------+
bool DynamicTakeProfit::ShouldTakePartialProfit(int orderType, double entryPrice, double currentPrice)
{
    if (entryPrice <= 0.0 || currentPrice <= 0.0)
        return false;
    
    double partialLevel = CalculatePartialTakeProfitLevel(orderType, entryPrice, 0.0);
    if (partialLevel <= 0.0)
        return false;
    
    if (orderType == OP_BUY)
    {
        return (currentPrice >= partialLevel);
    }
    else if (orderType == OP_SELL)
    {
        return (currentPrice <= partialLevel);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Calculate partial take profit level                             |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculatePartialTakeProfitLevel(int orderType, double entryPrice, double stopLoss)
{
    double fullTakeProfit = CalculateRiskRewardTakeProfit(orderType, entryPrice, stopLoss);
    if (fullTakeProfit <= 0.0)
        fullTakeProfit = CalculateFixedTakeProfit(orderType, entryPrice);
    
    if (fullTakeProfit <= 0.0)
        return 0.0;
    
    double partialDistance = MathAbs(fullTakeProfit - entryPrice) * (m_partialRatio / m_riskRewardRatio);
    
    if (orderType == OP_BUY)
    {
        return entryPrice + partialDistance;
    }
    else if (orderType == OP_SELL)
    {
        return entryPrice - partialDistance;
    }
    
    return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate partial lot size                                       |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculatePartialLotSize(double originalLotSize)
{
    if (originalLotSize <= 0.0)
        return 0.0;
    
    double partialLot = originalLotSize * (m_partialPercent / 100.0);
    
    // Normalize to valid lot size
    double lotStep = MarketInfo(m_symbol, MODE_LOTSTEP);
    if (lotStep > 0.0)
    {
        partialLot = MathRound(partialLot / lotStep) * lotStep;
    }
    
    double minLot = MarketInfo(m_symbol, MODE_MINLOT);
    return MathMax(minLot, partialLot);
}

//+------------------------------------------------------------------+
//| Validate take profit level                                      |
//+------------------------------------------------------------------+
bool DynamicTakeProfit::IsValidTakeProfit(int orderType, double entryPrice, double takeProfit)
{
    if (entryPrice <= 0.0 || takeProfit <= 0.0)
        return false;
    
    if (orderType == OP_BUY && takeProfit <= entryPrice)
        return false;
    
    if (orderType == OP_SELL && takeProfit >= entryPrice)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Normalize take profit to valid level                            |
//+------------------------------------------------------------------+
double DynamicTakeProfit::NormalizeTakeProfit(int orderType, double entryPrice, double takeProfit)
{
    if (takeProfit <= 0.0)
        return 0.0;
    
    if (!IsValidTakeProfit(orderType, entryPrice, takeProfit))
    {
        // Fallback to fixed take profit
        return CalculateFixedTakeProfit(orderType, entryPrice);
    }
    
    return NormalizeDouble(takeProfit, Digits);
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double DynamicTakeProfit::GetATR(int shift = 0)
{
    return iATR(m_symbol, m_timeframe, m_atrPeriod, shift);
}

//+------------------------------------------------------------------+
//| Find support level                                              |
//+------------------------------------------------------------------+
double DynamicTakeProfit::FindSupport(int shift = 0, int lookback = 20)
{
    double minLow = iLow(m_symbol, m_timeframe, shift);
    
    for (int i = 1; i < lookback; i++)
    {
        double low = iLow(m_symbol, m_timeframe, shift + i);
        if (low < minLow)
            minLow = low;
    }
    
    return minLow;
}

//+------------------------------------------------------------------+
//| Find resistance level                                           |
//+------------------------------------------------------------------+
double DynamicTakeProfit::FindResistance(int shift = 0, int lookback = 20)
{
    double maxHigh = iHigh(m_symbol, m_timeframe, shift);
    
    for (int i = 1; i < lookback; i++)
    {
        double high = iHigh(m_symbol, m_timeframe, shift + i);
        if (high > maxHigh)
            maxHigh = high;
    }
    
    return maxHigh;
}

//+------------------------------------------------------------------+
//| Calculate risk distance                                          |
//+------------------------------------------------------------------+
double DynamicTakeProfit::CalculateRiskDistance(double entryPrice, double stopLoss)
{
    if (entryPrice <= 0.0 || stopLoss <= 0.0)
        return 0.0;
    
    return MathAbs(entryPrice - stopLoss);
}

#endif // DYNAMIC_TAKE_PROFIT_MQH

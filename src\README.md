# EA_Wizard-dev2 Source Code Documentation

[![MQL4](https://img.shields.io/badge/MQL4-Expert%20Advisor-blue.svg)](https://www.mql5.com/en/docs/languages/mql4)
[![EA_Wizard](https://img.shields.io/badge/Framework-EA__Wizard-green.svg)](../README.md)
[![Status](https://img.shields.io/badge/Status-Development-yellow.svg)]()

## Table of Contents

- [Introduction](#introduction)
- [Architecture Overview](#architecture-overview)
- [Directory Structure](#directory-structure)
- [Core Components](#core-components)
- [Configuration System](#configuration-system)
- [Trading Strategy](#trading-strategy)
- [API Documentation](#api-documentation)
- [Development Guidelines](#development-guidelines)
- [Examples](#examples)
- [Contributing](#contributing)

## Introduction

This directory contains the source code for **EA_Wizard-dev2**, a sophisticated Expert Advisor implementing a Martingale trading strategy with technical indicator signals. The project is built using the EA_Wizard framework architecture, providing a modular and maintainable codebase.

### Key Features

- **Martingale Strategy**: 4-level position scaling with fixed lot sequence [0.01, 0.01, 0.02, 0.04]
- **Technical Indicators**: Bollinger Bands, MACD, and RSI for signal generation
- **Risk Management**: Account balance protection and position limits
- **Modular Architecture**: Organized pipeline-based structure following EA_Wizard standards

## Architecture Overview

The EA_Wizard-dev2 follows the standardized EA_Wizard framework architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   StandardEA    │────│ TradingController│────│  MainPipeline   │
│   (Main Entry)  │    │   (Framework)    │    │   (Framework)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ├── Config/     (Configuration Management)
         ├── OnInit/     (Initialization Pipeline)
         ├── OnTick/     (Tick Processing Pipeline)
         └── OnDeinit/   (Cleanup Pipeline)
```

## Directory Structure

```
src/
├── Config/                     # Configuration System
│   ├── index.mqh              # Configuration entry point
│   ├── Config.mqh             # Configuration management
│   └── Input.mqh              # Input parameters
├── OnInit/                     # Initialization Pipeline
│   └── index.mqh              # Initialization entry point
├── OnTick/                     # Tick Processing Pipeline
│   └── index.mqh              # Tick processing entry point
├── OnDeinit/                   # Cleanup Pipeline
│   └── index.mqh              # Cleanup entry point
├── StandardEA.mqh             # Main EA implementation
└── README.md                  # This documentation
```

### Directory Standards

1. **index.mqh Files**: Each directory contains an `index.mqh` file serving as the unified entry point
2. **Single Responsibility**: Each directory handles a specific aspect of the EA lifecycle
3. **Modular Design**: Related functionality is compressed into single `.mqh` files per directory
4. **Framework Integration**: All components integrate with EA_Wizard framework components

## Core Components

### StandardEA.mqh

The main Expert Advisor file implementing the EA_Wizard-dev2 class:

- **EA_Wizard_dev2 Class**: Main EA logic container
- **Lifecycle Management**: Handles initialization, tick processing, and cleanup
- **Framework Integration**: Integrates with TradingController and MainPipeline
- **Global Functions**: Provides MQL4 standard functions (OnInit, OnTick, OnDeinit)

### Configuration System (Config/)

Manages all EA configuration and input parameters:

- **Input Parameters**: Magic number, spread control, risk settings
- **Strategy Parameters**: Martingale levels, lot sequence, indicator settings
- **Validation**: Parameter validation and constraint checking

### Pipeline Components

- **OnInit/**: Initialization logic and setup procedures
- **OnTick/**: Real-time tick processing and trading logic
- **OnDeinit/**: Resource cleanup and shutdown procedures

## Configuration System

### Input Parameters Structure

```mql4
// Basic Settings
extern int    MagicNumber = 12345;        // EA identification
extern int    MaxSpread = 5;              // Maximum allowed spread
extern int    Slippage = 3;               // Slippage tolerance

// Martingale Settings
extern int    MartingaleLevels = 4;       // Fixed: 4 levels
extern int    LossPoints = 300;           // Trigger points for next level
extern string LotSequence = "0.01,0.01,0.02,0.04"; // Fixed sequence

// Risk Management
extern double MaxLossPercent = 20.0;      // Maximum account loss %
extern int    MaxOrders = 20;             // Maximum concurrent orders

// Technical Indicators
extern int    BB_Period = 20;             // Bollinger Bands period
extern double BB_Deviation = 2.0;         // Bollinger Bands deviation
extern int    MACD_Fast = 12;             // MACD fast EMA
extern int    MACD_Slow = 26;             // MACD slow EMA
extern int    MACD_Signal = 9;            // MACD signal line
extern int    RSI_Period = 14;            // RSI period
extern int    RSI_Overbought = 70;        // RSI overbought level
extern int    RSI_Oversold = 30;          // RSI oversold level
```

## Trading Strategy

### Martingale Implementation

**Level Configuration:**

- Level 1: 0.01 lots → Loss 300 points triggers Level 2
- Level 2: 0.01 lots → Loss 300 points triggers Level 3
- Level 3: 0.02 lots → Loss 300 points triggers Level 4
- Level 4: 0.04 lots → Final level (no further scaling)

**Take Profit Calculation:**

- 0.01 lots → 300 points take profit
- 0.02 lots → 600 points take profit
- 0.04 lots → 1200 points take profit

### Signal Generation

**Buy Signal Conditions:**

- RSI < 30 (oversold)
- Price touches Bollinger lower band
- MACD line crosses above signal line below zero

**Sell Signal Conditions:**

- RSI > 70 (overbought)
- Price touches Bollinger upper band
- MACD line crosses below signal line above zero

### Risk Management

**Account Protection:**

- Maximum loss: 20% of account balance
- Check frequency: Every OnTick()
- Action: Stop new positions, maintain existing

**Position Limits:**

- Maximum concurrent orders: 20
- Check timing: Before opening positions
- Overflow handling: Pause trading signals

## API Documentation

### Main EA Class Methods

```mql4
class EA_Wizard_dev2
{
public:
    int Initialize();                    // Initialize EA components
    void OnTick();                      // Process tick events
    void OnDeinit(const int reason);    // Handle deinitialization
    bool IsInitialized() const;         // Check initialization status
    bool IsActive() const;              // Check active status
    void SetActive(bool active);        // Set active state
};
```

### Pipeline Integration Functions

```mql4
// Configuration Management
bool InitializeConfigManager();
void CleanupConfigManager();

// OnInit Pipeline
int InitializeOnInitPipeline();
void CleanupOnInitPipeline();

// OnTick Pipeline
bool InitializeOnTickPipeline();
void ProcessOnTickPipeline();
void CleanupOnTickPipeline();

// OnDeinit Pipeline
bool InitializeOnDeinitPipeline();
void ProcessOnDeinitPipeline(const int reason);
void CleanupOnDeinitPipeline();
```

## Development Guidelines

### Code Organization

1. **Follow EA_Wizard Standards**: Adhere to framework guidelines and patterns
2. **Single File Per Directory**: Compress related functionality into single `.mqh` files
3. **Index Files**: Use `index.mqh` as unified entry points for each directory
4. **Modular Design**: Implement independent modules with clear interfaces

### Naming Conventions

- **Classes**: PascalCase (e.g., `EA_Wizard_dev2`)
- **Methods**: PascalCase (e.g., `Initialize()`)
- **Variables**: camelCase with prefix (e.g., `m_controller`)
- **Constants**: UPPER_CASE (e.g., `MAX_SPREAD`)

### Error Handling

- Use MQL4 standard return codes (`INIT_SUCCEEDED`, `INIT_FAILED`)
- Implement comprehensive logging with `Print()` statements
- Validate all inputs and handle edge cases gracefully

## Examples

### Basic EA Usage

```mql4
// Create and initialize EA
EA_Wizard_dev2* ea = new EA_Wizard_dev2();
int result = ea.Initialize();

if(result == INIT_SUCCEEDED)
{
    Print("EA initialized successfully");
}
```

### Configuration Access

```mql4
// Access configuration parameters
int magicNumber = GetMagicNumber();
double maxLoss = GetMaxLossPercent();
```

## Contributing

### Development Process

1. **Read Framework Documentation**: Review EA_Wizard framework README.md
2. **Follow Directory Structure**: Maintain standardized organization
3. **Test Thoroughly**: Implement comprehensive testing for all components
4. **Document Changes**: Update documentation for any modifications

### Code Quality Standards

- **Consistent Formatting**: Follow MQL4 coding standards
- **Comprehensive Comments**: Document all public interfaces
- **Error Handling**: Implement robust error checking
- **Performance**: Optimize for real-time trading requirements

---

**Framework**: EA_Wizard  
**Version**: 1.00  
**Copyright**: 2024, EA_Wizard Framework  
**License**: See project root LICENSE file

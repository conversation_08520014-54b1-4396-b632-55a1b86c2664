# OrderExecutor Module

## 概述

OrderExecutor 是為 EA_Wizard MQL4 交易框架設計的簡單直接的訂單執行模組。該模組專注於提供可靠的基本訂單操作功能，包括開倉、平倉和修改訂單，並採用佇列緩衝系統來處理訂單請求。

## 專案結構

```
OrderExecutor/
├── src/                        # 源代碼目錄
│   ├── OnInit/                 # 初始化模組
│   │   └── index.mqh          # OnInit 統一入口點
│   ├── OnTick/                 # Tick 處理模組
│   │   └── index.mqh          # OnTick 統一入口點
│   ├── OnDeinit/               # 清理模組
│   │   └── index.mqh          # OnDeinit 統一入口點
│   ├── Config/                 # 配置模組
│   │   └── index.mqh          # Config 統一入口點
│   └── OrderExecutor.mqh      # 主要模組文件
├── .taskmaster/               # Task Master 專案管理
│   ├── docs/                  # 專案文檔
│   │   └── prd.txt           # 產品需求文檔
│   └── tasks/                 # 任務管理
└── README.md                  # 本文檔
```

## 核心功能

### 1. 訂單開倉 (Order Opening)
- 實現 BUY 和 SELL 市價單執行功能
- 提供基本錯誤處理機制
- 支援標準的手數、止損和止盈參數

### 2. 訂單平倉 (Order Closing)
- 提供完整倉位平倉功能
- 支援部分倉位平倉操作
- 實現按票號平倉和按條件平倉

### 3. 訂單修改 (Order Modification)
- 修改現有訂單的止損價位
- 修改現有訂單的止盈價位
- 調整掛單的開倉價格

### 4. 佇列緩衝系統 (Queue Buffer System)
- 實現 FIFO 佇列資料結構管理訂單請求
- 提供非同步訂單處理機制
- 確保執行緒安全的佇列操作

## 技術架構

### 框架整合
- 遵循 EA_Wizard 框架標準和慣例
- 整合現有 EAOrderManager 和 mql4-lib 組件
- 相容 OrderTracker 和 OrderGroup 系統

### 設計原則
- 保持實現簡單和可維護
- 專注於可靠性而非高級功能
- 使用 MQL4/MQL5 語法和堆積分配模式
- 遵循單一職責原則進行組件設計

## 開發狀態

目前專案處於初始設置階段，基本目錄結構已建立。後續開發將按照 Task Master 管理的任務順序進行：

1. ✅ **Task 1**: Setup Project Repository (進行中)
2. ⏳ **Task 2**: Implement OrderRequest Data Model
3. ⏳ **Task 3**: Implement ExecutionResult Data Model
4. ⏳ **Task 4**: Implement Basic FIFO Queue Structure
5. ⏳ **Task 5**: Integrate EAOrderManager Component

## 使用方法

此模組設計為 EA_Wizard 框架的組件，將通過框架的標準整合模式進行使用。具體使用方法將在後續開發階段完善。

## 文檔

- [產品需求文檔 (PRD)](.taskmaster/docs/prd.txt) - 詳細的功能需求和技術規格
- [任務管理](.taskmaster/tasks/) - Task Master 任務追蹤和管理

## 貢獻

本專案遵循 EA_Wizard 框架的開發標準和最佳實踐。所有代碼應包含繁體中文註釋和文檔。

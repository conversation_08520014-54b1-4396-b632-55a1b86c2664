//+------------------------------------------------------------------+
//|                                         TradingResultModels.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef TRADING_RESULT_MODELS_MQH
#define TRADING_RESULT_MODELS_MQH

//+------------------------------------------------------------------+
//| Trading Result Model Classes                                     |
//| Data containers for trading operation results                   |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Base Result Class                                                |
//| Implements common functionality for all result types            |
//+------------------------------------------------------------------+
class BaseResult
{
protected:
    bool              m_success;          // Operation success
    int               m_ticket;           // Order ticket
    string            m_errorMessage;     // Error message if failed
    int               m_errorCode;        // Error code if failed

public:
    //--- Constructor
    BaseResult(bool success, int ticket, string errorMessage, int errorCode)
        : m_success(success), m_ticket(ticket), m_errorMessage(errorMessage), m_errorCode(errorCode) {}

    //--- Virtual Destructor
    virtual ~BaseResult() {}

    //--- Common interface implementation
    bool            IsSuccess() const { return m_success; }
    int             GetTicket() const { return m_ticket; }
    string          GetErrorMessage() const { return m_errorMessage; }
    int             GetErrorCode() const { return m_errorCode; }

    //--- Default ToString implementation (can be overridden)
    virtual string  ToString() const
    {
        return StringFormat("Success: %s, Ticket: %d, Error: %s (%d)",
                           m_success ? "true" : "false", m_ticket, m_errorMessage, m_errorCode);
    }
};

//+------------------------------------------------------------------+
//| Order Result                                                     |
//+------------------------------------------------------------------+
class OrderResult : public BaseResult
{
private:
    double            m_price;            // Execution price
    double            m_lotSize;          // Executed lot size

public:
    //--- Constructor
    OrderResult(bool success, int ticket, double price, double lotSize, string errorMessage, int errorCode)
        : BaseResult(success, ticket, errorMessage, errorCode), m_price(price), m_lotSize(lotSize) {}

    //--- Specific getters
    double          GetPrice() const { return m_price; }
    double          GetLotSize() const { return m_lotSize; }

    //--- Override ToString for specific formatting
    virtual string ToString() const override
    {
        return StringFormat("Success: %s, Ticket: %d, Price: %g, LotSize: %g, Error: %s (%d)",
                           IsSuccess() ? "true" : "false", GetTicket(), m_price, m_lotSize,
                           GetErrorMessage(), GetErrorCode());
    }
};

//+------------------------------------------------------------------+
//| Modify Result                                                    |
//+------------------------------------------------------------------+
class ModifyResult : public BaseResult
{
private:
    double            m_newStopLoss;      // New stop loss value
    double            m_newTakeProfit;    // New take profit value
    double            m_newPrice;         // New price (for pending orders)

public:
    //--- Constructor
    ModifyResult(bool success, int ticket, double newStopLoss, double newTakeProfit, double newPrice, string errorMessage, int errorCode)
        : BaseResult(success, ticket, errorMessage, errorCode),
          m_newStopLoss(newStopLoss), m_newTakeProfit(newTakeProfit), m_newPrice(newPrice) {}

    //--- Specific getters
    double          GetNewStopLoss() const { return m_newStopLoss; }
    double          GetNewTakeProfit() const { return m_newTakeProfit; }
    double          GetNewPrice() const { return m_newPrice; }

    //--- Override ToString for specific formatting
    virtual string ToString() const override
    {
        return StringFormat("Success: %s, Ticket: %d, SL: %g, TP: %g, Price: %g, Error: %s (%d)",
                           IsSuccess() ? "true" : "false", GetTicket(), m_newStopLoss, m_newTakeProfit,
                           m_newPrice, GetErrorMessage(), GetErrorCode());
    }
};

//+------------------------------------------------------------------+
//| Closure Result                                                   |
//+------------------------------------------------------------------+
class ClosureResult : public BaseResult
{
private:
    double            m_closedLots;       // Amount of lots closed
    double            m_closurePrice;     // Price at which order was closed
    double            m_profit;           // Profit/loss from closure
    datetime          m_closureTime;      // Time when order was closed

public:
    //--- Constructor
    ClosureResult(bool success, int ticket, double closedLots, double closurePrice, double profit, datetime closureTime, string errorMessage, int errorCode)
        : BaseResult(success, ticket, errorMessage, errorCode),
          m_closedLots(closedLots), m_closurePrice(closurePrice), m_profit(profit), m_closureTime(closureTime) {}

    //--- Specific getters
    double          GetClosedLots() const { return m_closedLots; }
    double          GetClosurePrice() const { return m_closurePrice; }
    double          GetProfit() const { return m_profit; }
    datetime        GetClosureTime() const { return m_closureTime; }

    //--- Override ToString for specific formatting
    virtual string ToString() const override
    {
        return StringFormat("Success: %s, Ticket: %d, Lots: %g, Price: %g, Profit: %g, Time: %s, Error: %s (%d)",
                           IsSuccess() ? "true" : "false", GetTicket(), m_closedLots, m_closurePrice,
                           m_profit, TimeToString(m_closureTime), GetErrorMessage(), GetErrorCode());
    }
};

#endif // TRADING_RESULT_MODELS_MQH

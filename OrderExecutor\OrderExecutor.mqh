#include "../mql4-lib/Trade/OrderManager.mqh"
#include "Model/TradingRequest.mqh"

//+------------------------------------------------------------------+
//| OrderExecutor 類別 - 訂單執行器                                  |
//| 提供單一和批次訂單操作功能                                       |
//+------------------------------------------------------------------+
class OrderExecutor : public OrderManager
{
private:
    //--- 驗證請求細節
    static bool     ValidateCloseRequest(CloseRequest* request, string symbol);
    static bool     ValidateOrderRequest(OrderRequest* request, string symbol);
    static bool     ValidateModifyRequest(ModifyRequest* request, string symbol);

    template <typename Request>
    static bool     ValidateBatchTradingRequest(BatchTradeingRequest<Request>* request);
    static bool     ValidateBatchOrderRequest(BatchOrderRequest* request);
    static bool     ValidateBatchModifyRequest(BatchModifyRequest* request);
    static bool     ValidateBatchCloseRequest(BatchCloseRequest* request);

private:
    string                   m_symbol;

public:
    //--- 構造函數
    OrderExecutor(string symbol);

    //--- 單一訂單操作方法
    TradingResult*            Market(OrderRequest* request);
    TradingResult*            Modify(ModifyRequest* request);
    TradingResult*            Close(CloseRequest* request);

    //--- 批次訂單操作方法
    Vector<TradingResult*>*   BatchMarket(BatchOrderRequest* requests);
    Vector<TradingResult*>*   BatchModify(BatchModifyRequest* requests);
    Vector<TradingResult*>*   BatchClose(BatchCloseRequest* requests);
};

//+------------------------------------------------------------------+
//| 構造函數                                                         |
//+------------------------------------------------------------------+
OrderExecutor::OrderExecutor(string symbol) : OrderManager(symbol)
{
    m_symbol = symbol;
}

//+------------------------------------------------------------------+
//| 驗證訂單請求                                                     |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateOrderRequest(OrderRequest* request, string symbol)
{
    if (request == NULL)
        return false;

    // 檢查操作類型
    int op = request.GetOp();
    if (op != OP_BUY && op != OP_SELL)
        return false;

    // 檢查手數
    double lots = request.GetLots();
    double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

    if (lots < minLot || lots > maxLot)
        return false;

    // 檢查手數是否符合步長
    if (MathMod(lots, lotStep) != 0.0)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| 驗證修改請求                                                     |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateModifyRequest(ModifyRequest* request, string symbol)
{
    if (request == NULL)
        return false;

    int ticket = request.GetTicket();
    if (ticket <= 0)
        return false;

    // 檢查訂單是否存在
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;

    // 檢查訂單符號是否匹配
    if (OrderSymbol() != symbol)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| 驗證關閉請求                                                     |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateCloseRequest(CloseRequest* request, string symbol)
{
    if (request == NULL)
        return false;

    int ticket = request.GetTicket();
    if (ticket <= 0)
        return false;

    // 檢查訂單是否存在
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;

    // 檢查訂單符號是否匹配
    if (OrderSymbol() != symbol)
        return false;

    // 檢查訂單是否為市價單
    int orderType = OrderType();
    if (orderType != OP_BUY && orderType != OP_SELL)
        return false;

    // 如果是部分平倉，檢查手數
    if (request.IsPartialClose())
    {
        double closeLots = request.GetLots();
        double orderLots = OrderLots();

        if (closeLots <= 0.0 || closeLots >= orderLots)
            return false;

        double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
        double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

        if (closeLots < minLot)
            return false;

        if (MathMod(closeLots, lotStep) != 0.0)
            return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證批次交易請求                                                 |
//+------------------------------------------------------------------+
template <typename Request>
static bool OrderExecutor::ValidateBatchTradingRequest(BatchTradeingRequest<Request>* request)
{
    if (request == NULL)
        return false;

    if (request.GetRequestCount() <= 0)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| 驗證批次訂單請求                                                 |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchOrderRequest(BatchOrderRequest* request)
{
    if (!ValidateBatchTradingRequest<OrderRequest*>(request))
        return false;

    // 檢查每個訂單請求
    for (int i = 0; i < request.GetRequestCount(); i++)
    {
        OrderRequest* orderReq = request.GetRequest(i);
        if (!ValidateOrderRequest(orderReq, ""))  // 符號將在執行時檢查
            return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證批次修改請求                                                 |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchModifyRequest(BatchModifyRequest* request)
{
    if (!ValidateBatchTradingRequest<ModifyRequest*>(request))
        return false;

    // 檢查每個修改請求
    for (int i = 0; i < request.GetRequestCount(); i++)
    {
        ModifyRequest* modifyReq = request.GetRequest(i);
        if (!ValidateModifyRequest(modifyReq, ""))  // 符號將在執行時檢查
            return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 驗證批次關閉請求                                                 |
//+------------------------------------------------------------------+
static bool OrderExecutor::ValidateBatchCloseRequest(BatchCloseRequest* request)
{
    if (!ValidateBatchTradingRequest<CloseRequest*>(request))
        return false;

    // 檢查每個關閉請求
    for (int i = 0; i < request.GetRequestCount(); i++)
    {
        CloseRequest* closeReq = request.GetRequest(i);
        if (!ValidateCloseRequest(closeReq, ""))  // 符號將在執行時檢查
            return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 市價訂單執行                                                     |
//+------------------------------------------------------------------+
TradingResult* OrderExecutor::Market(OrderRequest* request)
{
    // 驗證請求
    if (!ValidateOrderRequest(request, m_symbol))
    {
        return new TradingResult(-1, false, "Invalid order request");
    }

    // 執行訂單
    int ticket = -1;
    int op = request.GetOp();
    double lots = request.GetLots();
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();
    string comment = request.GetComment();

    if (op == OP_BUY)
    {
        ticket = buy(lots, stoploss, takeprofit, comment);
    }
    else if (op == OP_SELL)
    {
        ticket = sell(lots, stoploss, takeprofit, comment);
    }

    bool success = (ticket > 0);
    string message = success ? "Order executed successfully" : "Order execution failed";

    return new TradingResult(ticket, success, message);
}

//+------------------------------------------------------------------+
//| 修改訂單                                                         |
//+------------------------------------------------------------------+
TradingResult* OrderExecutor::Modify(ModifyRequest* request)
{
    // 驗證請求
    if (!ValidateModifyRequest(request, m_symbol))
    {
        return new TradingResult(-1, false, "Invalid modify request");
    }

    // 執行修改
    int ticket = request.GetTicket();
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();

    bool success = modify(ticket, stoploss, takeprofit);
    string message = success ? "Order modified successfully" : "Order modification failed";

    return new TradingResult(ticket, success, message);
}

//+------------------------------------------------------------------+
//| 關閉訂單                                                         |
//+------------------------------------------------------------------+
TradingResult* OrderExecutor::Close(CloseRequest* request)
{
    // 驗證請求
    if (!ValidateCloseRequest(request, m_symbol))
    {
        return new TradingResult(-1, false, "Invalid close request");
    }

    // 執行關閉
    int ticket = request.GetTicket();
    bool success = false;

    if (request.IsPartialClose())
    {
        double lots = request.GetLots();
        success = close(ticket, lots);
    }
    else
    {
        success = close(ticket);
    }

    string message = success ? "Order closed successfully" : "Order closure failed";

    return new TradingResult(ticket, success, message);
}

//+------------------------------------------------------------------+
//| 批次市價訂單執行                                                 |
//+------------------------------------------------------------------+
Vector<TradingResult*>* OrderExecutor::BatchMarket(BatchOrderRequest* requests)
{
    Vector<TradingResult*>* results = new Vector<TradingResult*>(true);

    if (!ValidateBatchOrderRequest(requests))
    {
        results.add(new TradingResult(-1, false, "Invalid batch order request"));
        return results;
    }

    // 處理每個訂單請求
    for (int i = 0; i < requests.GetRequestCount(); i++)
    {
        OrderRequest* orderReq = requests.GetRequest(i);
        TradingResult* result = Market(orderReq);
        results.add(result);
    }

    return results;
}

//+------------------------------------------------------------------+
//| 批次修改訂單                                                     |
//+------------------------------------------------------------------+
Vector<TradingResult*>* OrderExecutor::BatchModify(BatchModifyRequest* requests)
{
    Vector<TradingResult*>* results = new Vector<TradingResult*>(true);

    if (!ValidateBatchModifyRequest(requests))
    {
        results.add(new TradingResult(-1, false, "Invalid batch modify request"));
        return results;
    }

    // 處理每個修改請求
    for (int i = 0; i < requests.GetRequestCount(); i++)
    {
        ModifyRequest* modifyReq = requests.GetRequest(i);
        TradingResult* result = Modify(modifyReq);
        results.add(result);
    }

    return results;
}

//+------------------------------------------------------------------+
//| 批次關閉訂單                                                     |
//+------------------------------------------------------------------+
Vector<TradingResult*>* OrderExecutor::BatchClose(BatchCloseRequest* requests)
{
    Vector<TradingResult*>* results = new Vector<TradingResult*>(true);

    if (!ValidateBatchCloseRequest(requests))
    {
        results.add(new TradingResult(-1, false, "Invalid batch close request"));
        return results;
    }

    // 處理每個關閉請求
    for (int i = 0; i < requests.GetRequestCount(); i++)
    {
        CloseRequest* closeReq = requests.GetRequest(i);
        TradingResult* result = Close(closeReq);
        results.add(result);
    }

    return results;
}
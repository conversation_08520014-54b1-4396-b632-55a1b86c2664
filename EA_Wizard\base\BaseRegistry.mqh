//+------------------------------------------------------------------+
//|                                                  BaseRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingEvent.mqh"
#include "../TradingPipeline.mqh"
#include "../../mql4-lib/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 註冊項目詳細信息模板類                                           |
//| 存儲已註冊項目的詳細信息和元數據                                 |
//+------------------------------------------------------------------+
template<typename Val>
class RegisteredDetail
{
private:
    Val m_value;                    // 註冊的值
    datetime m_registrationTime;    // 註冊時間
    datetime m_lastAccessTime;      // 最後訪問時間
    int m_accessCount;              // 訪問次數
    string m_source;                // 註冊來源
    string m_description;           // 項目描述
    bool m_isValid;                 // 是否有效

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    RegisteredDetail()
        : m_registrationTime(0),
          m_lastAccessTime(0),
          m_accessCount(0),
          m_source(""),
          m_description(""),
          m_isValid(false)
    {
    }

    RegisteredDetail(Val value, string source = "BaseRegistry", string description = "")
        : m_value(value),
          m_registrationTime(TimeCurrent()),
          m_lastAccessTime(TimeCurrent()),
          m_accessCount(1),
          m_source(source),
          m_description(description),
          m_isValid(true)
    {
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    ~RegisteredDetail() {}

    //+------------------------------------------------------------------+
    //| 訪問器方法                                                       |
    //+------------------------------------------------------------------+

    // 獲取註冊的值
    Val GetValue()
    {
        UpdateAccess();
        return m_value;
    }

    // 獲取註冊時間
    datetime GetRegistrationTime() const
    {
        return m_registrationTime;
    }

    // 獲取最後訪問時間
    datetime GetLastAccessTime() const
    {
        return m_lastAccessTime;
    }

    // 獲取訪問次數
    int GetAccessCount() const
    {
        return m_accessCount;
    }

    // 獲取註冊來源
    string GetSource() const
    {
        return m_source;
    }

    // 獲取項目描述
    string GetDescription() const
    {
        return m_description;
    }

    // 檢查是否有效
    bool IsValid() const
    {
        return m_isValid;
    }

    // 獲取註冊持續時間（秒）
    int GetRegistrationDuration() const
    {
        if(!m_isValid) return 0;
        return (int)(TimeCurrent() - m_registrationTime);
    }

    // 獲取自最後訪問以來的時間（秒）
    int GetTimeSinceLastAccess() const
    {
        if(!m_isValid) return 0;
        return (int)(TimeCurrent() - m_lastAccessTime);
    }

    //+------------------------------------------------------------------+
    //| 修改器方法                                                       |
    //+------------------------------------------------------------------+

    // 更新值
    void UpdateValue(Val newValue)
    {
        m_value = newValue;
        UpdateAccess();
    }

    // 設置來源
    void SetSource(string source)
    {
        m_source = source;
    }

    // 標記為無效
    void Invalidate()
    {
        m_isValid = false;
    }

    //+------------------------------------------------------------------+
    //| 信息方法                                                         |
    //+------------------------------------------------------------------+

    // 轉換為字符串
    string ToString() const
    {
        if(!m_isValid)
        {
            return "RegisteredDetail: [INVALID]";
        }

        return StringFormat(
            "RegisteredDetail:\n"
            "  來源: %s\n"
            "  描述: %s\n"
            "  註冊時間: %s\n"
            "  最後訪問: %s\n"
            "  訪問次數: %d\n"
            "  註冊持續: %d 秒\n"
            "  距離最後訪問: %d 秒",
            m_source,
            m_description != "" ? m_description : "無描述",
            TimeToString(m_registrationTime),
            TimeToString(m_lastAccessTime),
            m_accessCount,
            GetRegistrationDuration(),
            GetTimeSinceLastAccess()
        );
    }

    // 克隆當前實例（創建一個新的副本）
    // 注意：返回的指針需要調用者負責釋放內存
    RegisteredDetail<Val>* Clone() const
    {
        RegisteredDetail<Val>* clone = new RegisteredDetail<Val>();

        // 複製所有成員變數
        clone.m_value = m_value;
        clone.m_registrationTime = m_registrationTime;
        clone.m_lastAccessTime = m_lastAccessTime;
        clone.m_accessCount = m_accessCount;
        clone.m_source = m_source;
        clone.m_description = m_description;
        clone.m_isValid = m_isValid;

        return clone;
    }

private:
    //+------------------------------------------------------------------+
    //| 私有輔助方法                                                     |
    //+------------------------------------------------------------------+

    // 更新訪問信息
    void UpdateAccess()
    {
        if(m_isValid)
        {
            m_lastAccessTime = TimeCurrent();
            m_accessCount++;
        }
    }
};

//+------------------------------------------------------------------+
//| 註冊器模板抽象基類                                               |
//| 實現所有註冊器的共同功能，具體註冊邏輯由子類實現                 |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
class BaseRegistry
{
protected:
    string m_name;                      // 註冊器名稱
    string m_type;                      // 註冊器類型
    int m_maxRegistrations;             // 最大註冊數量
    bool m_isEnabled;                   // 是否啟用
    bool m_owned;                       // 是否擁有註冊項目
    PipelineResult* m_last_result;      // 執行結果
    HashMap<Key, RegisteredDetail<Val>*> m_registeredItems; // 核心存儲結構（存儲詳細信息對象）

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    BaseRegistry(string name = "BaseRegistry",
                string type = "Registry",
                int maxRegistrations = 50,
                bool owned = true)
        : m_name(name),
          m_type(type),
          m_maxRegistrations(maxRegistrations),
          m_isEnabled(true),
          m_owned(owned),
          m_registeredItems(NULL, false),
          m_last_result(new PipelineResult(false, "註冊器尚未執行操作", name, ERROR_LEVEL_INFO))
    {
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~BaseRegistry()
    {
        // 清理所有 RegisteredDetail 對象
        CleanupRegisteredDetails();

        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 模板方法實現                                                     |
    //+------------------------------------------------------------------+

    // 註冊 Key-Value 對
    virtual bool Register(Key key, Val value)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, "無效的鍵值", ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵已經註冊", ERROR_LEVEL_WARNING);
            return false;
        }

        // 創建 RegisteredDetail 對象並註冊
        RegisteredDetail<Val>* detail = new RegisteredDetail<Val>(value, GetName());
        m_registeredItems.set(key, detail);

        UpdateResult(true, "成功註冊項目", ERROR_LEVEL_INFO);
        return true;
    }

    // 註冊 Key-Value 對（帶描述）
    virtual bool Register(Key key, Val value, string description)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, "無效的鍵值", ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵已經註冊", ERROR_LEVEL_WARNING);
            return false;
        }

        // 創建 RegisteredDetail 對象並註冊（帶描述）
        RegisteredDetail<Val>* detail = new RegisteredDetail<Val>(value, GetName(), description);
        m_registeredItems.set(key, detail);

        UpdateResult(true, "成功註冊項目（帶描述）", ERROR_LEVEL_INFO);
        return true;
    }

    // 獲取已註冊的值
    virtual Val GetRegisteredValue(Key key, Val defaultValue)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊", ERROR_LEVEL_WARNING);
            return defaultValue;
        }

        RegisteredDetail<Val>* detail = m_registeredItems.get(key, NULL);
        if(detail == NULL || !detail.IsValid())
        {
            UpdateResult(false, "註冊項目無效", ERROR_LEVEL_ERROR);
            return defaultValue;
        }

        Val value = detail.GetValue(); // 這會增加訪問計數
        UpdateResult(true, "成功獲取值", ERROR_LEVEL_INFO);
        return value;
    }

    // 獲取已註冊項目的詳細信息（返回副本）
    // 注意：返回的指針需要調用者負責釋放內存（使用 delete）
    virtual RegisteredDetail<Val>* GetRegisteredDetail(Key key)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            RegisteredDetail<Val>* invalidDetail = new RegisteredDetail<Val>();
            return invalidDetail;
        }

        // 檢查鍵是否存在
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊，無法獲取詳細信息", ERROR_LEVEL_WARNING);
            RegisteredDetail<Val>* invalidDetail = new RegisteredDetail<Val>();
            return invalidDetail;
        }

        // 獲取存儲的 RegisteredDetail 對象並創建副本
        RegisteredDetail<Val>* storedDetail = m_registeredItems.get(key, NULL);
        if(storedDetail == NULL || !storedDetail.IsValid())
        {
            UpdateResult(false, "存儲的註冊項目無效", ERROR_LEVEL_ERROR);
            RegisteredDetail<Val>* invalidDetail = new RegisteredDetail<Val>();
            return invalidDetail;
        }

        // 返回存儲對象的副本
        RegisteredDetail<Val>* detailCopy = storedDetail.Clone();
        UpdateResult(true, "成功獲取註冊項目詳細信息", ERROR_LEVEL_INFO);
        return detailCopy;
    }

    // 更新已註冊的值
    virtual bool UpdateRegisteredValue(Key key, Val newValue)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已註冊
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊，無法更新", ERROR_LEVEL_ERROR);
            return false;
        }

        // 獲取存儲的 RegisteredDetail 對象並更新值
        RegisteredDetail<Val>* detail = m_registeredItems.get(key, NULL);
        if(detail == NULL || !detail.IsValid())
        {
            UpdateResult(false, "存儲的註冊項目無效，無法更新", ERROR_LEVEL_ERROR);
            return false;
        }

        // 更新 RegisteredDetail 對象的值
        detail.UpdateValue(newValue);

        UpdateResult(true, "成功更新值", ERROR_LEVEL_INFO);
        return true;
    }

    // 檢查指定鍵是否已註冊
    virtual bool IsRegistered(Key key)
    {
        return m_registeredItems.contains(key);
    }

    // 取消註冊指定鍵
    virtual bool Unregister(Key key)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊，無法取消註冊", ERROR_LEVEL_WARNING);
            return false;
        }

        // 獲取並刪除 RegisteredDetail 對象
        RegisteredDetail<Val>* detail = m_registeredItems.get(key, NULL);
        if(detail != NULL)
        {
            delete detail;
        }

        bool result = m_registeredItems.remove(key);
        if(result)
        {
            UpdateResult(true, "成功取消註冊鍵", ERROR_LEVEL_INFO);
        }
        else
        {
            UpdateResult(false, "取消註冊失敗", ERROR_LEVEL_ERROR);
        }

        return result;
    }

    // 清理所有註冊項目
    virtual void Clear()
    {
        int totalCleared = GetRegisteredCount();

        // 刪除所有 RegisteredDetail 對象
        CleanupRegisteredDetails();

        // 清空 HashMap
        m_registeredItems.clear();

        UpdateResult(true, StringFormat("已清理 %d 個註冊項目", totalCleared), ERROR_LEVEL_INFO);
    }

    // 獲取已註冊項目數量
    virtual int GetRegisteredCount()
    {
        return m_registeredItems.size();
    }

    //+------------------------------------------------------------------+
    //| 狀態查詢實現                                                     |
    //+------------------------------------------------------------------+

    // 獲取最大註冊數量
    virtual int GetMaxRegistrations()
    {
        return m_maxRegistrations;
    }

    // 檢查是否已滿
    virtual bool IsFull()
    {
        return GetRegisteredCount() >= m_maxRegistrations;
    }

    // 檢查是否為空
    virtual bool IsEmpty()
    {
        return GetRegisteredCount() == 0;
    }

    //+------------------------------------------------------------------+
    //| 狀態管理實現                                                     |
    //+------------------------------------------------------------------+

    // 設置啟用狀態
    virtual void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
        UpdateResult(true, StringFormat("註冊器狀態已設置為: %s", enabled ? "啟用" : "禁用"), ERROR_LEVEL_INFO);
    }

    // 檢查是否啟用
    virtual bool IsEnabled()
    {
        return m_isEnabled;
    }

    // 設置擁有狀態
    virtual void SetOwned(bool owned)
    {
        m_owned = owned;
        UpdateResult(true, StringFormat("擁有狀態已設置為: %s", owned ? "是" : "否"), ERROR_LEVEL_INFO);
    }

    // 檢查是否擁有註冊項目
    virtual bool IsOwned()
    {
        return m_owned;
    }

    //+------------------------------------------------------------------+
    //| 結果和信息實現                                                   |
    //+------------------------------------------------------------------+

    // 獲取執行結果
    virtual PipelineResult* GetResult()
    {
        return m_last_result;
    }

    // 獲取最後執行結果（別名方法）
    virtual PipelineResult* GetLastResult()
    {
        return m_last_result;
    }

    // 獲取註冊器名稱
    virtual string GetName()
    {
        return m_name;
    }

    // 獲取註冊器類型
    virtual string GetType()
    {
        return m_type;
    }

    // 獲取註冊器狀態信息
    virtual string GetStatusInfo()
    {
        string info = StringFormat(
            "註冊器名稱: %s\n"
            "類型: %s\n"
            "狀態: %s\n"
            "擁有項目: %s\n"
            "已註冊數: %d\n"
            "最大註冊數: %d\n"
            "使用率: %.1f%%",
            m_name,
            m_type,
            m_isEnabled ? "啟用" : "禁用",
            m_owned ? "是" : "否",
            GetRegisteredCount(),
            m_maxRegistrations,
            (m_maxRegistrations > 0) ? (GetRegisteredCount() * 100.0 / m_maxRegistrations) : 0.0
        );
        return info;
    }

    // 獲取所有已註冊的鍵
    virtual int GetAllKeys(Key &keys[])
    {
        int count = 0;
        ArrayResize(keys, GetRegisteredCount());

        foreachm(Key, key, RegisteredDetail<Val>*, detail, m_registeredItems)
        {
            keys[count] = key;
            count++;
        }

        return count;
    }

    // 獲取所有已註冊的值
    virtual int GetAllValues(Val &values[])
    {
        int count = 0;
        ArrayResize(values, GetRegisteredCount());

        foreachm(Key, key, RegisteredDetail<Val>*, detail, m_registeredItems)
        {
            if(detail != NULL && detail.IsValid())
            {
                values[count] = detail.GetValue(); // 這會增加訪問計數
                count++;
            }
        }

        return count;
    }
    
protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+

    // 更新執行結果
    void UpdateResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }
        m_last_result = new PipelineResult(success, message, GetName(), errorLevel);
    }

    // 檢查註冊器是否可用
    bool IsRegistryAvailable()
    {
        if(!m_isEnabled)
        {
            UpdateResult(false, "註冊器已禁用，無法執行操作", ERROR_LEVEL_WARNING);
            return false;
        }
        return true;
    }

    // 檢查是否已達到最大註冊數量
    bool CheckMaxRegistrations()
    {
        if(GetRegisteredCount() >= m_maxRegistrations)
        {
            UpdateResult(false, StringFormat("已達到最大註冊數量 %d", m_maxRegistrations), ERROR_LEVEL_ERROR);
            return false;
        }
        return true;
    }

    // 驗證鍵值
    virtual bool ValidateKey(Key key)
    {
        // 基本鍵值驗證，子類可以重寫
        return true;
    }

    // 清理所有 RegisteredDetail 對象的輔助方法
    void CleanupRegisteredDetails()
    {
        foreachm(Key, key, RegisteredDetail<Val>*, detail, m_registeredItems)
        {
            if(detail != NULL)
            {
                delete detail;
            }
        }
    }

};

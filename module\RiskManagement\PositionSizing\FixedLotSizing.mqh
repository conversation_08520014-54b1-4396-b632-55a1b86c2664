//+------------------------------------------------------------------+
//|                                            FixedLotSizing.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef FIXED_LOT_SIZING_MQH
#define FIXED_LOT_SIZING_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| FixedLotSizing Class                                             |
//| Implementation of fixed lot size position sizing strategy       |
//+------------------------------------------------------------------+
class FixedLotSizing : public BaseComponent
{
private:
    double            m_fixedLotSize;     // Fixed lot size
    double            m_minLotSize;       // Minimum allowed lot size
    double            m_maxLotSize;       // Maximum allowed lot size
    double            m_lotStep;          // Lot size step
    string            m_symbol;           // Trading symbol

public:
    //--- Constructor and Destructor
                      FixedLotSizing(string symbol = "", double lotSize = 0.01);
    virtual          ~FixedLotSizing();
    
    //--- Configuration methods
    void              SetFixedLotSize(double lotSize);
    void              SetSymbol(string symbol);
    
    //--- Information methods
    double            GetFixedLotSize() const { return m_fixedLotSize; }
    double            GetMinLotSize() const { return m_minLotSize; }
    double            GetMaxLotSize() const { return m_maxLotSize; }
    double            GetLotStep() const { return m_lotStep; }
    string            GetSymbol() const { return m_symbol; }
    
    //--- Position sizing methods
    double            CalculatePositionSize(double accountBalance = 0.0, double riskPercent = 0.0, 
                                          double stopLossDistance = 0.0);
    double            NormalizeLotSize(double lotSize);
    bool              IsValidLotSize(double lotSize);
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Utility methods
    void              UpdateMarketInfo();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
FixedLotSizing::FixedLotSizing(string symbol = "", double lotSize = 0.01) : BaseComponent("FixedLotSizing")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_fixedLotSize = lotSize;
    m_minLotSize = 0.01;
    m_maxLotSize = 100.0;
    m_lotStep = 0.01;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
FixedLotSizing::~FixedLotSizing()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize fixed lot sizing                                      |
//+------------------------------------------------------------------+
bool FixedLotSizing::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(601, "Invalid symbol for position sizing");
        return false;
    }
    
    UpdateMarketInfo();
    
    if (!IsValidLotSize(m_fixedLotSize))
    {
        SetError(602, "Invalid fixed lot size");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool FixedLotSizing::OnValidate()
{
    if (m_fixedLotSize <= 0.0)
    {
        SetError(603, "Fixed lot size must be positive");
        return false;
    }
    
    if (m_fixedLotSize < m_minLotSize || m_fixedLotSize > m_maxLotSize)
    {
        SetError(604, "Fixed lot size outside allowed range");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set fixed lot size                                               |
//+------------------------------------------------------------------+
void FixedLotSizing::SetFixedLotSize(double lotSize)
{
    if (lotSize > 0.0)
    {
        m_fixedLotSize = NormalizeLotSize(lotSize);
    }
}

//+------------------------------------------------------------------+
//| Set trading symbol                                               |
//+------------------------------------------------------------------+
void FixedLotSizing::SetSymbol(string symbol)
{
    if (symbol != "")
    {
        m_symbol = symbol;
        UpdateMarketInfo();
    }
}

//+------------------------------------------------------------------+
//| Calculate position size (returns fixed lot size)                |
//+------------------------------------------------------------------+
double FixedLotSizing::CalculatePositionSize(double accountBalance = 0.0, double riskPercent = 0.0, 
                                            double stopLossDistance = 0.0)
{
    // For fixed lot sizing, we ignore the parameters and return the fixed size
    return NormalizeLotSize(m_fixedLotSize);
}

//+------------------------------------------------------------------+
//| Normalize lot size to valid increment                           |
//+------------------------------------------------------------------+
double FixedLotSizing::NormalizeLotSize(double lotSize)
{
    if (lotSize <= 0.0 || m_lotStep <= 0.0)
        return m_minLotSize;
    
    // Round to nearest lot step
    double normalizedLot = MathRound(lotSize / m_lotStep) * m_lotStep;
    
    // Ensure within bounds
    normalizedLot = MathMax(m_minLotSize, MathMin(m_maxLotSize, normalizedLot));
    
    return normalizedLot;
}

//+------------------------------------------------------------------+
//| Check if lot size is valid                                       |
//+------------------------------------------------------------------+
bool FixedLotSizing::IsValidLotSize(double lotSize)
{
    if (lotSize <= 0.0)
        return false;
    
    if (lotSize < m_minLotSize || lotSize > m_maxLotSize)
        return false;
    
    // Check if lot size is a valid increment
    double remainder = MathMod(lotSize, m_lotStep);
    return (MathAbs(remainder) < 0.0001 || MathAbs(remainder - m_lotStep) < 0.0001);
}

//+------------------------------------------------------------------+
//| Update market information                                        |
//+------------------------------------------------------------------+
void FixedLotSizing::UpdateMarketInfo()
{
    m_minLotSize = MarketInfo(m_symbol, MODE_MINLOT);
    m_maxLotSize = MarketInfo(m_symbol, MODE_MAXLOT);
    m_lotStep = MarketInfo(m_symbol, MODE_LOTSTEP);
    
    // Set defaults if market info is not available
    if (m_minLotSize <= 0.0) m_minLotSize = 0.01;
    if (m_maxLotSize <= 0.0) m_maxLotSize = 100.0;
    if (m_lotStep <= 0.0) m_lotStep = 0.01;
}

#endif // FIXED_LOT_SIZING_MQH

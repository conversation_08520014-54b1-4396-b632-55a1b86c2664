//+------------------------------------------------------------------+
//|                                       MultiIndicatorSignal.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MULTI_INDICATOR_SIGNAL_MQH
#define MULTI_INDICATOR_SIGNAL_MQH

#include "../../Base/BaseComponent.mqh"
#include "../../Indicators/BollingerBands/BollingerBands.mqh"
#include "../../Indicators/MACD/MACD.mqh"
#include "../../Indicators/RSI/RSI.mqh"

//+------------------------------------------------------------------+
//| Signal Confirmation Level Enumeration                           |
//+------------------------------------------------------------------+
enum ENUM_CONFIRMATION_LEVEL
{
    CONFIRMATION_SINGLE = 1,    // Single indicator confirmation
    CONFIRMATION_DOUBLE = 2,    // Two indicators confirmation
    CONFIRMATION_TRIPLE = 3     // Triple indicators confirmation
};

//+------------------------------------------------------------------+
//| MultiIndicatorSignal Class                                       |
//| Implementation of multi-indicator signal generation system      |
//+------------------------------------------------------------------+
class MultiIndicatorSignal : public BaseComponent
{
private:
    // Indicator instances
    BollingerBands*           m_bollingerBands;
    MACD*                     m_macd;
    RSI*                      m_rsi;
    
    // Configuration
    string                    m_symbol;
    ENUM_TIMEFRAMES           m_timeframe;
    ENUM_CONFIRMATION_LEVEL   m_confirmationLevel;
    double                    m_minConfidence;
    bool                      m_useWeightedSignals;
    
    // Signal weights
    double                    m_bbWeight;
    double                    m_macdWeight;
    double                    m_rsiWeight;
    
    // Last signals
    SignalInfo                m_lastBBSignal;
    SignalInfo                m_lastMACDSignal;
    SignalInfo                m_lastRSISignal;
    SignalInfo                m_lastCombinedSignal;

public:
    //--- Constructor and Destructor
                              MultiIndicatorSignal(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                                   ENUM_CONFIRMATION_LEVEL level = CONFIRMATION_TRIPLE);
    virtual                  ~MultiIndicatorSignal();
    
    //--- Configuration methods
    void                      SetSymbol(string symbol);
    void                      SetTimeframe(ENUM_TIMEFRAMES timeframe);
    void                      SetConfirmationLevel(ENUM_CONFIRMATION_LEVEL level) { m_confirmationLevel = level; }
    void                      SetMinConfidence(double confidence) { m_minConfidence = MathMax(0.1, MathMin(1.0, confidence)); }
    void                      SetUseWeightedSignals(bool use) { m_useWeightedSignals = use; }
    void                      SetSignalWeights(double bbWeight, double macdWeight, double rsiWeight);
    
    //--- Indicator configuration
    void                      ConfigureBollingerBands(int period = 20, double deviation = 2.0);
    void                      ConfigureMACD(int fastEMA = 12, int slowEMA = 26, int signal = 9);
    void                      ConfigureRSI(int period = 14, double overbought = 70.0, double oversold = 30.0);
    
    //--- Information methods
    string                    GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES           GetTimeframe() const { return m_timeframe; }
    ENUM_CONFIRMATION_LEVEL   GetConfirmationLevel() const { return m_confirmationLevel; }
    double                    GetMinConfidence() const { return m_minConfidence; }
    
    //--- Signal generation methods
    SignalInfo                GenerateCombinedSignal(int shift = 0);
    SignalInfo                GetLastCombinedSignal() const { return m_lastCombinedSignal; }
    bool                      HasValidSignal() const;
    bool                      IsSignalConfirmed(const SignalInfo& signal) const;
    
    //--- Individual signal methods
    SignalInfo                GetBollingerBandsSignal(int shift = 0);
    SignalInfo                GetMACDSignal(int shift = 0);
    SignalInfo                GetRSISignal(int shift = 0);
    
    //--- Signal analysis methods
    int                       CountConfirmingSignals(ENUM_SIGNAL_TYPE targetType, int shift = 0);
    double                    CalculateWeightedConfidence(int shift = 0);
    ENUM_SIGNAL_STRENGTH      DetermineOverallStrength(int shift = 0);
    bool                      AreSignalsAligned(int shift = 0);
    
    //--- Override base class methods
    virtual bool              OnInitialize() override;
    virtual bool              OnValidate() override;
    virtual bool              OnUpdate() override;
    
    //--- Utility methods
    string                    SignalToString(const SignalInfo& signal) const;
    void                      LogSignalDetails(const SignalInfo& signal) const;
    bool                      IsIndicatorReady(BaseIndicator* indicator) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
MultiIndicatorSignal::MultiIndicatorSignal(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                           ENUM_CONFIRMATION_LEVEL level = CONFIRMATION_TRIPLE) : BaseComponent("MultiIndicatorSignal")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_confirmationLevel = level;
    m_minConfidence = 0.6;
    m_useWeightedSignals = true;
    
    // Default equal weights
    m_bbWeight = 1.0;
    m_macdWeight = 1.0;
    m_rsiWeight = 1.0;
    
    // Initialize indicators
    m_bollingerBands = new BollingerBands(m_symbol, m_timeframe);
    m_macd = new MACD(m_symbol, m_timeframe);
    m_rsi = new RSI(m_symbol, m_timeframe);
    
    // Initialize signal structures
    m_lastBBSignal.type = SIGNAL_NONE;
    m_lastMACDSignal.type = SIGNAL_NONE;
    m_lastRSISignal.type = SIGNAL_NONE;
    m_lastCombinedSignal.type = SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
MultiIndicatorSignal::~MultiIndicatorSignal()
{
    if (m_bollingerBands != NULL)
    {
        delete m_bollingerBands;
        m_bollingerBands = NULL;
    }
    
    if (m_macd != NULL)
    {
        delete m_macd;
        m_macd = NULL;
    }
    
    if (m_rsi != NULL)
    {
        delete m_rsi;
        m_rsi = NULL;
    }
}

//+------------------------------------------------------------------+
//| Initialize multi-indicator signal                               |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(1101, "Invalid symbol for signal generation");
        return false;
    }
    
    // Initialize indicators
    if (!m_bollingerBands.Initialize())
    {
        SetError(1102, "Failed to initialize Bollinger Bands");
        return false;
    }
    
    if (!m_macd.Initialize())
    {
        SetError(1103, "Failed to initialize MACD");
        return false;
    }
    
    if (!m_rsi.Initialize())
    {
        SetError(1104, "Failed to initialize RSI");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::OnValidate()
{
    if (m_confirmationLevel < CONFIRMATION_SINGLE || m_confirmationLevel > CONFIRMATION_TRIPLE)
    {
        SetError(1105, "Invalid confirmation level");
        return false;
    }
    
    if (m_minConfidence < 0.1 || m_minConfidence > 1.0)
    {
        SetError(1106, "Invalid minimum confidence level");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update signal generation                                         |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::OnUpdate()
{
    // Update all indicators
    if (!m_bollingerBands.Update() || !m_macd.Update() || !m_rsi.Update())
    {
        SetError(1107, "Failed to update indicators");
        return false;
    }
    
    // Generate combined signal
    m_lastCombinedSignal = GenerateCombinedSignal(0);
    
    return true;
}

//+------------------------------------------------------------------+
//| Set symbol for all indicators                                    |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::SetSymbol(string symbol)
{
    if (symbol != "")
    {
        m_symbol = symbol;
        m_bollingerBands.SetSymbol(symbol);
        m_macd.SetSymbol(symbol);
        m_rsi.SetSymbol(symbol);
    }
}

//+------------------------------------------------------------------+
//| Set timeframe for all indicators                                 |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::SetTimeframe(ENUM_TIMEFRAMES timeframe)
{
    m_timeframe = timeframe;
    m_bollingerBands.SetTimeframe(timeframe);
    m_macd.SetTimeframe(timeframe);
    m_rsi.SetTimeframe(timeframe);
}

//+------------------------------------------------------------------+
//| Set signal weights                                               |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::SetSignalWeights(double bbWeight, double macdWeight, double rsiWeight)
{
    m_bbWeight = MathMax(0.1, bbWeight);
    m_macdWeight = MathMax(0.1, macdWeight);
    m_rsiWeight = MathMax(0.1, rsiWeight);
}

//+------------------------------------------------------------------+
//| Configure Bollinger Bands                                       |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::ConfigureBollingerBands(int period = 20, double deviation = 2.0)
{
    m_bollingerBands.SetPeriod(period);
    m_bollingerBands.SetDeviation(deviation);
}

//+------------------------------------------------------------------+
//| Configure MACD                                                   |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::ConfigureMACD(int fastEMA = 12, int slowEMA = 26, int signal = 9)
{
    m_macd.SetFastEMA(fastEMA);
    m_macd.SetSlowEMA(slowEMA);
    m_macd.SetSignalSMA(signal);
}

//+------------------------------------------------------------------+
//| Configure RSI                                                    |
//+------------------------------------------------------------------+
void MultiIndicatorSignal::ConfigureRSI(int period = 14, double overbought = 70.0, double oversold = 30.0)
{
    m_rsi.SetRSIPeriod(period);
    m_rsi.SetOverboughtLevel(overbought);
    m_rsi.SetOversoldLevel(oversold);
}

//+------------------------------------------------------------------+
//| Generate combined signal                                         |
//+------------------------------------------------------------------+
SignalInfo MultiIndicatorSignal::GenerateCombinedSignal(int shift = 0)
{
    SignalInfo combinedSignal;
    combinedSignal.type = SIGNAL_NONE;
    combinedSignal.strength = STRENGTH_WEAK;
    combinedSignal.confidence = 0.0;
    combinedSignal.timestamp = TimeCurrent();
    combinedSignal.value = 0.0;
    combinedSignal.description = "Multi-Indicator Signal";
    
    // Get individual signals
    m_lastBBSignal = GetBollingerBandsSignal(shift);
    m_lastMACDSignal = GetMACDSignal(shift);
    m_lastRSISignal = GetRSISignal(shift);
    
    // Check for buy signals
    int buyConfirmations = CountConfirmingSignals(SIGNAL_BUY, shift);
    int sellConfirmations = CountConfirmingSignals(SIGNAL_SELL, shift);
    
    // Determine signal type based on confirmation level
    if (buyConfirmations >= (int)m_confirmationLevel && buyConfirmations > sellConfirmations)
    {
        combinedSignal.type = SIGNAL_BUY;
        combinedSignal.description = "Multi-Indicator BUY Signal (" + IntegerToString(buyConfirmations) + " confirmations)";
    }
    else if (sellConfirmations >= (int)m_confirmationLevel && sellConfirmations > buyConfirmations)
    {
        combinedSignal.type = SIGNAL_SELL;
        combinedSignal.description = "Multi-Indicator SELL Signal (" + IntegerToString(sellConfirmations) + " confirmations)";
    }
    
    // Calculate confidence and strength
    if (combinedSignal.type != SIGNAL_NONE)
    {
        if (m_useWeightedSignals)
        {
            combinedSignal.confidence = CalculateWeightedConfidence(shift);
        }
        else
        {
            int totalConfirmations = (combinedSignal.type == SIGNAL_BUY) ? buyConfirmations : sellConfirmations;
            combinedSignal.confidence = (double)totalConfirmations / 3.0; // Normalize to 0-1
        }
        
        combinedSignal.strength = DetermineOverallStrength(shift);
        
        // Apply minimum confidence filter
        if (combinedSignal.confidence < m_minConfidence)
        {
            combinedSignal.type = SIGNAL_NONE;
            combinedSignal.confidence = 0.0;
        }
    }
    
    return combinedSignal;
}

//+------------------------------------------------------------------+
//| Get Bollinger Bands signal                                       |
//+------------------------------------------------------------------+
SignalInfo MultiIndicatorSignal::GetBollingerBandsSignal(int shift = 0)
{
    if (!IsIndicatorReady(m_bollingerBands))
    {
        SignalInfo emptySignal;
        emptySignal.type = SIGNAL_NONE;
        return emptySignal;
    }
    
    return m_bollingerBands.GenerateSignal(shift);
}

//+------------------------------------------------------------------+
//| Get MACD signal                                                  |
//+------------------------------------------------------------------+
SignalInfo MultiIndicatorSignal::GetMACDSignal(int shift = 0)
{
    if (!IsIndicatorReady(m_macd))
    {
        SignalInfo emptySignal;
        emptySignal.type = SIGNAL_NONE;
        return emptySignal;
    }
    
    return m_macd.GenerateSignal(shift);
}

//+------------------------------------------------------------------+
//| Get RSI signal                                                   |
//+------------------------------------------------------------------+
SignalInfo MultiIndicatorSignal::GetRSISignal(int shift = 0)
{
    if (!IsIndicatorReady(m_rsi))
    {
        SignalInfo emptySignal;
        emptySignal.type = SIGNAL_NONE;
        return emptySignal;
    }
    
    return m_rsi.GenerateSignal(shift);
}

//+------------------------------------------------------------------+
//| Count confirming signals                                         |
//+------------------------------------------------------------------+
int MultiIndicatorSignal::CountConfirmingSignals(ENUM_SIGNAL_TYPE targetType, int shift = 0)
{
    int count = 0;
    
    if (m_lastBBSignal.type == targetType)
        count++;
    
    if (m_lastMACDSignal.type == targetType)
        count++;
    
    if (m_lastRSISignal.type == targetType)
        count++;
    
    return count;
}

//+------------------------------------------------------------------+
//| Calculate weighted confidence                                    |
//+------------------------------------------------------------------+
double MultiIndicatorSignal::CalculateWeightedConfidence(int shift = 0)
{
    double totalWeight = 0.0;
    double weightedConfidence = 0.0;
    
    if (m_lastBBSignal.type != SIGNAL_NONE)
    {
        weightedConfidence += m_lastBBSignal.confidence * m_bbWeight;
        totalWeight += m_bbWeight;
    }
    
    if (m_lastMACDSignal.type != SIGNAL_NONE)
    {
        weightedConfidence += m_lastMACDSignal.confidence * m_macdWeight;
        totalWeight += m_macdWeight;
    }
    
    if (m_lastRSISignal.type != SIGNAL_NONE)
    {
        weightedConfidence += m_lastRSISignal.confidence * m_rsiWeight;
        totalWeight += m_rsiWeight;
    }
    
    return (totalWeight > 0.0) ? weightedConfidence / totalWeight : 0.0;
}

//+------------------------------------------------------------------+
//| Determine overall signal strength                                |
//+------------------------------------------------------------------+
ENUM_SIGNAL_STRENGTH MultiIndicatorSignal::DetermineOverallStrength(int shift = 0)
{
    int strongSignals = 0;
    int mediumSignals = 0;
    int weakSignals = 0;
    
    if (m_lastBBSignal.type != SIGNAL_NONE)
    {
        switch(m_lastBBSignal.strength)
        {
            case STRENGTH_STRONG: strongSignals++; break;
            case STRENGTH_MEDIUM: mediumSignals++; break;
            case STRENGTH_WEAK: weakSignals++; break;
        }
    }
    
    if (m_lastMACDSignal.type != SIGNAL_NONE)
    {
        switch(m_lastMACDSignal.strength)
        {
            case STRENGTH_STRONG: strongSignals++; break;
            case STRENGTH_MEDIUM: mediumSignals++; break;
            case STRENGTH_WEAK: weakSignals++; break;
        }
    }
    
    if (m_lastRSISignal.type != SIGNAL_NONE)
    {
        switch(m_lastRSISignal.strength)
        {
            case STRENGTH_STRONG: strongSignals++; break;
            case STRENGTH_MEDIUM: mediumSignals++; break;
            case STRENGTH_WEAK: weakSignals++; break;
        }
    }
    
    if (strongSignals >= 2)
        return STRENGTH_STRONG;
    else if (strongSignals >= 1 || mediumSignals >= 2)
        return STRENGTH_MEDIUM;
    else
        return STRENGTH_WEAK;
}

//+------------------------------------------------------------------+
//| Check if signals are aligned                                     |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::AreSignalsAligned(int shift = 0)
{
    ENUM_SIGNAL_TYPE firstType = SIGNAL_NONE;
    int signalCount = 0;
    
    if (m_lastBBSignal.type != SIGNAL_NONE)
    {
        if (firstType == SIGNAL_NONE)
            firstType = m_lastBBSignal.type;
        else if (firstType != m_lastBBSignal.type)
            return false;
        signalCount++;
    }
    
    if (m_lastMACDSignal.type != SIGNAL_NONE)
    {
        if (firstType == SIGNAL_NONE)
            firstType = m_lastMACDSignal.type;
        else if (firstType != m_lastMACDSignal.type)
            return false;
        signalCount++;
    }
    
    if (m_lastRSISignal.type != SIGNAL_NONE)
    {
        if (firstType == SIGNAL_NONE)
            firstType = m_lastRSISignal.type;
        else if (firstType != m_lastRSISignal.type)
            return false;
        signalCount++;
    }
    
    return (signalCount >= (int)m_confirmationLevel);
}

//+------------------------------------------------------------------+
//| Check if combined signal is valid                               |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::HasValidSignal() const
{
    return (m_lastCombinedSignal.type != SIGNAL_NONE && 
            m_lastCombinedSignal.confidence >= m_minConfidence);
}

//+------------------------------------------------------------------+
//| Check if signal is confirmed                                     |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::IsSignalConfirmed(const SignalInfo& signal) const
{
    return (signal.type != SIGNAL_NONE && 
            signal.confidence >= m_minConfidence &&
            signal.strength >= STRENGTH_WEAK);
}

//+------------------------------------------------------------------+
//| Convert signal to string                                         |
//+------------------------------------------------------------------+
string MultiIndicatorSignal::SignalToString(const SignalInfo& signal) const
{
    string result = "Type: ";
    
    switch(signal.type)
    {
        case SIGNAL_BUY: result += "BUY"; break;
        case SIGNAL_SELL: result += "SELL"; break;
        case SIGNAL_NEUTRAL: result += "NEUTRAL"; break;
        default: result += "NONE"; break;
    }
    
    result += " | Confidence: " + DoubleToString(signal.confidence, 2);
    result += " | Strength: ";
    
    switch(signal.strength)
    {
        case STRENGTH_STRONG: result += "STRONG"; break;
        case STRENGTH_MEDIUM: result += "MEDIUM"; break;
        default: result += "WEAK"; break;
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Check if indicator is ready                                      |
//+------------------------------------------------------------------+
bool MultiIndicatorSignal::IsIndicatorReady(BaseIndicator* indicator) const
{
    return (indicator != NULL && indicator.IsInitialized() && indicator.IsEnabled());
}

#endif // MULTI_INDICATOR_SIGNAL_MQH

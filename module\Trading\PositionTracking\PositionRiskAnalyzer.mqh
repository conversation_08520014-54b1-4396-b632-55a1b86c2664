//+------------------------------------------------------------------+
//|                                       PositionRiskAnalyzer.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_RISK_ANALYZER_MQH
#define POSITION_RISK_ANALYZER_MQH

#include "../../Base/BaseComponent.mqh"
#include "PositionCollector.mqh"
#include "PositionStatistics.mqh"

//+------------------------------------------------------------------+
//| Risk Analysis Results Structure                                  |
//+------------------------------------------------------------------+
struct RiskAnalysisResult
{
    double            currentDrawdown;      // Current drawdown percentage
    double            maxDrawdown;          // Maximum drawdown recorded
    double            profitFactor;         // Profit factor ratio
    double            winRate;              // Win rate percentage
    double            exposureRatio;        // Position exposure ratio
    bool              riskLimitExceeded;    // Risk limit status
    string            riskLevel;            // Risk level description
};

//+------------------------------------------------------------------+
//| PositionRiskAnalyzer Class                                       |
//| Responsible for risk analysis and monitoring                     |
//+------------------------------------------------------------------+
class PositionRiskAnalyzer : public BaseComponent
{
private:
    // Risk thresholds
    double            m_maxDrawdownLimit;       // Maximum allowed drawdown
    double            m_maxExposureLimit;       // Maximum exposure limit
    double            m_minProfitFactor;        // Minimum profit factor
    double            m_maxLossPercentage;      // Maximum loss percentage

    // Risk tracking
    double            m_peakBalance;            // Peak balance for drawdown calculation
    double            m_maxDrawdownRecorded;    // Maximum drawdown recorded
    RiskAnalysisResult m_lastAnalysis;          // Last analysis result

    // Historical data for calculations
    double            m_totalWins;              // Total winning trades
    double            m_totalLosses;            // Total losing trades
    double            m_grossProfit;            // Gross profit
    double            m_grossLoss;              // Gross loss

    // Error codes specific to PositionRiskAnalyzer
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionRiskAnalyzer();
    virtual          ~PositionRiskAnalyzer();

    //--- Configuration methods
    void              SetMaxDrawdownLimit(double limit) { m_maxDrawdownLimit = MathMax(0.0, limit); }
    void              SetMaxExposureLimit(double limit) { m_maxExposureLimit = MathMax(0.0, limit); }
    void              SetMinProfitFactor(double factor) { m_minProfitFactor = MathMax(0.0, factor); }
    void              SetMaxLossPercentage(double percentage) { m_maxLossPercentage = MathMax(0.0, percentage); }

    //--- Risk analysis methods
    RiskAnalysisResult AnalyzeRisk(PositionCollector* collector, PositionStatistics* statistics);
    RiskAnalysisResult AnalyzeRisk(PositionInfo& positions[], int count, const PositionStats& stats);
    RiskAnalysisResult GetLastAnalysis() const { return m_lastAnalysis; }

    //--- Individual risk calculations
    double            CalculateDrawdown(double currentBalance);
    double            CalculateExposureRatio(const PositionStats& stats, double accountBalance);
    double            CalculateProfitFactor();
    double            CalculateWinRate();
    bool              CheckRiskLimits(const RiskAnalysisResult& analysis);
    string            DetermineRiskLevel(const RiskAnalysisResult& analysis);

    //--- Historical data management
    void              UpdateHistoricalData();
    void              ResetHistoricalData();

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;

private:
    //--- Internal calculation methods
    void              UpdatePeakBalance(double currentBalance);
    double            GetAccountBalance();
    void              CalculateHistoricalMetrics();
};

// Error codes for PositionRiskAnalyzer
const BaseErrorDescriptor PositionRiskAnalyzer::CODE_ERRORS[] = {
    {1321, "Invalid risk analysis parameters"},
    {1322, "Failed to calculate risk metrics"},
    {1323, "Invalid position data for risk analysis"},
    {1324, "Risk limit exceeded"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionRiskAnalyzer::PositionRiskAnalyzer() : BaseComponent("PositionRiskAnalyzer")
{
    // Initialize risk thresholds with conservative defaults
    m_maxDrawdownLimit = 20.0;      // 20% maximum drawdown
    m_maxExposureLimit = 50.0;      // 50% maximum exposure
    m_minProfitFactor = 1.2;        // Minimum 1.2 profit factor
    m_maxLossPercentage = 10.0;     // 10% maximum loss

    // Initialize tracking variables
    m_peakBalance = GetAccountBalance();
    m_maxDrawdownRecorded = 0.0;

    // Initialize historical data
    m_totalWins = 0.0;
    m_totalLosses = 0.0;
    m_grossProfit = 0.0;
    m_grossLoss = 0.0;

    ZeroMemory(m_lastAnalysis);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionRiskAnalyzer::~PositionRiskAnalyzer()
{
    // No dynamic memory to clean up
}

//+------------------------------------------------------------------+
//| Initialize risk analyzer                                         |
//+------------------------------------------------------------------+
bool PositionRiskAnalyzer::OnInitialize()
{
    m_peakBalance = GetAccountBalance();
    UpdateHistoricalData();
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionRiskAnalyzer::OnValidate()
{
    if (m_maxDrawdownLimit <= 0.0 || m_maxDrawdownLimit > 100.0)
    {
        HandleError(1321, GetErrorDescription(1321));
        return false;
    }

    if (m_maxExposureLimit <= 0.0 || m_maxExposureLimit > 100.0)
    {
        HandleError(1321, GetErrorDescription(1321));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update risk analyzer                                             |
//+------------------------------------------------------------------+
bool PositionRiskAnalyzer::OnUpdate()
{
    UpdatePeakBalance(GetAccountBalance());
    UpdateHistoricalData();
    return true;
}

//+------------------------------------------------------------------+
//| Analyze risk using collector and statistics                     |
//+------------------------------------------------------------------+
RiskAnalysisResult PositionRiskAnalyzer::AnalyzeRisk(PositionCollector* collector, PositionStatistics* statistics)
{
    RiskAnalysisResult result;
    ZeroMemory(result);

    if (collector == NULL || statistics == NULL)
    {
        HandleError(1323, GetErrorDescription(1323));
        return result;
    }

    PositionInfo positions[];
    collector.GetAllPositions(positions);
    int count = collector.GetPositionCount();
    PositionStats stats = statistics.GetCurrentStats();

    return AnalyzeRisk(positions, count, stats);
}

//+------------------------------------------------------------------+
//| Analyze risk using position array and statistics               |
//+------------------------------------------------------------------+
RiskAnalysisResult PositionRiskAnalyzer::AnalyzeRisk(PositionInfo& positions[], int count, const PositionStats& stats)
{
    RiskAnalysisResult result;
    ZeroMemory(result);

    if (count < 0)
    {
        HandleError(1323, GetErrorDescription(1323));
        return result;
    }

    double currentBalance = GetAccountBalance();

    // Calculate risk metrics
    result.currentDrawdown = CalculateDrawdown(currentBalance);
    result.maxDrawdown = m_maxDrawdownRecorded;
    result.profitFactor = CalculateProfitFactor();
    result.winRate = CalculateWinRate();
    result.exposureRatio = CalculateExposureRatio(stats, currentBalance);
    result.riskLimitExceeded = CheckRiskLimits(result);
    result.riskLevel = DetermineRiskLevel(result);

    m_lastAnalysis = result;
    return result;
}

//+------------------------------------------------------------------+
//| Calculate current drawdown                                       |
//+------------------------------------------------------------------+
double PositionRiskAnalyzer::CalculateDrawdown(double currentBalance)
{
    if (m_peakBalance <= 0.0) return 0.0;

    double drawdown = ((m_peakBalance - currentBalance) / m_peakBalance) * 100.0;
    drawdown = MathMax(0.0, drawdown);

    // Update maximum drawdown recorded
    if (drawdown > m_maxDrawdownRecorded)
    {
        m_maxDrawdownRecorded = drawdown;
    }

    return drawdown;
}

//+------------------------------------------------------------------+
//| Calculate exposure ratio                                         |
//+------------------------------------------------------------------+
double PositionRiskAnalyzer::CalculateExposureRatio(const PositionStats& stats, double accountBalance)
{
    if (accountBalance <= 0.0) return 0.0;

    double totalExposure = MathAbs(stats.totalLots) * 100000.0; // Assuming standard lot size
    return (totalExposure / accountBalance) * 100.0;
}

//+------------------------------------------------------------------+
//| Calculate profit factor                                          |
//+------------------------------------------------------------------+
double PositionRiskAnalyzer::CalculateProfitFactor()
{
    if (MathAbs(m_grossLoss) < 0.01) // Avoid division by zero
    {
        return (m_grossProfit > 0.0) ? 999.0 : 0.0;
    }

    return m_grossProfit / MathAbs(m_grossLoss);
}

//+------------------------------------------------------------------+
//| Calculate win rate                                               |
//+------------------------------------------------------------------+
double PositionRiskAnalyzer::CalculateWinRate()
{
    double totalTrades = m_totalWins + m_totalLosses;

    if (totalTrades <= 0.0) return 0.0;

    return (m_totalWins / totalTrades) * 100.0;
}

//+------------------------------------------------------------------+
//| Check if risk limits are exceeded                               |
//+------------------------------------------------------------------+
bool PositionRiskAnalyzer::CheckRiskLimits(const RiskAnalysisResult& analysis)
{
    if (analysis.currentDrawdown > m_maxDrawdownLimit) return true;
    if (analysis.exposureRatio > m_maxExposureLimit) return true;
    if (analysis.profitFactor < m_minProfitFactor && m_totalWins + m_totalLosses > 10) return true;

    return false;
}

//+------------------------------------------------------------------+
//| Determine risk level description                                 |
//+------------------------------------------------------------------+
string PositionRiskAnalyzer::DetermineRiskLevel(const RiskAnalysisResult& analysis)
{
    if (analysis.riskLimitExceeded) return "HIGH";
    if (analysis.currentDrawdown > m_maxDrawdownLimit * 0.7) return "MEDIUM";
    if (analysis.exposureRatio > m_maxExposureLimit * 0.7) return "MEDIUM";

    return "LOW";
}

//+------------------------------------------------------------------+
//| Update peak balance                                              |
//+------------------------------------------------------------------+
void PositionRiskAnalyzer::UpdatePeakBalance(double currentBalance)
{
    if (currentBalance > m_peakBalance)
    {
        m_peakBalance = currentBalance;
    }
}

//+------------------------------------------------------------------+
//| Get account balance                                              |
//+------------------------------------------------------------------+
double PositionRiskAnalyzer::GetAccountBalance()
{
    return AccountBalance();
}

//+------------------------------------------------------------------+
//| Update historical data from trading history                     |
//+------------------------------------------------------------------+
void PositionRiskAnalyzer::UpdateHistoricalData()
{
    CalculateHistoricalMetrics();
}

//+------------------------------------------------------------------+
//| Calculate historical metrics from closed trades                 |
//+------------------------------------------------------------------+
void PositionRiskAnalyzer::CalculateHistoricalMetrics()
{
    m_totalWins = 0.0;
    m_totalLosses = 0.0;
    m_grossProfit = 0.0;
    m_grossLoss = 0.0;

    int totalHistoryOrders = OrdersHistoryTotal();

    for (int i = 0; i < totalHistoryOrders; i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
        {
            double profit = OrderProfit() + OrderSwap() + OrderCommission();

            if (profit > 0.0)
            {
                m_totalWins++;
                m_grossProfit += profit;
            }
            else if (profit < 0.0)
            {
                m_totalLosses++;
                m_grossLoss += profit; // profit is negative, so this adds to gross loss
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset historical data                                            |
//+------------------------------------------------------------------+
void PositionRiskAnalyzer::ResetHistoricalData()
{
    m_totalWins = 0.0;
    m_totalLosses = 0.0;
    m_grossProfit = 0.0;
    m_grossLoss = 0.0;
    m_maxDrawdownRecorded = 0.0;
    m_peakBalance = GetAccountBalance();
}

#endif // POSITION_RISK_ANALYZER_MQH

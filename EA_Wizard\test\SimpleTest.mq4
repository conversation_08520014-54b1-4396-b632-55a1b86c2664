//+------------------------------------------------------------------+
//|                                                   SimpleTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TradingPipelineDriver.mqh"
#include "../MainPipeline.mqh"

// 簡單的測試流水線
class TestPipeline : public MainPipeline
{
public:
    TestPipeline(string name = "TestPipeline") : MainPipeline(name) {}

protected:
    virtual void Main() override
    {
        Print("TestPipeline 執行中...");
    }
};

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 開始簡單測試 ===");

    // 測試驅動器初始化
    TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
    if(driver != NULL)
    {
        Print("✅ 驅動器創建成功");
        Print("驅動器名稱: ", driver.GetName());
        Print("驅動器類型: ", driver.GetType());
        Print("初始化狀態: ", driver.IsInitialized() ? "已初始化" : "未初始化");

        // 顯示組件信息
        if(driver.IsInitialized())
        {
            Print("容器數量: ", driver.GetTotalContainers());
            Print("註冊數量: ", driver.GetTotalRegistrations());
        }
    }
    else
    {
        Print("❌ 驅動器創建失敗");
        return;
    }

    // 測試流水線創建
    TestPipeline* pipeline = new TestPipeline("測試流水線");
    if(pipeline != NULL)
    {
        Print("✅ 流水線創建成功");
        Print("流水線名稱: ", pipeline.GetName());
        Print("流水線類型: ", pipeline.GetType());
        Print("流水線階段: ", TradingEventUtils::StageToString(pipeline.GetStage()));

        // 測試驅動器獲取
        ITradingPipelineDriver* pipelineDriver = pipeline.GetDriver();
        if(pipelineDriver != NULL)
        {
            Print("✅ 流水線驅動器獲取成功");
        }
        else
        {
            Print("❌ 流水線驅動器為空");
        }

        // 測試執行
        pipeline.Execute();
        Print("執行狀態: ", pipeline.IsExecuted() ? "已執行" : "未執行");

        delete pipeline;
    }
    else
    {
        Print("❌ 流水線創建失敗");
    }

    Print("=== 簡單測試完成 ===");
}

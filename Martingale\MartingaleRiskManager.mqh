//+------------------------------------------------------------------+
//|                                        MartingaleRiskManager.mqh |
//|                                      馬丁格爾風險管理主模組        |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_RISK_MANAGER_MQH
#define MARTINGALE_RISK_MANAGER_MQH

#property strict

//+------------------------------------------------------------------+
//| 包含所有馬丁格爾風險管理模組                                      |
//+------------------------------------------------------------------+
#include "MartingaleLevelManager.mqh"
#include "MartingaleEquityProtector.mqh"
#include "MartingaleCircuitBreaker.mqh"
#include "MartingaleProfitTracker.mqh"
#include "MartingaleRiskCoordinator.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾風險管理器主類別                                          |
//| 提供簡化的統一介面，封裝協調器的複雜性                            |
//+------------------------------------------------------------------+
class MartingaleRiskManager
{
private:
    MartingaleRiskCoordinator*  m_coordinator;      // 風險協調器
    bool                        m_initialized;      // 初始化狀態
    
public:
    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+
                                MartingaleRiskManager();
                               ~MartingaleRiskManager();
    
    //+------------------------------------------------------------------+
    //| 初始化和配置                                                     |
    //+------------------------------------------------------------------+
    bool                        Initialize();
    bool                        Update();
    bool                        IsInitialized() const { return m_initialized; }
    
    //+------------------------------------------------------------------+
    //| 快速配置方法                                                     |
    //+------------------------------------------------------------------+
    bool                        QuickSetup(int maxLevels, double initialLot, int gridStepPoints, 
                                          double lotMultiplier, double profitTarget, double equityStopPercent);
    
    //+------------------------------------------------------------------+
    //| 風險參數配置                                                     |
    //+------------------------------------------------------------------+
    bool                        SetMaxLevels(int levels);
    bool                        SetInitialLot(double lot);
    bool                        SetGridStepPoints(int points);
    bool                        SetLotMultiplier(double multiplier);
    bool                        SetOverallProfitTarget(double target);
    bool                        SetEquityStopPercent(double percent);
    void                        SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action);
    
    //+------------------------------------------------------------------+
    //| 交易決策支援                                                     |
    //+------------------------------------------------------------------+
    bool                        CanAddNextLevel(double currentPrice);
    double                      CalculateNextLevelPrice(int direction);
    double                      CalculateNextLotSize();
    bool                        CheckProfitTarget();
    bool                        ShouldExecuteCircuitBreaker();
    
    //+------------------------------------------------------------------+
    //| 序列管理                                                         |
    //+------------------------------------------------------------------+
    void                        StartSequence(double entryPrice);
    void                        AddLevel(double entryPrice);
    void                        ResetSequence();
    
    //+------------------------------------------------------------------+
    //| 狀態查詢                                                         |
    //+------------------------------------------------------------------+
    bool                        IsSequenceActive();
    int                         GetCurrentLevel();
    string                      GetRiskStatus();
    string                      GetDetailedStatus();
    
private:
    //+------------------------------------------------------------------+
    //| 內部方法                                                         |
    //+------------------------------------------------------------------+
    void                        LogEvent(string message);
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleRiskManager::MartingaleRiskManager()
{
    m_coordinator = new MartingaleRiskCoordinator();
    m_initialized = false;
    
    LogEvent("馬丁格爾風險管理器建構完成");
}

//+------------------------------------------------------------------+
//| 解構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleRiskManager::~MartingaleRiskManager()
{
    if (m_coordinator != NULL)
    {
        delete m_coordinator;
        m_coordinator = NULL;
    }
    
    LogEvent("馬丁格爾風險管理器已清理");
}

//+------------------------------------------------------------------+
//| 初始化風險管理器                                                 |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::Initialize()
{
    if (m_coordinator == NULL)
    {
        LogEvent("錯誤: 協調器未創建");
        return false;
    }
    
    if (!m_coordinator.Initialize())
    {
        LogEvent("錯誤: 協調器初始化失敗");
        return false;
    }
    
    m_initialized = true;
    LogEvent("馬丁格爾風險管理器初始化完成");
    return true;
}

//+------------------------------------------------------------------+
//| 更新風險管理器                                                   |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::Update()
{
    if (!m_initialized || m_coordinator == NULL)
    {
        return false;
    }
    
    return m_coordinator.Update();
}

//+------------------------------------------------------------------+
//| 快速設定                                                         |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::QuickSetup(int maxLevels, double initialLot, int gridStepPoints, 
                                      double lotMultiplier, double profitTarget, double equityStopPercent)
{
    if (!m_initialized)
    {
        LogEvent("錯誤: 風險管理器尚未初始化");
        return false;
    }
    
    bool success = true;
    
    success &= SetMaxLevels(maxLevels);
    success &= SetInitialLot(initialLot);
    success &= SetGridStepPoints(gridStepPoints);
    success &= SetLotMultiplier(lotMultiplier);
    success &= SetOverallProfitTarget(profitTarget);
    success &= SetEquityStopPercent(equityStopPercent);
    
    if (success)
    {
        LogEvent(StringFormat("快速設定完成 - 層級:%d, 手數:%.2f, 間隔:%d點, 乘數:%.2f, 目標:$%.2f, 保護:%.1f%%",
                             maxLevels, initialLot, gridStepPoints, lotMultiplier, profitTarget, equityStopPercent));
    }
    else
    {
        LogEvent("快速設定失敗 - 請檢查參數");
    }
    
    return success;
}

//+------------------------------------------------------------------+
//| 風險參數配置代理方法                                             |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::SetMaxLevels(int levels)
{
    return (m_coordinator != NULL) ? m_coordinator.SetMaxLevels(levels) : false;
}

bool MartingaleRiskManager::SetInitialLot(double lot)
{
    return (m_coordinator != NULL) ? m_coordinator.SetInitialLot(lot) : false;
}

bool MartingaleRiskManager::SetGridStepPoints(int points)
{
    return (m_coordinator != NULL) ? m_coordinator.SetGridStepPoints(points) : false;
}

bool MartingaleRiskManager::SetLotMultiplier(double multiplier)
{
    return (m_coordinator != NULL) ? m_coordinator.SetLotMultiplier(multiplier) : false;
}

bool MartingaleRiskManager::SetOverallProfitTarget(double target)
{
    return (m_coordinator != NULL) ? m_coordinator.SetOverallProfitTarget(target) : false;
}

bool MartingaleRiskManager::SetEquityStopPercent(double percent)
{
    return (m_coordinator != NULL) ? m_coordinator.SetEquityStopPercent(percent) : false;
}

void MartingaleRiskManager::SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action)
{
    if (m_coordinator != NULL)
    {
        m_coordinator.SetActionOnMaxLevel(action);
    }
}

//+------------------------------------------------------------------+
//| 交易決策支援代理方法                                             |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::CanAddNextLevel(double currentPrice)
{
    return (m_coordinator != NULL) ? m_coordinator.CanAddNextLevel(currentPrice) : false;
}

double MartingaleRiskManager::CalculateNextLevelPrice(int direction)
{
    return (m_coordinator != NULL) ? m_coordinator.CalculateNextLevelPrice(direction) : 0.0;
}

double MartingaleRiskManager::CalculateNextLotSize()
{
    return (m_coordinator != NULL) ? m_coordinator.CalculateNextLotSize() : 0.0;
}

bool MartingaleRiskManager::CheckProfitTarget()
{
    return (m_coordinator != NULL) ? m_coordinator.CheckProfitTarget() : false;
}

bool MartingaleRiskManager::ShouldExecuteCircuitBreaker()
{
    return (m_coordinator != NULL) ? m_coordinator.ShouldExecuteCircuitBreaker() : false;
}

//+------------------------------------------------------------------+
//| 序列管理代理方法                                                 |
//+------------------------------------------------------------------+
void MartingaleRiskManager::StartSequence(double entryPrice)
{
    if (m_coordinator != NULL)
    {
        m_coordinator.StartSequence(entryPrice);
    }
}

void MartingaleRiskManager::AddLevel(double entryPrice)
{
    if (m_coordinator != NULL)
    {
        m_coordinator.AddLevel(entryPrice);
    }
}

void MartingaleRiskManager::ResetSequence()
{
    if (m_coordinator != NULL)
    {
        m_coordinator.ResetSequence();
    }
}

//+------------------------------------------------------------------+
//| 狀態查詢代理方法                                                 |
//+------------------------------------------------------------------+
bool MartingaleRiskManager::IsSequenceActive()
{
    return (m_coordinator != NULL) ? m_coordinator.IsSequenceActive() : false;
}

int MartingaleRiskManager::GetCurrentLevel()
{
    return (m_coordinator != NULL) ? m_coordinator.GetCurrentLevel() : 0;
}

string MartingaleRiskManager::GetRiskStatus()
{
    return (m_coordinator != NULL) ? m_coordinator.GetRiskStatus() : "風險管理器未初始化";
}

string MartingaleRiskManager::GetDetailedStatus()
{
    if (m_coordinator == NULL)
    {
        return "馬丁格爾風險管理器未初始化";
    }
    
    string status = "=== 馬丁格爾風險管理器詳細狀態 ===\n";
    status += m_coordinator.GetRiskStatus() + "\n";
    status += "初始化狀態: " + (m_initialized ? "已初始化" : "未初始化") + "\n";
    status += "協調器狀態: " + (m_coordinator != NULL ? "正常" : "異常");
    
    return status;
}

//+------------------------------------------------------------------+
//| 內部方法實作                                                     |
//+------------------------------------------------------------------+
void MartingaleRiskManager::LogEvent(string message)
{
    string logMessage = StringFormat("[MartingaleRiskManager] %s", message);
    Print(logMessage);
}

#endif // MARTINGALE_RISK_MANAGER_MQH

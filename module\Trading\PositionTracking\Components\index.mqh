//+------------------------------------------------------------------+
//|                                                        index.mqh |
//|                                    EA_Wizard Framework Component |
//|                     Simplified Position Tracking System         |
//+------------------------------------------------------------------+
#ifndef POSITION_TRACKING_COMPONENTS_INDEX_MQH
#define POSITION_TRACKING_COMPONENTS_INDEX_MQH

//+------------------------------------------------------------------+
//| Simplified Position Tracking System                             |
//| Focused on core functionality with optional features            |
//+------------------------------------------------------------------+

// Core components only
#include "../PositionCollector.mqh"
#include "../PositionStatistics.mqh"

// Main simplified orchestrator
#include "../PositionTracker.mqh"

//+------------------------------------------------------------------+
//| Simplified Factory Functions                                    |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Create a basic position tracking system                         |
//+------------------------------------------------------------------+
PositionTracker* CreatePositionTracker(string symbol = "", int magicNumber = 0)
{
    return new PositionTracker(symbol, magicNumber, false);
}

//+------------------------------------------------------------------+
//| Create position tracker with risk analysis                      |
//+------------------------------------------------------------------+
PositionTracker* CreatePositionTrackerWithRisk(string symbol = "", int magicNumber = 0)
{
    return new PositionTracker(symbol, magicNumber, true);
}

//+------------------------------------------------------------------+
//| Create individual core components for custom integration        |
//+------------------------------------------------------------------+
PositionCollector* CreatePositionCollector(string symbol = "", int magicNumber = 0)
{
    return new PositionCollector(symbol, magicNumber);
}

PositionStatistics* CreatePositionStatistics()
{
    return new PositionStatistics();
}

//+------------------------------------------------------------------+
//| Simplified Factory Helper                                        |
//+------------------------------------------------------------------+
class PositionTrackingFactory
{
public:
    //--- Create basic system with default configuration
    static PositionTracker* CreateBasicSystem(string symbol = "", int magicNumber = 0)
    {
        PositionTracker* tracker = new PositionTracker(symbol, magicNumber, false);

        // Configure with reasonable defaults
        if (tracker)
        {
            tracker.SetAlertThresholds(1000.0, -500.0);
            tracker.SetUpdateInterval(5);
            tracker.SetAlertCooldown(300); // 5 minutes
        }

        return tracker;
    }

    //--- Create system with alerts and basic risk monitoring
    static PositionTracker* CreateWithAlertsAndRisk(string symbol, int magicNumber,
                                                   double profitAlert, double lossAlert)
    {
        PositionTracker* tracker = new PositionTracker(symbol, magicNumber, true);

        if (tracker)
        {
            tracker.SetAlertThresholds(profitAlert, lossAlert);
            tracker.EnableAlerts(true);
            tracker.EnableRiskAnalysis(true);
        }

        return tracker;
    }

    //--- Create lightweight system (no risk analysis, basic alerts only)
    static PositionTracker* CreateLightweight(string symbol = "", int magicNumber = 0)
    {
        PositionTracker* tracker = new PositionTracker(symbol, magicNumber, false);

        if (tracker)
        {
            tracker.EnableAlerts(false);
            tracker.EnableRiskAnalysis(false);
        }

        return tracker;
    }
};

#endif // POSITION_TRACKING_COMPONENTS_INDEX_MQH

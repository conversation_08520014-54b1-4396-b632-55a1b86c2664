//+------------------------------------------------------------------+
//|                                           TradingPipelineBase.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingEvent.mqh"
#include "interface/ITradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 流水線執行結果                                                   |
//+------------------------------------------------------------------+
class PipelineResult
{
private:
    bool m_success;                 // 執行是否成功
    string m_message;               // 結果消息
    string m_source;                // 來源流水線
    datetime m_timestamp;           // 執行時間戳
    ENUM_ERROR_LEVEL m_errorLevel;  // 錯誤級別

public:
    // 構造函數
    PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        : m_success(success),
          m_message(message),
          m_source(source),
          m_timestamp(TimeCurrent()),
          m_errorLevel(errorLevel)
    {
    }

    // 析構函數
    ~PipelineResult() {}

    // 獲取執行結果
    bool IsSuccess() const { return m_success; }

    // 獲取消息
    string GetMessage() const { return m_message; }

    // 獲取來源
    string GetSource() const { return m_source; }

    // 獲取時間戳
    datetime GetTimestamp() const { return m_timestamp; }

    // 獲取錯誤級別
    ENUM_ERROR_LEVEL GetErrorLevel() const { return m_errorLevel; }

    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("[%s] %s: %s (Level: %d)",
                          TimeToString(m_timestamp),
                          m_source,
                          m_message,
                          m_errorLevel);
    }
};

//+------------------------------------------------------------------+
//| 交易流水線基類                                                   |
//| 提供所有流水線的共同功能和模板方法模式                           |
//| 遵循 SOLID 原則和模組化設計                                      |
//+------------------------------------------------------------------+
class TradingPipelineBase : public ITradingPipeline
{
protected:
    string m_name;                          // 流水線名稱
    string m_type;                          // 流水線類型
    bool m_executed;                        // 執行狀態
    PipelineResult* m_last_result;          // 執行結果

public:
    // 構造函數
    TradingPipelineBase(string name = "",
                       string type = "TradingPipelineBase")
        : m_name(name),
          m_type(type),
          m_executed(false),
          m_last_result(new PipelineResult(false, "流水線尚未執行", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~TradingPipelineBase()
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    // 實現 ITradingPipeline 介面方法

    // 執行流水線 - 模板方法
    virtual void Execute() override
    {
        if(m_executed)
        {
            SetResult(false, "已執行，跳過重複執行", ERROR_LEVEL_WARNING);
            return;
        }

        // 執行前置檢查
        if(!PreExecuteCheck())
        {
            return;
        }

        // 執行具體邏輯 - 子類實現
        ExecuteInternal();

        // 執行後置處理
        PostExecuteProcess();

        m_executed = true;
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return m_type;
    }

    // 檢查是否已執行
    virtual bool IsExecuted() override
    {
        return m_executed;
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        m_executed = false;
        SetResult(false, "流水線尚未執行", ERROR_LEVEL_INFO);
    }

    // 獲取執行結果
    virtual PipelineResult* GetResult() const
    {
        return m_last_result;
    }

protected:
    // 設置執行結果
    void SetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }
        m_last_result = new PipelineResult(success, message, m_name, errorLevel);
    }

    // 模板方法模式的鉤子方法

    // 執行前置檢查 - 子類可以覆寫
    virtual bool PreExecuteCheck()
    {
        return true;
    }

    // 執行具體邏輯 - 子類必須實現
    virtual void ExecuteInternal() = 0;

    // 執行後置處理 - 子類可以覆寫
    virtual void PostExecuteProcess()
    {
        // 默認實現為空
    }
};

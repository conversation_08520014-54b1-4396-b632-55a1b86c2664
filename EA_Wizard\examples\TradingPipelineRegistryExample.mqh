//+------------------------------------------------------------------+
//|                                TradingPipelineRegistryExample.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineRegistry.mqh"

//+------------------------------------------------------------------+
//| TradingPipelineRegistry 使用示例                                 |
//| 展示如何使用註冊器來管理交易階段和事件                           |
//+------------------------------------------------------------------+
class TradingPipelineRegistryExample
{
public:
    // 基本使用示例
    static void BasicUsageExample()
    {
        Print("=== TradingPipelineRegistry 基本使用示例 ===");

        // 創建容器管理器
        TradingPipelineContainerManager* manager =
            new TradingPipelineContainerManager("示例管理器", "ExampleManager");

        // 創建註冊器（預設 owned = true）
        TradingPipelineRegistry* registry =
            new TradingPipelineRegistry(manager, "示例註冊器", "ExampleRegistry");

        // 創建示例流水線（使用容器作為流水線）
        TradingPipelineContainer* initPipeline = new TradingPipelineContainer("初始化流水線", "示例初始化", INIT_START);
        TradingPipelineContainer* tickPipeline = new TradingPipelineContainer("交易流水線", "示例交易", TICK_DATA_FEED);
        TradingPipelineContainer* deinitPipeline = new TradingPipelineContainer("清理流水線", "示例清理", DEINIT_CLEANUP);

        // 註冊交易事件
        Print("\n--- 註冊交易事件 ---");
        bool result1 = registry.Register(TRADING_INIT, initPipeline);
        bool result2 = registry.Register(TRADING_TICK, tickPipeline);
        bool result3 = registry.Register(TRADING_DEINIT, deinitPipeline);

        Print("註冊 TRADING_INIT: ", result1 ? "成功" : "失敗");
        Print("註冊 TRADING_TICK: ", result2 ? "成功" : "失敗");
        Print("註冊 TRADING_DEINIT: ", result3 ? "成功" : "失敗");

        // 創建階段流水線
        TradingPipelineContainer* startPipeline = new TradingPipelineContainer("開始階段", "開始階段流水線", INIT_START);
        TradingPipelineContainer* feedPipeline = new TradingPipelineContainer("數據饋送", "數據饋送流水線", TICK_DATA_FEED);
        TradingPipelineContainer* cleanupPipeline = new TradingPipelineContainer("清理階段", "清理階段流水線", DEINIT_CLEANUP);

        // 註冊交易階段
        Print("\n--- 註冊交易階段 ---");
        bool result4 = registry.Register(INIT_START, startPipeline);
        bool result5 = registry.Register(TICK_DATA_FEED, feedPipeline);
        bool result6 = registry.Register(DEINIT_CLEANUP, cleanupPipeline);

        Print("註冊 INIT_START: ", result4 ? "成功" : "失敗");
        Print("註冊 TICK_DATA_FEED: ", result5 ? "成功" : "失敗");
        Print("註冊 DEINIT_CLEANUP: ", result6 ? "成功" : "失敗");

        // 顯示註冊器狀態
        Print("\n--- 註冊器狀態 ---");
        Print(registry.GetStatusInfo());

        // 清理資源（註冊器會自動清理擁有的流水線）
        delete registry;
        delete manager;

        Print("\n=== 基本使用示例完成 ===\n");
    }

    // 進階使用示例
    static void AdvancedUsageExample()
    {
        Print("=== TradingPipelineRegistry 進階使用示例 ===");

        // 創建容器管理器
        TradingPipelineContainerManager* manager =
            new TradingPipelineContainerManager("進階管理器", "AdvancedManager", false, 20);

        // 創建註冊器（預設 owned = true）
        TradingPipelineRegistry* registry =
            new TradingPipelineRegistry(manager, "進階註冊器", "AdvancedRegistry", 30);

        // 批量註冊所有初始化階段
        Print("\n--- 批量註冊初始化階段 ---");
        ENUM_TRADING_STAGE initStages[] = {
            INIT_START, INIT_PARAMETERS, INIT_VARIABLES,
            INIT_ENVIRONMENT, INIT_INDICATORS, INIT_COMPLETE
        };

        int successCount = 0;
        for(int i = 0; i < ArraySize(initStages); i++)
        {
            // 為每個階段創建一個流水線
            string stageName = TradingEventUtils::StageToString(initStages[i]);
            TradingPipelineContainer* stagePipeline = new TradingPipelineContainer(
                stageName + "流水線",
                stageName + "階段流水線",
                initStages[i]
            );

            if(registry.Register(initStages[i], stagePipeline))
            {
                successCount++;
            }
        }
        Print("成功註冊初始化階段: ", successCount, "/", ArraySize(initStages));

        // 批量註冊所有交易階段
        Print("\n--- 批量註冊交易階段 ---");
        ENUM_TRADING_STAGE tickStages[] = {
            TICK_DATA_FEED, TICK_SIGNAL_ANALYSIS, TICK_ORDER_MANAGEMENT,
            TICK_RISK_CONTROL, TICK_LOGGING
        };

        successCount = 0;
        for(int i = 0; i < ArraySize(tickStages); i++)
        {
            // 為每個階段創建一個流水線
            string stageName = TradingEventUtils::StageToString(tickStages[i]);
            TradingPipelineContainer* stagePipeline = new TradingPipelineContainer(
                stageName + "流水線",
                stageName + "階段流水線",
                tickStages[i]
            );

            if(registry.Register(tickStages[i], stagePipeline))
            {
                successCount++;
            }
        }
        Print("成功註冊交易階段: ", successCount, "/", ArraySize(tickStages));

        // 檢查註冊狀態
        Print("\n--- 檢查註冊狀態 ---");
        Print("INIT_START 已註冊: ", registry.IsStageRegistered(INIT_START) ? "是" : "否");
        Print("TICK_DATA_FEED 已註冊: ", registry.IsStageRegistered(TICK_DATA_FEED) ? "是" : "否");
        Print("TRADING_INIT 已註冊: ", registry.IsEventRegistered(TRADING_INIT) ? "是" : "否");

        // 顯示統計信息
        Print("\n--- 統計信息 ---");
        Print("已註冊階段數量: ", registry.GetRegisteredStageCount());
        Print("已註冊事件數量: ", registry.GetRegisteredEventCount());
        Print("總註冊數量: ", registry.GetTotalRegistrations());
        Print("最大註冊數量: ", registry.GetMaxRegistrations());
        Print("是否已滿: ", registry.IsFull() ? "是" : "否");
        Print("是否為空: ", registry.IsEmpty() ? "是" : "否");

        // 測試取消註冊
        Print("\n--- 測試取消註冊 ---");
        bool unregResult1 = registry.UnregisterStage(INIT_START);
        bool unregResult2 = registry.UnregisterEvent(TRADING_INIT);
        Print("取消註冊 INIT_START: ", unregResult1 ? "成功" : "失敗");
        Print("取消註冊 TRADING_INIT: ", unregResult2 ? "成功" : "失敗");

        // 顯示最終狀態
        Print("\n--- 最終狀態 ---");
        Print(registry.GetStatusInfo());

        // 清理資源（註冊器會自動清理擁有的流水線）
        delete registry;
        delete manager;

        Print("\n=== 進階使用示例完成 ===\n");
    }

    // 錯誤處理示例
    static void ErrorHandlingExample()
    {
        Print("=== TradingPipelineRegistry 錯誤處理示例 ===");

        // 測試 NULL 管理器
        Print("\n--- 測試 NULL 管理器 ---");
        TradingPipelineRegistry* nullRegistry =
            new TradingPipelineRegistry(NULL, "NULL測試註冊器");

        TradingPipelineContainer* testPipeline1 = new TradingPipelineContainer("測試流水線1", "測試", INIT_START);
        bool result1 = nullRegistry.Register(INIT_START, testPipeline1);
        Print("使用 NULL 管理器註冊階段: ", result1 ? "成功" : "失敗");

        delete nullRegistry;

        // 測試重複註冊
        Print("\n--- 測試重複註冊 ---");
        TradingPipelineContainerManager* manager =
            new TradingPipelineContainerManager("錯誤測試管理器");

        TradingPipelineRegistry* registry =
            new TradingPipelineRegistry(manager, "錯誤測試註冊器");

        TradingPipelineContainer* testPipeline2 = new TradingPipelineContainer("測試流水線2", "測試", INIT_START);
        TradingPipelineContainer* testPipeline3 = new TradingPipelineContainer("測試流水線3", "測試", INIT_START);

        bool result2 = registry.Register(INIT_START, testPipeline2);
        bool result3 = registry.Register(INIT_START, testPipeline3); // 重複註冊
        Print("首次註冊 INIT_START: ", result2 ? "成功" : "失敗");
        Print("重複註冊 INIT_START: ", result3 ? "成功" : "失敗");

        // 測試 NULL 流水線
        Print("\n--- 測試 NULL 流水線 ---");
        bool result4 = registry.Register(INIT_PARAMETERS, NULL);
        Print("使用 NULL 流水線註冊階段: ", result4 ? "成功" : "失敗");

        // 測試禁用狀態
        Print("\n--- 測試禁用狀態 ---");
        registry.SetEnabled(false);
        TradingPipelineContainer* testPipeline4 = new TradingPipelineContainer("測試流水線4", "測試", INIT_PARAMETERS);
        bool result5 = registry.Register(INIT_PARAMETERS, testPipeline4);
        Print("禁用狀態下註冊階段: ", result5 ? "成功" : "失敗");

        // 重新啟用
        registry.SetEnabled(true);
        bool result6 = registry.Register(INIT_PARAMETERS, testPipeline4);
        Print("重新啟用後註冊階段: ", result6 ? "成功" : "失敗");

        // 測試最大註冊數量限制
        Print("\n--- 測試最大註冊數量限制 ---");
        TradingPipelineRegistry* limitedRegistry =
            new TradingPipelineRegistry(manager, "限制測試註冊器", "LimitedRegistry", 2);

        TradingPipelineContainer* eventPipeline1 = new TradingPipelineContainer("事件流水線1", "測試", INIT_START);
        TradingPipelineContainer* eventPipeline2 = new TradingPipelineContainer("事件流水線2", "測試", TICK_DATA_FEED);
        TradingPipelineContainer* eventPipeline3 = new TradingPipelineContainer("事件流水線3", "測試", DEINIT_CLEANUP);

        bool result7 = limitedRegistry.Register(TRADING_INIT, eventPipeline1);
        bool result8 = limitedRegistry.Register(TRADING_TICK, eventPipeline2);
        bool result9 = limitedRegistry.Register(TRADING_DEINIT, eventPipeline3); // 超過限制

        Print("註冊第1個事件: ", result7 ? "成功" : "失敗");
        Print("註冊第2個事件: ", result8 ? "成功" : "失敗");
        Print("註冊第3個事件（超過限制）: ", result9 ? "成功" : "失敗");

        // 測試 owned = false 的情況
        Print("\n--- 測試 owned = false ---");
        TradingPipelineRegistry* nonOwnedRegistry =
            new TradingPipelineRegistry(manager, "非擁有註冊器", "NonOwnedRegistry", 10, false);

        TradingPipelineContainer* nonOwnedPipeline = new TradingPipelineContainer("非擁有流水線", "測試", INIT_PARAMETERS);
        bool result10 = nonOwnedRegistry.Register(INIT_PARAMETERS, nonOwnedPipeline);
        Print("註冊非擁有流水線: ", result10 ? "成功" : "失敗");
        Print("註冊器擁有狀態: ", nonOwnedRegistry.IsOwned() ? "擁有" : "不擁有");

        // 清理非擁有註冊器（不會刪除流水線）
        delete nonOwnedRegistry;

        // 手動清理非擁有的流水線
        delete nonOwnedPipeline;

        // 清理資源
        delete limitedRegistry;
        delete registry;
        delete manager;

        Print("\n=== 錯誤處理示例完成 ===\n");
    }

    // 運行所有示例
    static void RunAllExamples()
    {
        Print("開始運行 TradingPipelineRegistry 所有示例...\n");

        BasicUsageExample();
        AdvancedUsageExample();
        ErrorHandlingExample();

        Print("所有示例運行完成！");
    }
};

//+------------------------------------------------------------------+
//|                                      MartingaleEquityProtector.mqh |
//|                                      馬丁格爾淨值保護器類別        |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_EQUITY_PROTECTOR_MQH
#define MARTINGALE_EQUITY_PROTECTOR_MQH

#property strict

//+------------------------------------------------------------------+
//| 馬丁格爾淨值保護器類別                                            |
//| 負責帳戶淨值保護機制 (單一責任原則)                               |
//+------------------------------------------------------------------+
class MartingaleEquityProtector
{
private:
    // 淨值保護參數
    double                  m_initialEquity;        // 初始帳戶淨值
    double                  m_equityStopPercent;    // 淨值保護百分比
    double                  m_currentDrawdownPercent; // 當前回撤百分比
    double                  m_maxDrawdownPercent;   // 最大回撤百分比
    
public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleEquityProtector();
    
    //+------------------------------------------------------------------+
    //| 初始化和配置方法                                                 |
    //+------------------------------------------------------------------+
    bool                    Initialize();
    bool                    SetEquityStopPercent(double percent);
    
    //+------------------------------------------------------------------+
    //| 保護檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckEquityProtection();
    void                    UpdateDrawdownTracking();
    
    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    double                  GetCurrentDrawdownPercent() const { return m_currentDrawdownPercent; }
    double                  GetMaxDrawdownPercent() const { return m_maxDrawdownPercent; }
    double                  GetEquityStopPercent() const { return m_equityStopPercent; }
    double                  GetInitialEquity() const { return m_initialEquity; }
    
    //+------------------------------------------------------------------+
    //| 重置方法                                                         |
    //+------------------------------------------------------------------+
    void                    Reset();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleEquityProtector::MartingaleEquityProtector()
{
    m_initialEquity = 0.0;
    m_equityStopPercent = 30.0;         // 預設30%保護
    m_currentDrawdownPercent = 0.0;
    m_maxDrawdownPercent = 0.0;
    
    Print("[MartingaleEquityProtector] 淨值保護器初始化完成");
}

//+------------------------------------------------------------------+
//| 初始化淨值保護器                                                 |
//+------------------------------------------------------------------+
bool MartingaleEquityProtector::Initialize()
{
    m_initialEquity = AccountEquity();
    if (m_initialEquity <= 0.0)
    {
        Print("[MartingaleEquityProtector] 錯誤: 無法取得帳戶淨值資訊");
        return false;
    }
    
    // 重置回撤追蹤
    m_currentDrawdownPercent = 0.0;
    m_maxDrawdownPercent = 0.0;
    
    Print("[MartingaleEquityProtector] 淨值保護器初始化完成 - 初始淨值: ", DoubleToString(m_initialEquity, 2));
    return true;
}

//+------------------------------------------------------------------+
//| 設定淨值保護百分比                                               |
//+------------------------------------------------------------------+
bool MartingaleEquityProtector::SetEquityStopPercent(double percent)
{
    if (percent < 0.0 || percent > 100.0)
    {
        Print("[MartingaleEquityProtector] 錯誤: 淨值保護百分比必須在0-100之間，當前值: ", DoubleToString(percent, 1));
        return false;
    }
    m_equityStopPercent = percent;
    Print("[MartingaleEquityProtector] 淨值保護百分比設定為: ", DoubleToString(percent, 1), "%");
    return true;
}

//+------------------------------------------------------------------+
//| 檢查淨值保護                                                     |
//+------------------------------------------------------------------+
bool MartingaleEquityProtector::CheckEquityProtection()
{
    if (m_equityStopPercent <= 0.0)
    {
        return true; // 未啟用保護
    }
    
    if (m_initialEquity <= 0.0)
    {
        Print("[MartingaleEquityProtector] 警告: 初始淨值無效，無法進行保護檢查");
        return true; // 避免誤觸發
    }
    
    double currentEquity = AccountEquity();
    double equityThreshold = m_initialEquity * (1.0 - m_equityStopPercent / 100.0);
    
    if (currentEquity <= equityThreshold)
    {
        Print("[MartingaleEquityProtector] 警告: 觸發帳戶淨值保護機制");
        Print("  當前淨值: ", DoubleToString(currentEquity, 2));
        Print("  保護線: ", DoubleToString(equityThreshold, 2));
        Print("  虧損百分比: ", DoubleToString((m_initialEquity - currentEquity) / m_initialEquity * 100.0, 2), "%");
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 更新回撤追蹤                                                     |
//+------------------------------------------------------------------+
void MartingaleEquityProtector::UpdateDrawdownTracking()
{
    if (m_initialEquity <= 0.0)
    {
        return; // 尚未初始化
    }
    
    double currentEquity = AccountEquity();
    
    // 計算當前回撤百分比
    if (currentEquity < m_initialEquity)
    {
        m_currentDrawdownPercent = (m_initialEquity - currentEquity) / m_initialEquity * 100.0;
    }
    else
    {
        m_currentDrawdownPercent = 0.0; // 沒有回撤
    }
    
    // 更新最大回撤
    if (m_currentDrawdownPercent > m_maxDrawdownPercent)
    {
        m_maxDrawdownPercent = m_currentDrawdownPercent;
        Print("[MartingaleEquityProtector] 新的最大回撤: ", DoubleToString(m_maxDrawdownPercent, 2), "%");
    }
}

//+------------------------------------------------------------------+
//| 重置淨值保護器                                                   |
//+------------------------------------------------------------------+
void MartingaleEquityProtector::Reset()
{
    m_initialEquity = AccountEquity();
    m_currentDrawdownPercent = 0.0;
    m_maxDrawdownPercent = 0.0;
    
    Print("[MartingaleEquityProtector] 淨值保護器已重置 - 新的初始淨值: ", DoubleToString(m_initialEquity, 2));
}

#endif // MARTINGALE_EQUITY_PROTECTOR_MQH

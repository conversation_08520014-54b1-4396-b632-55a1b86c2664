//+------------------------------------------------------------------+
//|                                  TradingPipelineContainerBase.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib/Collection/Vector.mqh"
#include "TradingPipelineBase.mqh"
#include "TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 交易流水線容器抽象基類                                           |
//| 繼承自 TradingPipelineBase，提供容器管理的基本功能              |
//| 遵循 SOLID 原則和模組化設計                                      |
//+------------------------------------------------------------------+
class TradingPipelineContainerBase : public TradingPipelineBase
{
protected:
    string m_description;                   // 容器描述
    bool m_isEnabled;                       // 是否啟用
    Vector<TradingPipelineBase*> m_pipelines;  // 流水線向量
    bool m_owned;                           // 是否擁有子流水線
    int m_maxPipelines;                     // 最大子流水線數量

public:
    // 構造函數
    TradingPipelineContainerBase(string name,
                                string description = "",
                                string type = "TradingPipelineContainerBase",
                                bool owned = false,
                                int maxPipelines = 50)
        : TradingPipelineBase(name, type),
          m_description(description),
          m_isEnabled(true),
          m_pipelines(owned),
          m_owned(owned),
          m_maxPipelines(maxPipelines)
    {
    }

    // 析構函數
    virtual ~TradingPipelineContainerBase()
    {
        Clear();
    }

    // 添加子流水線
    bool AddPipeline(TradingPipelineBase* pipeline)
    {
        if(pipeline == NULL)
        {
            SetResult(false, "流水線為空", ERROR_LEVEL_ERROR);
            return false;
        }

        if(m_pipelines.size() >= m_maxPipelines)
        {
            SetResult(false, "已達到最大流水線數量限制", ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.add(pipeline))
        {
            SetResult(false, "添加流水線失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        SetResult(true, "成功添加流水線", ERROR_LEVEL_INFO);
        return true;
    }

    // 移除子流水線
    bool RemovePipeline(TradingPipelineBase* pipeline)
    {
        if(pipeline == NULL)
        {
            SetResult(false, "流水線為空", ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.remove(pipeline))
        {
            SetResult(false, "移除流水線失敗", ERROR_LEVEL_WARNING);
            return false;
        }

        SetResult(true, "成功移除流水線", ERROR_LEVEL_INFO);
        return true;
    }

    // 按索引獲取子流水線
    TradingPipelineBase* GetPipeline(int index, TradingPipelineBase* defaultValue = NULL)
    {
        if(index >= 0 && index < m_pipelines.size())
        {
            SetResult(true, "成功獲取流水線", ERROR_LEVEL_INFO);
            return m_pipelines.get(index);
        }

        SetResult(false, "索引超出範圍", ERROR_LEVEL_WARNING);
        return defaultValue;
    }

    // 清空所有子流水線
    void Clear()
    {
        m_pipelines.clear();
        SetResult(true, "已清空所有流水線", ERROR_LEVEL_INFO);
    }

    // 獲取子流水線數量
    int GetPipelineCount() const
    {
        return m_pipelines.size();
    }

    // 獲取最大子流水線數量
    int GetMaxPipelines() const
    {
        return m_maxPipelines;
    }

    // 檢查是否有指定流水線
    bool HasPipeline(TradingPipelineBase* pipeline) const
    {
        if(pipeline == NULL) return false;

        foreachv(TradingPipelineBase*, p, (Vector<TradingPipelineBase*>*)GetPointer(m_pipelines))
        {
            if(p == pipeline) return true;
        }
        return false;
    }

    // 覆寫基類的前置檢查方法
    virtual bool PreExecuteCheck() override
    {
        if(!m_isEnabled)
        {
            SetResult(false, "容器已禁用，跳過執行", ERROR_LEVEL_WARNING);
            return false;
        }
        return true;
    }

    // 重置所有子流水線
    virtual void Restore() override
    {
        TradingPipelineBase::Restore(); // 調用基類方法
        int restoredCount = 0;

        foreachv(TradingPipelineBase*, pipeline, GetPointer(m_pipelines))
        {
            if(pipeline != NULL)
            {
                pipeline.Restore();
                restoredCount++;
            }
        }

        SetResult(true,
            StringFormat("重置完成，共重置 %d 個流水線", restoredCount),
            ERROR_LEVEL_INFO);
    }

    // 業務屬性管理方法

    // 獲取容器描述
    string GetDescription() const
    {
        return m_description;
    }

    // 啟用/禁用容器
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
        SetResult(true,
            StringFormat("容器已%s", enabled ? "啟用" : "禁用"),
            ERROR_LEVEL_INFO);
    }

    bool IsEnabled() const
    {
        return m_isEnabled;
    }



    // 獲取容器狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat(
            "容器名稱: %s\n"
            "類型: %s\n"
            "描述: %s\n"
            "狀態: %s\n"
            "已執行: %s\n"
            "流水線數量: %d/%d",
            m_name,
            m_type,
            m_description,
            m_isEnabled ? "啟用" : "禁用",
            m_executed ? "是" : "否",
            m_pipelines.size(),
            m_maxPipelines
        );
        return info;
    }

    // 檢查容器是否為空
    bool IsEmpty() const
    {
        return m_pipelines.size() == 0;
    }

    // 檢查容器是否已滿
    bool IsFull() const
    {
        return m_pipelines.size() >= m_maxPipelines;
    }

    // 獲取所有子流水線（用於遍歷）
    int GetAllPipelines(TradingPipelineBase* &pipelines[])
    {
        int count = m_pipelines.size();
        ArrayResize(pipelines, count);

        for(int i = 0; i < count; i++)
        {
            pipelines[i] = m_pipelines.get(i);
        }

        return count;
    }

protected:
    // 執行具體邏輯 - 子類必須實現
    virtual void ExecuteInternal() = 0;
};

#property strict

#include "../../../mql4_module/EA_Wizard/MainPipeline.mqh"
#include "../../../../Include/Indicators/TimeSeries.mqh"
#include "../../../../Include/Indicators/Oscilators.mqh"
#include "../../../../Include/Indicators/Trend.mqh"

// 定義符號和時間框架
const string SYMBOL = _Symbol;
const ENUM_TIMEFRAMES TIMEFRAME = PERIOD_CURRENT;

// RSI 指標參數
const int RSI_PERIOD = 14;
const int RSI_APPLIED_PRICE = PRICE_CLOSE;
const int RSI_PARAM_COUNT = 2;

// Bollinger Bands 指標參數
const int BANDS_PERIOD = 20;
const int BANDS_SHIFT = 0;
const double BANDS_DEVIATION = 2.0;
const int BANDS_APPLIED_PRICE = PRICE_CLOSE;
const int BANDS_PARAM_COUNT = 4;

// 初始化檢查階段
class InitCheck : public MainPipeline
{
public:
    InitCheck() : MainPipeline(INIT_INDICATORS, "InitIndicators"){}
    void Main() override { // 實現 Main 方法
        MqlParam bandsParams[];
        ArrayResize(bandsParams, BANDS_PARAM_COUNT);
        bandsParams[0].type = TYPE_INT; bandsParams[0].integer_value = BANDS_PERIOD;
        bandsParams[1].type = TYPE_INT; bandsParams[1].integer_value = BANDS_SHIFT;
        bandsParams[2].type = TYPE_DOUBLE; bandsParams[2].double_value = BANDS_DEVIATION;
        bandsParams[3].type = TYPE_INT; bandsParams[3].integer_value = BANDS_APPLIED_PRICE;

        MqlParam rsiParams[];
        ArrayResize(rsiParams, RSI_PARAM_COUNT);
        rsiParams[0].type = TYPE_INT; rsiParams[0].integer_value = RSI_PERIOD;
        rsiParams[1].type = TYPE_INT; rsiParams[1].integer_value = RSI_APPLIED_PRICE;

        IndicatorFactory factory(SYMBOL, TIMEFRAME);
        IndicatorCreationResult* bands_result = factory.CreateIndicator(IND_BANDS, bandsParams);
        if(bands_result.Status() != INDICATOR_OK)
        {
            SetResult(false, bands_result.GetMessage());
            return;
        }
        IndicatorCreationResult* rsi_result = factory.CreateIndicator(IND_RSI, rsiParams);
        if(rsi_result.Status() != INDICATOR_OK)
        {
            SetResult(false, rsi_result.GetMessage());
            return;
        }

        if(!Register(rsi_result.GetIndicator(), "RSI", "RSI 指標"))
        {
            SetResult(false, "RSI 指標註冊失敗");
            return;
        }
        if(!Register(bands_result.GetIndicator(), "Bands", "Bands 指標"))
        {
            SetResult(false, "Bands 指標註冊失敗");
            return;
        }

        SetResult(true, "指標初始化成功");
    }
}init_indicator_stage;

// CiClose *close = new CiClose();
// void InitPriceSeries(){
//     close.Create(symbol, timeframe);
// }

// CiRSI *rsi = new CiRSI();
// CiBands *bands = new CiBands();
// void InitIndicator(){
//     rsi.Create(symbol, timeframe, rsi_period, rsi_applied_price);    
//     bands.Create(symbol, timeframe, bands_period, bands_shift, bands_deviation, bands_applied_price);    
// }

// void IndicatorLogic(){
//     double curr_close = close.GetData(0);
//     double prev_close = close.GetData(1);

//     double curr_rsi = rsi.GetData(0);
//     double prev_rsi = rsi.GetData(1);

//     double curr_base = bands.GetData(0,0);
//     double prev_base = bands.GetData(0,1);
//     double curr_upper = bands.GetData(1,0);
//     double prev_upper = bands.GetData(1,1);
//     double curr_lower = bands.GetData(2,0);
//     double prev_lower = bands.GetData(2,1);
//     if (curr_rsi > 70) 
//     {
//         // 超買
//     } else if (curr_rsi < 30) 
//     {
//         // 超賣
//     }
//     else
//     {
//         // 中性區域
//     } 
// }

// 定義狀態碼
enum ENUM_INDICATOR_CREATION_STATUS {
    INDICATOR_OK = 0,
    INDICATOR_PARAM_ERROR = 1,
    INDICATOR_TYPE_UNSUPPORTED = 2,
    INDICATOR_CREATION_FAILED = 3
};

class IndicatorCreationResult
{
private:
    ENUM_INDICATOR_CREATION_STATUS m_status;
    string m_message;
    CIndicator* m_indicator;
    
public:
    // 建構函數
    IndicatorCreationResult(ENUM_INDICATOR_CREATION_STATUS status, string message, CIndicator* indicator = NULL)
        : m_status(status), m_message(message), m_indicator(indicator)
    {
    }
    
    // 獲取狀態
    ENUM_INDICATOR_CREATION_STATUS Status()
    {
        return m_status;
    }
    
    // 獲取消息
    string GetMessage()
    {
        return m_message;
    }
    
    // 獲取指標
    CIndicator* GetIndicator()
    {
        return m_indicator;
    }
};

class IndicatorFactory
{
private:
    string m_symbol;
    ENUM_TIMEFRAMES m_timeframe;

    // 共用參數驗證方法
    // 檢查參數數量與型別是否正確，不正確則回傳錯誤結果
    IndicatorCreationResult* CheckParams(MqlParam &params[], Vector<int>* type_col, string name)
    {
        // 驗證參數數量
        if(ArraySize(params) != type_col.size())
        {
            return new IndicatorCreationResult(
                INDICATOR_PARAM_ERROR,
                name + " 指標需要 " + type_col.size() + " 個參數，但提供了 " + ArraySize(params) + " 個參數"
            );
        }
        // 驗證參數型別
        for(int i = 0; i < type_col.size(); i++)
        {
            if(params[i].type != type_col.get(i))
            {
                return new IndicatorCreationResult(
                    INDICATOR_PARAM_ERROR,
                    name + " 指標參數" + i + "類型錯誤, 需要 " + type_col.get(i) + " 類型"
                );
            }
        }
        return NULL; // 驗證通過
    }

    // 創建 Bands 指標
    IndicatorCreationResult* CreateBands(MqlParam &params[])
    {
        // Bands 參數型別：int, int, double, int
        int type_arr[] = {TYPE_INT, TYPE_INT, TYPE_DOUBLE, TYPE_INT};
        Vector<int>* type_col = new Vector<int>();
        type_col.addAll(type_arr);

        // 驗證參數
        IndicatorCreationResult* check = CheckParams(params, type_col, "Bands");
        if(check) return check;

        // 創建 Bands 指標
        CiBands *indicator = new CiBands();
        if(!indicator.Create(m_symbol, m_timeframe, params[0].integer_value, params[1].integer_value, params[2].double_value, params[3].integer_value))
        {
            return new IndicatorCreationResult(INDICATOR_CREATION_FAILED, "Bands 指標創建失敗");
        }
        return new IndicatorCreationResult(INDICATOR_OK, "Bands 指標創建成功", indicator);
    }

    // 創建 RSI 指標
    IndicatorCreationResult* CreateRSI(MqlParam &params[])
    {
        // RSI 參數型別：int, int
        int type_arr[] = {TYPE_INT, TYPE_INT};
        Vector<int>* type_col = new Vector<int>();
        type_col.addAll(type_arr);

        // 驗證參數
        IndicatorCreationResult* check = CheckParams(params, type_col, "RSI");
        if(check) return check;

        // 創建 RSI 指標
        CiRSI *indicator = new CiRSI();
        if(!indicator.Create(m_symbol, m_timeframe, params[0].integer_value, params[1].integer_value))
        {
            return new IndicatorCreationResult(INDICATOR_CREATION_FAILED, "RSI 指標創建失敗");
        }
        return new IndicatorCreationResult(INDICATOR_OK, "RSI 指標創建成功", indicator);
    }

public:
    // 建構函數
    IndicatorFactory(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT)
        : m_symbol(symbol == "" ? Symbol() : symbol),
          m_timeframe(timeframe == PERIOD_CURRENT ? (ENUM_TIMEFRAMES)Period() : timeframe)
    {
    }

    // 根據類型創建對應指標
    IndicatorCreationResult* CreateIndicator(ENUM_INDICATOR type, MqlParam &params[])
    {
        switch(type)
        {
            case IND_BANDS:
                return CreateBands(params);
            case IND_RSI:
                return CreateRSI(params);
            default:
                return new IndicatorCreationResult(INDICATOR_TYPE_UNSUPPORTED, "不支持的指標類型");
        }
    }
};


















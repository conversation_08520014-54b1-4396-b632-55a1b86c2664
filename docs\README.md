# EA_Wizard-dev2 Project Documentation

## Introduction

EA_Wizard-dev2 is an Expert Advisor project built using the EA_Wizard framework. This project implements a sophisticated trading system with modular architecture following EA_Wizard framework guidelines.

## Project Structure

```
EA_Wizard-dev2/
├── src/                    # Source code directory
│   ├── OnInit/            # Initialization modules
│   ├── OnTick/            # Tick processing modules
│   ├── OnDeinit/          # Cleanup modules
│   └── Config/            # Configuration modules
├── docs/                  # Documentation
└── README.md              # Project overview
```

## Framework Components

This project utilizes the EA_Wizard framework's core components:

- **TradingController**: EA lifecycle management controller
- **MainPipeline**: Abstract pipeline class for implementing main business logic

## Getting Started

1. Ensure you have MetaTrader 4/5 platform installed
2. Access to the EA_Wizard framework files
3. Basic understanding of MQL4/MQL5 programming

## Development Guidelines

- Follow EA_Wizard framework patterns
- Use MainPipeline or TradingController as primary headers
- Maintain modular structure with separate directories for different lifecycle stages

## License

Copyright 2024, EA_Wizard Framework

//+------------------------------------------------------------------+
//|                                              ErrorHandler.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ERROR_HANDLER_MQH
#define ERROR_HANDLER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Error Severity Enumeration                                       |
//+------------------------------------------------------------------+
enum ENUM_ERROR_SEVERITY
{
    ERROR_SEVERITY_INFO = 0,        // Informational
    ERROR_SEVERITY_WARNING = 1,     // Warning
    ERROR_SEVERITY_ERROR = 2,       // Error
    ERROR_SEVERITY_CRITICAL = 3,    // Critical error
    ERROR_SEVERITY_FATAL = 4        // Fatal error
};

//+------------------------------------------------------------------+
//| Error Category Enumeration                                       |
//+------------------------------------------------------------------+
enum ENUM_ERROR_CATEGORY
{
    ERROR_CAT_GENERAL = 0,          // General error
    ERROR_CAT_TRADING = 1,          // Trading related
    ERROR_CAT_INDICATOR = 2,        // Indicator related
    ERROR_CAT_VALIDATION = 3,       // Validation related
    ERROR_CAT_NETWORK = 4,          // Network/connection related
    ERROR_CAT_FILE = 5,             // File operation related
    ERROR_CAT_MEMORY = 6            // Memory related
};

//+------------------------------------------------------------------+
//| Error Information Structure                                       |
//+------------------------------------------------------------------+
struct ErrorInfo
{
    int                   errorCode;        // Error code
    ENUM_ERROR_SEVERITY   severity;         // Error severity
    ENUM_ERROR_CATEGORY   category;         // Error category
    string                message;          // Error message
    string                details;          // Additional details
    string                source;           // Error source (function/class)
    datetime              timestamp;        // Error timestamp
    int                   lineNumber;       // Line number (if available)
    string                fileName;         // File name (if available)
};

//+------------------------------------------------------------------+
//| Error Statistics Structure                                        |
//+------------------------------------------------------------------+
struct ErrorStats
{
    int                   totalErrors;      // Total error count
    int                   infoCount;        // Info count
    int                   warningCount;     // Warning count
    int                   errorCount;       // Error count
    int                   criticalCount;    // Critical error count
    int                   fatalCount;       // Fatal error count
    datetime              lastErrorTime;    // Last error timestamp
    int                   lastErrorCode;    // Last error code
};

//+------------------------------------------------------------------+
//| ErrorHandler Class                                               |
//| Implementation of comprehensive error handling system           |
//+------------------------------------------------------------------+
class ErrorHandler : public BaseComponent
{
private:
    bool                  m_enableLogging;      // Enable error logging
    bool                  m_enableAlerts;       // Enable error alerts
    bool                  m_enableRecovery;     // Enable automatic recovery
    ENUM_ERROR_SEVERITY   m_alertThreshold;     // Alert threshold
    int                   m_maxErrorHistory;    // Maximum error history
    
    // Error tracking
    ErrorInfo             m_errorHistory[];     // Error history array
    int                   m_errorIndex;         // Current error index
    ErrorStats            m_errorStats;         // Error statistics
    
    // Recovery settings
    bool                  m_autoRetry;          // Enable auto retry
    int                   m_maxRetries;         // Maximum retry attempts
    int                   m_retryDelay;         // Retry delay (ms)

public:
    //--- Constructor and Destructor
                          ErrorHandler(bool enableLogging = true, bool enableAlerts = true);
    virtual              ~ErrorHandler();
    
    //--- Configuration methods
    void                  SetEnableLogging(bool enable) { m_enableLogging = enable; }
    void                  SetEnableAlerts(bool enable) { m_enableAlerts = enable; }
    void                  SetEnableRecovery(bool enable) { m_enableRecovery = enable; }
    void                  SetAlertThreshold(ENUM_ERROR_SEVERITY threshold) { m_alertThreshold = threshold; }
    void                  SetMaxErrorHistory(int maxHistory);
    void                  SetRetrySettings(bool autoRetry, int maxRetries, int delayMs);
    
    //--- Information methods
    bool                  IsLoggingEnabled() const { return m_enableLogging; }
    bool                  IsAlertsEnabled() const { return m_enableAlerts; }
    ErrorStats            GetErrorStats() const { return m_errorStats; }
    int                   GetErrorHistoryCount() const;
    
    //--- Error handling methods
    void                  HandleError(int errorCode, ENUM_ERROR_SEVERITY severity = ERROR_SEVERITY_ERROR,
                                     ENUM_ERROR_CATEGORY category = ERROR_CAT_GENERAL,
                                     string message = "", string details = "", string source = "");
    void                  HandleLastError(ENUM_ERROR_SEVERITY severity = ERROR_SEVERITY_ERROR,
                                         ENUM_ERROR_CATEGORY category = ERROR_CAT_GENERAL,
                                         string source = "");
    void                  HandleTradingError(int errorCode, string source = "");
    void                  HandleIndicatorError(int errorCode, string source = "");
    void                  HandleValidationError(string message, string source = "");
    
    //--- Error information methods
    ErrorInfo             GetLastError() const;
    ErrorInfo             GetErrorByIndex(int index) const;
    string                GetErrorMessage(int errorCode) const;
    ENUM_ERROR_SEVERITY   GetErrorSeverity(int errorCode) const;
    ENUM_ERROR_CATEGORY   GetErrorCategory(int errorCode) const;
    
    //--- Error recovery methods
    bool                  CanRecover(int errorCode) const;
    bool                  AttemptRecovery(int errorCode, string source = "");
    bool                  RetryOperation(int errorCode, int maxAttempts = 3);
    
    //--- Error analysis methods
    bool                  IsRetryableError(int errorCode) const;
    bool                  IsCriticalError(int errorCode) const;
    bool                  IsNetworkError(int errorCode) const;
    bool                  IsTradingError(int errorCode) const;
    
    //--- Override base class methods
    virtual bool          OnInitialize() override;
    virtual bool          OnValidate() override;
    virtual bool          OnUpdate() override;
    
    //--- Utility methods
    void                  ClearErrorHistory();
    void                  PrintErrorStats();
    void                  PrintErrorHistory(int count = 10);
    string                SeverityToString(ENUM_ERROR_SEVERITY severity) const;
    string                CategoryToString(ENUM_ERROR_CATEGORY category) const;
    string                FormatErrorInfo(const ErrorInfo& error) const;
    
private:
    //--- Internal methods
    void                  AddToHistory(const ErrorInfo& error);
    void                  UpdateStats(const ErrorInfo& error);
    void                  LogError(const ErrorInfo& error);
    void                  AlertError(const ErrorInfo& error);
    ErrorInfo             CreateErrorInfo(int errorCode, ENUM_ERROR_SEVERITY severity,
                                         ENUM_ERROR_CATEGORY category, string message,
                                         string details, string source);
    bool                  ShouldAlert(ENUM_ERROR_SEVERITY severity) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
ErrorHandler::ErrorHandler(bool enableLogging = true, bool enableAlerts = true) : BaseComponent("ErrorHandler")
{
    m_enableLogging = enableLogging;
    m_enableAlerts = enableAlerts;
    m_enableRecovery = true;
    m_alertThreshold = ERROR_SEVERITY_ERROR;
    m_maxErrorHistory = 100;
    m_errorIndex = 0;
    
    m_autoRetry = true;
    m_maxRetries = 3;
    m_retryDelay = 1000;
    
    // Initialize statistics
    ZeroMemory(m_errorStats);
    
    // Initialize error history
    ArrayResize(m_errorHistory, m_maxErrorHistory);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
ErrorHandler::~ErrorHandler()
{
    ArrayFree(m_errorHistory);
}

//+------------------------------------------------------------------+
//| Initialize error handler                                         |
//+------------------------------------------------------------------+
bool ErrorHandler::OnInitialize()
{
    if (m_enableLogging)
    {
        Print("ERROR_HANDLER: Error handler initialized");
        Print("ERROR_HANDLER: Logging=", (m_enableLogging ? "ON" : "OFF"),
              " Alerts=", (m_enableAlerts ? "ON" : "OFF"),
              " Recovery=", (m_enableRecovery ? "ON" : "OFF"));
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate error handler parameters                               |
//+------------------------------------------------------------------+
bool ErrorHandler::OnValidate()
{
    if (m_maxErrorHistory <= 0)
    {
        SetError(1501, "Invalid maximum error history size");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update error handler                                             |
//+------------------------------------------------------------------+
bool ErrorHandler::OnUpdate()
{
    // Periodic cleanup or maintenance can be added here
    return true;
}

//+------------------------------------------------------------------+
//| Set maximum error history                                        |
//+------------------------------------------------------------------+
void ErrorHandler::SetMaxErrorHistory(int maxHistory)
{
    if (maxHistory > 0)
    {
        m_maxErrorHistory = maxHistory;
        ArrayResize(m_errorHistory, m_maxErrorHistory);
        
        if (m_errorIndex >= m_maxErrorHistory)
        {
            m_errorIndex = 0;
        }
    }
}

//+------------------------------------------------------------------+
//| Set retry settings                                               |
//+------------------------------------------------------------------+
void ErrorHandler::SetRetrySettings(bool autoRetry, int maxRetries, int delayMs)
{
    m_autoRetry = autoRetry;
    m_maxRetries = MathMax(1, maxRetries);
    m_retryDelay = MathMax(100, delayMs);
}

//+------------------------------------------------------------------+
//| Handle error with full parameters                                |
//+------------------------------------------------------------------+
void ErrorHandler::HandleError(int errorCode, ENUM_ERROR_SEVERITY severity = ERROR_SEVERITY_ERROR,
                              ENUM_ERROR_CATEGORY category = ERROR_CAT_GENERAL,
                              string message = "", string details = "", string source = "")
{
    // Create error info
    ErrorInfo error = CreateErrorInfo(errorCode, severity, category, message, details, source);
    
    // Add to history and update statistics
    AddToHistory(error);
    UpdateStats(error);
    
    // Log error if enabled
    if (m_enableLogging)
    {
        LogError(error);
    }
    
    // Send alert if enabled and threshold met
    if (m_enableAlerts && ShouldAlert(severity))
    {
        AlertError(error);
    }
    
    // Attempt recovery if enabled and possible
    if (m_enableRecovery && CanRecover(errorCode))
    {
        AttemptRecovery(errorCode, source);
    }
}

//+------------------------------------------------------------------+
//| Handle last MQL4 error                                           |
//+------------------------------------------------------------------+
void ErrorHandler::HandleLastError(ENUM_ERROR_SEVERITY severity = ERROR_SEVERITY_ERROR,
                                  ENUM_ERROR_CATEGORY category = ERROR_CAT_GENERAL,
                                  string source = "")
{
    int lastError = GetLastError();
    if (lastError != 0)
    {
        string message = GetErrorMessage(lastError);
        HandleError(lastError, severity, category, message, "", source);
    }
}

//+------------------------------------------------------------------+
//| Handle trading specific error                                    |
//+------------------------------------------------------------------+
void ErrorHandler::HandleTradingError(int errorCode, string source = "")
{
    ENUM_ERROR_SEVERITY severity = GetErrorSeverity(errorCode);
    HandleError(errorCode, severity, ERROR_CAT_TRADING, GetErrorMessage(errorCode), "", source);
}

//+------------------------------------------------------------------+
//| Handle indicator specific error                                  |
//+------------------------------------------------------------------+
void ErrorHandler::HandleIndicatorError(int errorCode, string source = "")
{
    HandleError(errorCode, ERROR_SEVERITY_WARNING, ERROR_CAT_INDICATOR, 
                GetErrorMessage(errorCode), "", source);
}

//+------------------------------------------------------------------+
//| Handle validation error                                          |
//+------------------------------------------------------------------+
void ErrorHandler::HandleValidationError(string message, string source = "")
{
    HandleError(0, ERROR_SEVERITY_WARNING, ERROR_CAT_VALIDATION, message, "", source);
}

//+------------------------------------------------------------------+
//| Get last error from history                                      |
//+------------------------------------------------------------------+
ErrorInfo ErrorHandler::GetLastError() const
{
    if (m_errorStats.totalErrors > 0)
    {
        int lastIndex = (m_errorIndex - 1 + m_maxErrorHistory) % m_maxErrorHistory;
        return m_errorHistory[lastIndex];
    }
    
    ErrorInfo emptyError;
    ZeroMemory(emptyError);
    return emptyError;
}

//+------------------------------------------------------------------+
//| Get error by index                                               |
//+------------------------------------------------------------------+
ErrorInfo ErrorHandler::GetErrorByIndex(int index) const
{
    ErrorInfo emptyError;
    ZeroMemory(emptyError);
    
    if (index >= 0 && index < MathMin(m_errorStats.totalErrors, m_maxErrorHistory))
    {
        int actualIndex = (m_errorIndex - 1 - index + m_maxErrorHistory) % m_maxErrorHistory;
        return m_errorHistory[actualIndex];
    }
    
    return emptyError;
}

//+------------------------------------------------------------------+
//| Get error message for error code                                 |
//+------------------------------------------------------------------+
string ErrorHandler::GetErrorMessage(int errorCode) const
{
    switch(errorCode)
    {
        case ERR_NO_ERROR:              return "No error";
        case ERR_NO_RESULT:             return "No result";
        case ERR_COMMON_ERROR:          return "Common error";
        case ERR_INVALID_TRADE_PARAMETERS: return "Invalid trade parameters";
        case ERR_SERVER_BUSY:           return "Server is busy";
        case ERR_OLD_VERSION:           return "Old version of the client terminal";
        case ERR_NO_CONNECTION:         return "No connection with trade server";
        case ERR_NOT_ENOUGH_RIGHTS:     return "Not enough rights";
        case ERR_TOO_FREQUENT_REQUESTS: return "Too frequent requests";
        case ERR_MALFUNCTIONAL_TRADE:   return "Malfunctional trade operation";
        case ERR_ACCOUNT_DISABLED:      return "Account disabled";
        case ERR_INVALID_ACCOUNT:       return "Invalid account";
        case ERR_TRADE_TIMEOUT:         return "Trade timeout";
        case ERR_INVALID_PRICE:         return "Invalid price";
        case ERR_INVALID_STOPS:         return "Invalid stops";
        case ERR_INVALID_TRADE_VOLUME:  return "Invalid trade volume";
        case ERR_MARKET_CLOSED:         return "Market is closed";
        case ERR_TRADE_DISABLED:        return "Trade is disabled";
        case ERR_NOT_ENOUGH_MONEY:      return "Not enough money";
        case ERR_PRICE_CHANGED:         return "Price changed";
        case ERR_OFF_QUOTES:            return "Off quotes";
        case ERR_BROKER_BUSY:           return "Broker is busy";
        case ERR_REQUOTE:               return "Requote";
        case ERR_ORDER_LOCKED:          return "Order is locked";
        case ERR_LONG_POSITIONS_ONLY_ALLOWED: return "Long positions only allowed";
        case ERR_TOO_MANY_REQUESTS:     return "Too many requests";
        case ERR_TRADE_MODIFY_DENIED:   return "Modification denied because order too close to market";
        case ERR_TRADE_CONTEXT_BUSY:    return "Trade context is busy";
        case ERR_TRADE_EXPIRATION_DENIED: return "Expirations are denied by broker";
        case ERR_TRADE_TOO_MANY_ORDERS: return "The amount of open and pending orders has reached the limit";
        default:                        return "Unknown error: " + IntegerToString(errorCode);
    }
}

//+------------------------------------------------------------------+
//| Get error severity for error code                               |
//+------------------------------------------------------------------+
ENUM_ERROR_SEVERITY ErrorHandler::GetErrorSeverity(int errorCode) const
{
    switch(errorCode)
    {
        case ERR_NO_ERROR:
        case ERR_NO_RESULT:
            return ERROR_SEVERITY_INFO;
            
        case ERR_REQUOTE:
        case ERR_PRICE_CHANGED:
        case ERR_OFF_QUOTES:
            return ERROR_SEVERITY_WARNING;
            
        case ERR_NOT_ENOUGH_MONEY:
        case ERR_INVALID_TRADE_VOLUME:
        case ERR_INVALID_PRICE:
        case ERR_INVALID_STOPS:
            return ERROR_SEVERITY_ERROR;
            
        case ERR_NO_CONNECTION:
        case ERR_ACCOUNT_DISABLED:
        case ERR_TRADE_DISABLED:
        case ERR_MARKET_CLOSED:
            return ERROR_SEVERITY_CRITICAL;
            
        default:
            return ERROR_SEVERITY_ERROR;
    }
}

//+------------------------------------------------------------------+
//| Check if error is retryable                                     |
//+------------------------------------------------------------------+
bool ErrorHandler::IsRetryableError(int errorCode) const
{
    switch(errorCode)
    {
        case ERR_SERVER_BUSY:
        case ERR_BROKER_BUSY:
        case ERR_TRADE_CONTEXT_BUSY:
        case ERR_TOO_FREQUENT_REQUESTS:
        case ERR_REQUOTE:
        case ERR_PRICE_CHANGED:
        case ERR_OFF_QUOTES:
            return true;
            
        default:
            return false;
    }
}

//+------------------------------------------------------------------+
//| Check if error is critical                                       |
//+------------------------------------------------------------------+
bool ErrorHandler::IsCriticalError(int errorCode) const
{
    return (GetErrorSeverity(errorCode) >= ERROR_SEVERITY_CRITICAL);
}

//+------------------------------------------------------------------+
//| Check if can recover from error                                  |
//+------------------------------------------------------------------+
bool ErrorHandler::CanRecover(int errorCode) const
{
    return IsRetryableError(errorCode);
}

//+------------------------------------------------------------------+
//| Attempt error recovery                                           |
//+------------------------------------------------------------------+
bool ErrorHandler::AttemptRecovery(int errorCode, string source = "")
{
    if (!CanRecover(errorCode))
        return false;
    
    if (m_enableLogging)
    {
        Print("ERROR_HANDLER: Attempting recovery for error ", errorCode, " from ", source);
    }
    
    // Add specific recovery logic here based on error type
    switch(errorCode)
    {
        case ERR_TRADE_CONTEXT_BUSY:
            Sleep(m_retryDelay);
            return true;
            
        case ERR_SERVER_BUSY:
        case ERR_BROKER_BUSY:
            Sleep(m_retryDelay * 2);
            return true;
            
        default:
            Sleep(m_retryDelay);
            return true;
    }
}

//+------------------------------------------------------------------+
//| Print error statistics                                           |
//+------------------------------------------------------------------+
void ErrorHandler::PrintErrorStats()
{
    Print("=== ERROR STATISTICS ===");
    Print("Total Errors: ", m_errorStats.totalErrors);
    Print("Info: ", m_errorStats.infoCount);
    Print("Warnings: ", m_errorStats.warningCount);
    Print("Errors: ", m_errorStats.errorCount);
    Print("Critical: ", m_errorStats.criticalCount);
    Print("Fatal: ", m_errorStats.fatalCount);
    Print("Last Error: ", m_errorStats.lastErrorCode, " at ", TimeToString(m_errorStats.lastErrorTime));
}

//+------------------------------------------------------------------+
//| Convert severity to string                                       |
//+------------------------------------------------------------------+
string ErrorHandler::SeverityToString(ENUM_ERROR_SEVERITY severity) const
{
    switch(severity)
    {
        case ERROR_SEVERITY_INFO:     return "INFO";
        case ERROR_SEVERITY_WARNING:  return "WARNING";
        case ERROR_SEVERITY_ERROR:    return "ERROR";
        case ERROR_SEVERITY_CRITICAL: return "CRITICAL";
        case ERROR_SEVERITY_FATAL:    return "FATAL";
        default:                      return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Convert category to string                                       |
//+------------------------------------------------------------------+
string ErrorHandler::CategoryToString(ENUM_ERROR_CATEGORY category) const
{
    switch(category)
    {
        case ERROR_CAT_GENERAL:    return "GENERAL";
        case ERROR_CAT_TRADING:    return "TRADING";
        case ERROR_CAT_INDICATOR:  return "INDICATOR";
        case ERROR_CAT_VALIDATION: return "VALIDATION";
        case ERROR_CAT_NETWORK:    return "NETWORK";
        case ERROR_CAT_FILE:       return "FILE";
        case ERROR_CAT_MEMORY:     return "MEMORY";
        default:                   return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Create error info structure                                      |
//+------------------------------------------------------------------+
ErrorInfo ErrorHandler::CreateErrorInfo(int errorCode, ENUM_ERROR_SEVERITY severity,
                                        ENUM_ERROR_CATEGORY category, string message,
                                        string details, string source)
{
    ErrorInfo error;
    error.errorCode = errorCode;
    error.severity = severity;
    error.category = category;
    error.message = (message == "") ? GetErrorMessage(errorCode) : message;
    error.details = details;
    error.source = source;
    error.timestamp = TimeCurrent();
    error.lineNumber = 0;
    error.fileName = "";
    
    return error;
}

//+------------------------------------------------------------------+
//| Add error to history                                             |
//+------------------------------------------------------------------+
void ErrorHandler::AddToHistory(const ErrorInfo& error)
{
    m_errorHistory[m_errorIndex] = error;
    m_errorIndex = (m_errorIndex + 1) % m_maxErrorHistory;
}

//+------------------------------------------------------------------+
//| Update error statistics                                          |
//+------------------------------------------------------------------+
void ErrorHandler::UpdateStats(const ErrorInfo& error)
{
    m_errorStats.totalErrors++;
    m_errorStats.lastErrorTime = error.timestamp;
    m_errorStats.lastErrorCode = error.errorCode;
    
    switch(error.severity)
    {
        case ERROR_SEVERITY_INFO:     m_errorStats.infoCount++; break;
        case ERROR_SEVERITY_WARNING:  m_errorStats.warningCount++; break;
        case ERROR_SEVERITY_ERROR:    m_errorStats.errorCount++; break;
        case ERROR_SEVERITY_CRITICAL: m_errorStats.criticalCount++; break;
        case ERROR_SEVERITY_FATAL:    m_errorStats.fatalCount++; break;
    }
}

//+------------------------------------------------------------------+
//| Log error                                                        |
//+------------------------------------------------------------------+
void ErrorHandler::LogError(const ErrorInfo& error)
{
    Print("ERROR_HANDLER: [", SeverityToString(error.severity), "] [", 
          CategoryToString(error.category), "] ", error.message);
    
    if (error.details != "")
    {
        Print("ERROR_HANDLER: Details: ", error.details);
    }
    
    if (error.source != "")
    {
        Print("ERROR_HANDLER: Source: ", error.source);
    }
}

//+------------------------------------------------------------------+
//| Send error alert                                                 |
//+------------------------------------------------------------------+
void ErrorHandler::AlertError(const ErrorInfo& error)
{
    string alertMsg = "ERROR: " + error.message;
    if (error.source != "")
    {
        alertMsg += " (Source: " + error.source + ")";
    }
    
    Alert(alertMsg);
}

//+------------------------------------------------------------------+
//| Check if should alert for severity                               |
//+------------------------------------------------------------------+
bool ErrorHandler::ShouldAlert(ENUM_ERROR_SEVERITY severity) const
{
    return (severity >= m_alertThreshold);
}

#endif // ERROR_HANDLER_MQH

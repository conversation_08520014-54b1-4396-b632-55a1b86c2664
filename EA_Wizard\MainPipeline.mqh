#property strict

#include "TradingPipeline.mqh"
#include "TradingPipelineDriver.mqh"

class MainPipeline : public TradingPipeline
{
public:
    MainPipeline(ENUM_TRADING_STAGE stage = INIT_START,
                      string name = "",
                      string type = "MainPipeline",
                      ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, type, stage, driver!=NULL?driver:TradingPipelineDriver::GetInstance())
    {
        
        if(m_driver != NULL)
        {
            TradingPipelineRegistry* registry = m_driver.GetRegistry();
            TradingMessageHandler* errorHandler = m_driver.GetErrorHandler();
            if(registry != NULL)
            {
                registry.Register(GetPointer(this));
                errorHandler.HandlePipelineResult(registry.GetResult());
                errorHandler.GetLastMessage(new LogMessageVisitor());
            }
        }
        
    }

protected:
    // 主程序 - 子類必須實現
    virtual void Main() = 0;

    // 獲取錯誤處理器
    TradingMessageHandler* GetMessageHandler()
    {
        if(m_driver == NULL)
        {
            return NULL;
        }

        return m_driver.GetErrorHandler();
    }

    // 獲取對象詳細信息
    ObjectDetail* GetObjectDetail(string name)
    {
        ObjectRegistry* registry = m_driver.GetObjectRegistry();

        if(
            m_driver == NULL ||
            registry == NULL
          )
        {
            return new ObjectDetail("", "", "", NULL, "");
        }

         return registry.GetObjectDetail(name);
    }

    // 註冊對象到 ObjectRegistry
    bool Register(void* object, string name, string description)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Register(name, object, name, description);
    }

    // 從 ObjectRegistry 移除對象
    bool Unregister(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Unregister(name);
    }

    // 檢查對象是否已註冊
    bool HasObject(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Contains(name);
    }

    // 註冊不同類型的值
    bool Register(long value, string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetLongRegistry().Register(name, value);
    }

    bool Register(double value, string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetDoubleRegistry().Register(name, value);
    }

    bool Register(string value, string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetStringRegistry().Register(name, value);
    }

    //+------------------------------------------------------------------+
    //| 帶描述的註冊方法                                                 |
    //| 允許為註冊項目添加描述信息                                       |
    //+------------------------------------------------------------------+

    // 註冊 Long 值（帶描述）
    bool Register(long value, string name, string description)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetLongRegistry().Register(name, value, description);
    }

    // 註冊 Double 值（帶描述）
    bool Register(double value, string name, string description)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetDoubleRegistry().Register(name, value, description);
    }

    // 註冊 String 值（帶描述）
    bool Register(string value, string name, string description)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetStringRegistry().Register(name, value, description);
    }

    // 移除不同類型的值
    bool UnregisterLong(string name)
    {
        return m_driver.GetLongRegistry().Unregister(name);
    }
    
    bool UnregisterDouble(string name)
    {
        return m_driver.GetDoubleRegistry().Unregister(name);
    }
    
    bool UnregisterString(string name)
    {
        return m_driver.GetStringRegistry().Unregister(name);
    }

    // 獲取所有已註冊的值
    int GetAllRegistered(string &names[], long &values[])
    {
        if(m_driver == NULL)
        {
            return 0;
        }

        LongRegistry* registry = m_driver.GetLongRegistry();
        if(registry == NULL)
        {
            return 0;
        }

        ArrayResize(names, registry.GetRegisteredCount());
        ArrayResize(values, registry.GetRegisteredCount());

        int count = registry.GetAllKeys(names);
        registry.GetAllValues(values);

        return count;
    }
    
    int GetAllRegistered(string &names[], double &values[])
    {
        if(m_driver == NULL)
        {
            return 0;
        }

        DoubleRegistry* registry = m_driver.GetDoubleRegistry();
        if(registry == NULL)
        {
            return 0;
        }

        ArrayResize(names, registry.GetRegisteredCount());
        ArrayResize(values, registry.GetRegisteredCount());

        int count = registry.GetAllKeys(names);
        registry.GetAllValues(values);

        return count;
    }
    
    int GetAllRegistered(string &names[], string &values[])
    {
        if(m_driver == NULL)
        {
            return 0;
        }

        StringRegistry* registry = m_driver.GetStringRegistry();
        if(registry == NULL)
        {
            return 0;
        }

        ArrayResize(names, registry.GetRegisteredCount());
        ArrayResize(values, registry.GetRegisteredCount());

        int count = registry.GetAllKeys(names);
        registry.GetAllValues(values);

        return count;
    }

    bool HasRegisteredLong(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetLongRegistry().IsRegistered(name);
    }
    
    bool HasRegisteredDouble(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetDoubleRegistry().IsRegistered(name);
    }
    
    bool HasRegisteredString(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        return m_driver.GetStringRegistry().IsRegistered(name);
    }

    //+------------------------------------------------------------------+
    //| 公共值獲取方法                                                   |
    //| 提供類型安全的值檢索接口                                         |
    //+------------------------------------------------------------------+

    // 獲取已註冊的 Long 值
    long GetRegisteredLong(string name)
    {
        if(m_driver == NULL)
        {
            return 0;
        }

        LongRegistry* registry = m_driver.GetLongRegistry();
        if(registry == NULL)
        {
            return 0;
        }

        return registry.GetRegisteredValue(name, 0);
    }

    // 獲取已註冊的 Double 值
    double GetRegisteredDouble(string name)
    {
        if(m_driver == NULL)
        {
            return 0.0;
        }

        DoubleRegistry* registry = m_driver.GetDoubleRegistry();
        if(registry == NULL)
        {
            return 0.0;
        }

        return registry.GetRegisteredValue(name, 0.0);
    }

    // 獲取已註冊的 String 值
    string GetRegisteredString(string name)
    {
        if(m_driver == NULL)
        {
            return "";
        }

        StringRegistry* registry = m_driver.GetStringRegistry();
        if(registry == NULL)
        {
            return "";
        }

        return registry.GetRegisteredValue(name, "");
    }

    //+------------------------------------------------------------------+
    //| 詳細信息檢索方法                                                 |
    //| 返回包含元數據的 RegisteredDetail 對象                          |
    //+------------------------------------------------------------------+

    // 獲取 String 類型的詳細信息
    // 注意：返回的指針需要調用者負責釋放內存（使用 delete）
    RegisteredDetail<string>* GetStringDetail(string name)
    {
        if(m_driver == NULL)
        {
            return new RegisteredDetail<string>(); // 返回無效的詳細信息
        }

        StringRegistry* registry = m_driver.GetStringRegistry();
        if(registry == NULL)
        {
            return new RegisteredDetail<string>(); // 返回無效的詳細信息
        }

        return registry.GetRegisteredDetail(name);
    }

    // 獲取 Double 類型的詳細信息
    // 注意：返回的指針需要調用者負責釋放內存（使用 delete）
    RegisteredDetail<double>* GetDoubleDetail(string name)
    {
        if(m_driver == NULL)
        {
            return new RegisteredDetail<double>(); // 返回無效的詳細信息
        }

        DoubleRegistry* registry = m_driver.GetDoubleRegistry();
        if(registry == NULL)
        {
            return new RegisteredDetail<double>(); // 返回無效的詳細信息
        }

        return registry.GetRegisteredDetail(name);
    }

    // 獲取 Long 類型的詳細信息
    // 注意：返回的指針需要調用者負責釋放內存（使用 delete）
    RegisteredDetail<long>* GetLongDetail(string name)
    {
        if(m_driver == NULL)
        {
            return new RegisteredDetail<long>(); // 返回無效的詳細信息
        }

        LongRegistry* registry = m_driver.GetLongRegistry();
        if(registry == NULL)
        {
            return new RegisteredDetail<long>(); // 返回無效的詳細信息
        }

        return registry.GetRegisteredDetail(name);
    }

};

//+------------------------------------------------------------------+
//| 便利宏定義                                                       |
//| 提供簡化的管道數據訪問接口（使用公共 API）                       |
//+------------------------------------------------------------------+

// 檢查指定類型的鍵是否已註冊
// 用法: PipelineDataIsRegistered(string, "myKey")
//       PipelineDataIsRegistered(double, "myValue")
//       PipelineDataIsRegistered(long, "myNumber")

// 為 string 類型特化
#define PipelineDataIsRegistered_string(key) HasRegisteredString(key)
// 為 double 類型特化
#define PipelineDataIsRegistered_double(key) HasRegisteredDouble(key)
// 為 long 類型特化
#define PipelineDataIsRegistered_long(key) HasRegisteredLong(key)

// 主宏定義 - 通過類型名稱分發到對應的特化宏
#define PipelineDataIsRegistered(type, key) PipelineDataIsRegistered_##type(key)

// 從指定類型的註冊器獲取值，如果不存在則返回適當的空值
// 用法: GetPipelineData(string, "myKey")
//       GetPipelineData(double, "myValue")
//       GetPipelineData(long, "myNumber")

// 為 string 類型特化 - 返回空字符串如果不存在
#define GetPipelineData_string(key) GetRegisteredString(key)
// 為 double 類型特化 - 返回 0.0 如果不存在
#define GetPipelineData_double(key) GetRegisteredDouble(key)
// 為 long 類型特化 - 返回 0 如果不存在
#define GetPipelineData_long(key) GetRegisteredLong(key)

// 主宏定義 - 通過類型名稱分發到對應的特化宏
#define GetPipelineData(type, key) GetPipelineData_##type(key)

#define PipelineObjectIsRegistered(ObjectType, name) dynamic_cast<ObjectType>(GetObjectDetail(name).GetObject()) == true
#define GetPipelineObject(ObjectType, name) dynamic_cast<ObjectType>(GetObjectDetail(name).GetObject())
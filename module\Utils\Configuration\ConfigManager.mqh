//+------------------------------------------------------------------+
//|                                              ConfigManager.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef CONFIG_MANAGER_MQH
#define CONFIG_MANAGER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Configuration Value Type Enumeration                            |
//+------------------------------------------------------------------+
enum ENUM_CONFIG_TYPE
{
    CONFIG_TYPE_INT = 0,        // Integer value
    CONFIG_TYPE_DOUBLE = 1,     // Double value
    CONFIG_TYPE_STRING = 2,     // String value
    CONFIG_TYPE_BOOL = 3        // Boolean value
};

//+------------------------------------------------------------------+
//| Configuration Entry Structure                                    |
//+------------------------------------------------------------------+
struct ConfigEntry
{
    string                key;              // Configuration key
    ENUM_CONFIG_TYPE      type;             // Value type
    string                stringValue;      // String representation
    int                   intValue;         // Integer value
    double                doubleValue;      // Double value
    bool                  boolValue;        // Boolean value
    string                description;      // Description
    bool                  isDefault;        // Is default value
    datetime              lastModified;     // Last modification time
};

//+------------------------------------------------------------------+
//| ConfigManager Class                                              |
//| Implementation of configuration management system               |
//+------------------------------------------------------------------+
class ConfigManager : public BaseComponent
{
private:
    ConfigEntry           m_configs[];          // Configuration entries
    int                   m_configCount;        // Number of configurations
    string                m_configFile;         // Configuration file path
    bool                  m_autoSave;           // Auto save changes
    bool                  m_autoLoad;           // Auto load on init
    string                m_sectionPrefix;      // Section prefix for grouping

public:
    //--- Constructor and Destructor
                          ConfigManager(string configFile = "config.ini", bool autoSave = true);
    virtual              ~ConfigManager();
    
    //--- Configuration methods
    void                  SetConfigFile(string fileName) { m_configFile = fileName; }
    void                  SetAutoSave(bool autoSave) { m_autoSave = autoSave; }
    void                  SetAutoLoad(bool autoLoad) { m_autoLoad = autoLoad; }
    void                  SetSectionPrefix(string prefix) { m_sectionPrefix = prefix; }
    
    //--- Information methods
    string                GetConfigFile() const { return m_configFile; }
    bool                  IsAutoSaveEnabled() const { return m_autoSave; }
    int                   GetConfigCount() const { return m_configCount; }
    
    //--- Value setting methods
    bool                  SetInt(string key, int value, string description = "");
    bool                  SetDouble(string key, double value, string description = "");
    bool                  SetString(string key, string value, string description = "");
    bool                  SetBool(string key, bool value, string description = "");
    
    //--- Value getting methods
    int                   GetInt(string key, int defaultValue = 0);
    double                GetDouble(string key, double defaultValue = 0.0);
    string                GetString(string key, string defaultValue = "");
    bool                  GetBool(string key, bool defaultValue = false);
    
    //--- Configuration management methods
    bool                  HasKey(string key);
    bool                  RemoveKey(string key);
    void                  ClearAll();
    bool                  LoadFromFile();
    bool                  SaveToFile();
    
    //--- Default value methods
    bool                  SetDefaultInt(string key, int value, string description = "");
    bool                  SetDefaultDouble(string key, double value, string description = "");
    bool                  SetDefaultString(string key, string value, string description = "");
    bool                  SetDefaultBool(string key, bool value, string description = "");
    void                  ResetToDefaults();
    
    //--- Validation methods
    bool                  ValidateInt(string key, int minValue, int maxValue);
    bool                  ValidateDouble(string key, double minValue, double maxValue);
    bool                  ValidateString(string key, int minLength, int maxLength);
    
    //--- Override base class methods
    virtual bool          OnInitialize() override;
    virtual bool          OnValidate() override;
    virtual void          OnCleanup() override;
    
    //--- Utility methods
    void                  PrintAllConfigs();
    void                  PrintConfig(string key);
    string                TypeToString(ENUM_CONFIG_TYPE type);
    ConfigEntry           GetConfigEntry(string key);
    bool                  ExportToFile(string fileName);
    bool                  ImportFromFile(string fileName);
    
private:
    //--- Internal methods
    int                   FindConfigIndex(string key);
    bool                  AddOrUpdateConfig(string key, ENUM_CONFIG_TYPE type, string stringValue,
                                           int intValue, double doubleValue, bool boolValue,
                                           string description, bool isDefault);
    string                FormatConfigLine(const ConfigEntry& entry);
    bool                  ParseConfigLine(string line, ConfigEntry& entry);
    string                BoolToString(bool value);
    bool                  StringToBool(string value);
    void                  ResizeConfigArray(int newSize);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
ConfigManager::ConfigManager(string configFile = "config.ini", bool autoSave = true) : BaseComponent("ConfigManager")
{
    m_configFile = configFile;
    m_autoSave = autoSave;
    m_autoLoad = true;
    m_sectionPrefix = "";
    m_configCount = 0;
    
    ArrayResize(m_configs, 50); // Initial size
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
ConfigManager::~ConfigManager()
{
    if (m_autoSave)
    {
        SaveToFile();
    }
    
    ArrayFree(m_configs);
}

//+------------------------------------------------------------------+
//| Initialize config manager                                        |
//+------------------------------------------------------------------+
bool ConfigManager::OnInitialize()
{
    if (m_autoLoad)
    {
        if (!LoadFromFile())
        {
            Print("CONFIG_MANAGER: Warning - Could not load config file: ", m_configFile);
        }
    }
    
    Print("CONFIG_MANAGER: Initialized with ", m_configCount, " configurations");
    return true;
}

//+------------------------------------------------------------------+
//| Validate config manager                                          |
//+------------------------------------------------------------------+
bool ConfigManager::OnValidate()
{
    if (m_configFile == "")
    {
        SetError(1601, "Invalid config file name");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Cleanup config manager                                           |
//+------------------------------------------------------------------+
void ConfigManager::OnCleanup()
{
    if (m_autoSave)
    {
        SaveToFile();
    }
}

//+------------------------------------------------------------------+
//| Set integer value                                                |
//+------------------------------------------------------------------+
bool ConfigManager::SetInt(string key, int value, string description = "")
{
    bool result = AddOrUpdateConfig(key, CONFIG_TYPE_INT, IntegerToString(value), 
                                   value, 0.0, false, description, false);
    
    if (result && m_autoSave)
    {
        SaveToFile();
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Set double value                                                 |
//+------------------------------------------------------------------+
bool ConfigManager::SetDouble(string key, double value, string description = "")
{
    bool result = AddOrUpdateConfig(key, CONFIG_TYPE_DOUBLE, DoubleToString(value, 8), 
                                   0, value, false, description, false);
    
    if (result && m_autoSave)
    {
        SaveToFile();
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Set string value                                                 |
//+------------------------------------------------------------------+
bool ConfigManager::SetString(string key, string value, string description = "")
{
    bool result = AddOrUpdateConfig(key, CONFIG_TYPE_STRING, value, 
                                   0, 0.0, false, description, false);
    
    if (result && m_autoSave)
    {
        SaveToFile();
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Set boolean value                                                |
//+------------------------------------------------------------------+
bool ConfigManager::SetBool(string key, bool value, string description = "")
{
    bool result = AddOrUpdateConfig(key, CONFIG_TYPE_BOOL, BoolToString(value), 
                                   0, 0.0, value, description, false);
    
    if (result && m_autoSave)
    {
        SaveToFile();
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Get integer value                                                |
//+------------------------------------------------------------------+
int ConfigManager::GetInt(string key, int defaultValue = 0)
{
    int index = FindConfigIndex(key);
    
    if (index >= 0 && m_configs[index].type == CONFIG_TYPE_INT)
    {
        return m_configs[index].intValue;
    }
    
    return defaultValue;
}

//+------------------------------------------------------------------+
//| Get double value                                                 |
//+------------------------------------------------------------------+
double ConfigManager::GetDouble(string key, double defaultValue = 0.0)
{
    int index = FindConfigIndex(key);
    
    if (index >= 0 && m_configs[index].type == CONFIG_TYPE_DOUBLE)
    {
        return m_configs[index].doubleValue;
    }
    
    return defaultValue;
}

//+------------------------------------------------------------------+
//| Get string value                                                 |
//+------------------------------------------------------------------+
string ConfigManager::GetString(string key, string defaultValue = "")
{
    int index = FindConfigIndex(key);
    
    if (index >= 0 && m_configs[index].type == CONFIG_TYPE_STRING)
    {
        return m_configs[index].stringValue;
    }
    
    return defaultValue;
}

//+------------------------------------------------------------------+
//| Get boolean value                                                |
//+------------------------------------------------------------------+
bool ConfigManager::GetBool(string key, bool defaultValue = false)
{
    int index = FindConfigIndex(key);
    
    if (index >= 0 && m_configs[index].type == CONFIG_TYPE_BOOL)
    {
        return m_configs[index].boolValue;
    }
    
    return defaultValue;
}

//+------------------------------------------------------------------+
//| Check if key exists                                              |
//+------------------------------------------------------------------+
bool ConfigManager::HasKey(string key)
{
    return (FindConfigIndex(key) >= 0);
}

//+------------------------------------------------------------------+
//| Remove configuration key                                         |
//+------------------------------------------------------------------+
bool ConfigManager::RemoveKey(string key)
{
    int index = FindConfigIndex(key);
    
    if (index >= 0)
    {
        // Shift remaining elements
        for (int i = index; i < m_configCount - 1; i++)
        {
            m_configs[i] = m_configs[i + 1];
        }
        
        m_configCount--;
        
        if (m_autoSave)
        {
            SaveToFile();
        }
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Clear all configurations                                         |
//+------------------------------------------------------------------+
void ConfigManager::ClearAll()
{
    m_configCount = 0;
    
    if (m_autoSave)
    {
        SaveToFile();
    }
}

//+------------------------------------------------------------------+
//| Load configurations from file                                    |
//+------------------------------------------------------------------+
bool ConfigManager::LoadFromFile()
{
    int handle = FileOpen(m_configFile, FILE_READ | FILE_TXT);
    
    if (handle == INVALID_HANDLE)
    {
        return false;
    }
    
    m_configCount = 0;
    
    while (!FileIsEnding(handle))
    {
        string line = FileReadString(handle);
        
        if (StringLen(line) > 0 && StringGetCharacter(line, 0) != '#') // Skip comments
        {
            ConfigEntry entry;
            if (ParseConfigLine(line, entry))
            {
                if (m_configCount >= ArraySize(m_configs))
                {
                    ResizeConfigArray(ArraySize(m_configs) + 50);
                }
                
                m_configs[m_configCount] = entry;
                m_configCount++;
            }
        }
    }
    
    FileClose(handle);
    return true;
}

//+------------------------------------------------------------------+
//| Save configurations to file                                      |
//+------------------------------------------------------------------+
bool ConfigManager::SaveToFile()
{
    int handle = FileOpen(m_configFile, FILE_WRITE | FILE_TXT);
    
    if (handle == INVALID_HANDLE)
    {
        return false;
    }
    
    // Write header
    FileWrite(handle, "# EA_Wizard Configuration File");
    FileWrite(handle, "# Generated: " + TimeToString(TimeCurrent()));
    FileWrite(handle, "");
    
    // Write configurations
    for (int i = 0; i < m_configCount; i++)
    {
        if (m_configs[i].description != "")
        {
            FileWrite(handle, "# " + m_configs[i].description);
        }
        
        FileWrite(handle, FormatConfigLine(m_configs[i]));
        FileWrite(handle, "");
    }
    
    FileClose(handle);
    return true;
}

//+------------------------------------------------------------------+
//| Set default integer value                                        |
//+------------------------------------------------------------------+
bool ConfigManager::SetDefaultInt(string key, int value, string description = "")
{
    if (!HasKey(key))
    {
        return AddOrUpdateConfig(key, CONFIG_TYPE_INT, IntegerToString(value), 
                                value, 0.0, false, description, true);
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Print all configurations                                         |
//+------------------------------------------------------------------+
void ConfigManager::PrintAllConfigs()
{
    Print("=== CONFIGURATION MANAGER ===");
    Print("Config File: ", m_configFile);
    Print("Total Configs: ", m_configCount);
    Print("");
    
    for (int i = 0; i < m_configCount; i++)
    {
        ConfigEntry config = m_configs[i];
        Print(config.key, " = ", config.stringValue, " (", TypeToString(config.type), ")");
        
        if (config.description != "")
        {
            Print("  Description: ", config.description);
        }
    }
}

//+------------------------------------------------------------------+
//| Convert type to string                                           |
//+------------------------------------------------------------------+
string ConfigManager::TypeToString(ENUM_CONFIG_TYPE type)
{
    switch(type)
    {
        case CONFIG_TYPE_INT:    return "INT";
        case CONFIG_TYPE_DOUBLE: return "DOUBLE";
        case CONFIG_TYPE_STRING: return "STRING";
        case CONFIG_TYPE_BOOL:   return "BOOL";
        default:                 return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Find configuration index                                         |
//+------------------------------------------------------------------+
int ConfigManager::FindConfigIndex(string key)
{
    for (int i = 0; i < m_configCount; i++)
    {
        if (m_configs[i].key == key)
        {
            return i;
        }
    }
    
    return -1;
}

//+------------------------------------------------------------------+
//| Add or update configuration                                      |
//+------------------------------------------------------------------+
bool ConfigManager::AddOrUpdateConfig(string key, ENUM_CONFIG_TYPE type, string stringValue,
                                      int intValue, double doubleValue, bool boolValue,
                                      string description, bool isDefault)
{
    int index = FindConfigIndex(key);
    
    if (index >= 0)
    {
        // Update existing
        m_configs[index].type = type;
        m_configs[index].stringValue = stringValue;
        m_configs[index].intValue = intValue;
        m_configs[index].doubleValue = doubleValue;
        m_configs[index].boolValue = boolValue;
        m_configs[index].description = description;
        m_configs[index].isDefault = isDefault;
        m_configs[index].lastModified = TimeCurrent();
    }
    else
    {
        // Add new
        if (m_configCount >= ArraySize(m_configs))
        {
            ResizeConfigArray(ArraySize(m_configs) + 50);
        }
        
        ConfigEntry newEntry;
        newEntry.key = key;
        newEntry.type = type;
        newEntry.stringValue = stringValue;
        newEntry.intValue = intValue;
        newEntry.doubleValue = doubleValue;
        newEntry.boolValue = boolValue;
        newEntry.description = description;
        newEntry.isDefault = isDefault;
        newEntry.lastModified = TimeCurrent();
        
        m_configs[m_configCount] = newEntry;
        m_configCount++;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Format configuration line                                        |
//+------------------------------------------------------------------+
string ConfigManager::FormatConfigLine(const ConfigEntry& entry)
{
    return entry.key + "=" + entry.stringValue;
}

//+------------------------------------------------------------------+
//| Parse configuration line                                         |
//+------------------------------------------------------------------+
bool ConfigManager::ParseConfigLine(string line, ConfigEntry& entry)
{
    int equalPos = StringFind(line, "=");
    
    if (equalPos > 0)
    {
        entry.key = StringSubstr(line, 0, equalPos);
        entry.stringValue = StringSubstr(line, equalPos + 1);
        
        // Determine type based on value
        if (entry.stringValue == "true" || entry.stringValue == "false")
        {
            entry.type = CONFIG_TYPE_BOOL;
            entry.boolValue = StringToBool(entry.stringValue);
        }
        else if (StringFind(entry.stringValue, ".") >= 0)
        {
            entry.type = CONFIG_TYPE_DOUBLE;
            entry.doubleValue = StringToDouble(entry.stringValue);
        }
        else if (StringIsDigit(StringGetCharacter(entry.stringValue, 0)) || 
                 StringGetCharacter(entry.stringValue, 0) == '-')
        {
            entry.type = CONFIG_TYPE_INT;
            entry.intValue = StringToInteger(entry.stringValue);
        }
        else
        {
            entry.type = CONFIG_TYPE_STRING;
        }
        
        entry.description = "";
        entry.isDefault = false;
        entry.lastModified = TimeCurrent();
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Convert boolean to string                                        |
//+------------------------------------------------------------------+
string ConfigManager::BoolToString(bool value)
{
    return value ? "true" : "false";
}

//+------------------------------------------------------------------+
//| Convert string to boolean                                        |
//+------------------------------------------------------------------+
bool ConfigManager::StringToBool(string value)
{
    return (value == "true" || value == "1" || value == "yes");
}

//+------------------------------------------------------------------+
//| Resize configuration array                                       |
//+------------------------------------------------------------------+
void ConfigManager::ResizeConfigArray(int newSize)
{
    ArrayResize(m_configs, newSize);
}

#endif // CONFIG_MANAGER_MQH

//+------------------------------------------------------------------+
//|                                            TestPipelineResult.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| PipelineResult 單元測試類                                        |
//+------------------------------------------------------------------+
class TestPipelineResult : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestPipelineResult(TestRunner* runner = NULL)
        : TestCase("TestPipelineResult"), m_runner(runner) {}

    // 析構函數
    virtual ~TestPipelineResult() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 PipelineResult 單元測試 ===");

        TestConstructor();
        TestGetters();
        TestToString();
        TestErrorLevels();
        TestTimestamp();

        Print("=== PipelineResult 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 PipelineResult 構造函數 ---");

        // 測試基本構造函數
        PipelineResult* result1 = new PipelineResult(true, "測試消息", "TestSource");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestConstructor - 基本構造函數",
                result1 != NULL,
                result1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶錯誤級別的構造函數
        PipelineResult* result2 = new PipelineResult(false, "錯誤消息", "ErrorSource", ERROR_LEVEL_ERROR);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestConstructor - 帶錯誤級別構造函數",
                result2 != NULL,
                result2 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        delete result1;
        delete result2;
    }

    // 測試 Getter 方法
    void TestGetters()
    {
        Print("--- 測試 PipelineResult Getter 方法 ---");

        PipelineResult* result = new PipelineResult(true, "成功消息", "TestPipeline", ERROR_LEVEL_INFO);

        // 測試 IsSuccess
        bool success = result.IsSuccess();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestGetters - IsSuccess",
                success == true,
                success ? "IsSuccess 返回正確" : "IsSuccess 返回錯誤"
            ));
        }

        // 測試 GetMessage
        string message = result.GetMessage();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestGetters - GetMessage",
                message == "成功消息",
                message == "成功消息" ? "GetMessage 返回正確" : "GetMessage 返回: " + message
            ));
        }

        // 測試 GetSource
        string source = result.GetSource();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestGetters - GetSource",
                source == "TestPipeline",
                source == "TestPipeline" ? "GetSource 返回正確" : "GetSource 返回: " + source
            ));
        }

        // 測試 GetErrorLevel
        ENUM_ERROR_LEVEL level = result.GetErrorLevel();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestGetters - GetErrorLevel",
                level == ERROR_LEVEL_INFO,
                level == ERROR_LEVEL_INFO ? "GetErrorLevel 返回正確" : "GetErrorLevel 返回錯誤級別"
            ));
        }

        delete result;
    }

    // 測試 ToString 方法
    void TestToString()
    {
        Print("--- 測試 PipelineResult ToString 方法 ---");

        PipelineResult* result = new PipelineResult(true, "測試消息", "TestSource", ERROR_LEVEL_WARNING);

        string str = result.ToString();
        bool containsSource = StringFind(str, "TestSource") >= 0;
        bool containsMessage = StringFind(str, "測試消息") >= 0;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestToString - 格式化字符串",
                containsSource && containsMessage,
                (containsSource && containsMessage) ? "ToString 格式正確" : "ToString 格式錯誤: " + str
            ));
        }

        delete result;
    }

    // 測試不同錯誤級別
    void TestErrorLevels()
    {
        Print("--- 測試 PipelineResult 錯誤級別 ---");

        // 測試 INFO 級別
        PipelineResult* infoResult = new PipelineResult(true, "信息", "Source", ERROR_LEVEL_INFO);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestErrorLevels - INFO 級別",
                infoResult.GetErrorLevel() == ERROR_LEVEL_INFO,
                "INFO 級別設置正確"
            ));
        }

        // 測試 WARNING 級別
        PipelineResult* warningResult = new PipelineResult(false, "警告", "Source", ERROR_LEVEL_WARNING);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestErrorLevels - WARNING 級別",
                warningResult.GetErrorLevel() == ERROR_LEVEL_WARNING,
                "WARNING 級別設置正確"
            ));
        }

        // 測試 ERROR 級別
        PipelineResult* errorResult = new PipelineResult(false, "錯誤", "Source", ERROR_LEVEL_ERROR);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestErrorLevels - ERROR 級別",
                errorResult.GetErrorLevel() == ERROR_LEVEL_ERROR,
                "ERROR 級別設置正確"
            ));
        }

        delete infoResult;
        delete warningResult;
        delete errorResult;
    }

    // 測試時間戳
    void TestTimestamp()
    {
        Print("--- 測試 PipelineResult 時間戳 ---");

        datetime beforeCreate = TimeCurrent();
        PipelineResult* result = new PipelineResult(true, "時間測試", "TimeSource");
        datetime afterCreate = TimeCurrent();

        datetime timestamp = result.GetTimestamp();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestPipelineResult::TestTimestamp - 時間戳範圍",
                timestamp >= beforeCreate && timestamp <= afterCreate,
                "時間戳在合理範圍內"
            ));
        }

        delete result;
    }
};

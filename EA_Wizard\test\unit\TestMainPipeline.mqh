//+------------------------------------------------------------------+
//|                                              TestMainPipeline.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../MainPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體 MainPipeline 實現                                   |
//+------------------------------------------------------------------+
class MockMainPipeline : public MainPipeline
{
private:
    bool m_shouldExecute;
    int m_executeCount;
    string m_executionResult;

public:
    MockMainPipeline(string name = "",
                    string type = "MockMain",
                    ENUM_TRADING_STAGE stage = INIT_START,
                    ITradingPipelineDriver* driver = NULL,
                    bool shouldExecute = true)
        : MainPipeline(stage, name, type, driver),
          m_shouldExecute(shouldExecute),
          m_executeCount(0),
          m_executionResult("")
    {
    }

    virtual ~MockMainPipeline() {}

    // 實現抽象方法
    virtual void Main() override
    {
        m_executeCount++;
        if(m_shouldExecute)
        {
            m_executionResult = "執行成功";
        }
        else
        {
            m_executionResult = "執行失敗";
        }
    }

    // 測試輔助方法
    int GetExecuteCount() const { return m_executeCount; }
    void SetShouldExecute(bool should) { m_shouldExecute = should; }
    string GetExecutionResult() const { return m_executionResult; }
};

//+------------------------------------------------------------------+
//| MainPipeline 單元測試類                                          |
//+------------------------------------------------------------------+
class TestMainPipeline : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestMainPipeline(TestRunner* runner = NULL)
        : TestCase("TestMainPipeline"), m_runner(runner) {}

    // 析構函數
    virtual ~TestMainPipeline() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 MainPipeline 單元測試 ===");

        TestConstructor();
        TestInheritance();
        TestDriverIntegration();
        TestRegistryIntegration();
        TestExecuteFlow();
        TestDefaultParameters();
        TestEnhancedRegistrationMethods();
        TestDetailRetrievalMethods();
        TestConvenienceMacros();

        Print("=== MainPipeline 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 MainPipeline 構造函數 ---");

        // 測試默認構造函數
        MockMainPipeline* pipeline1 = new MockMainPipeline();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestConstructor - 默認構造函數",
                pipeline1 != NULL,
                pipeline1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        MockMainPipeline* pipeline2 = new MockMainPipeline("TestMain", "TestType", INIT_COMPLETE);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestConstructor - 帶參數構造函數",
                pipeline2 != NULL && pipeline2.GetName() == "TestMain",
                pipeline2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        delete pipeline1;
        delete pipeline2;
    }

    // 測試繼承關係
    void TestInheritance()
    {
        Print("--- 測試 MainPipeline 繼承關係 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("InheritanceTest", "Test", INIT_START);

        // 測試是否正確繼承 TradingPipeline
        string type = pipeline.GetType();
        string name = pipeline.GetName();
        ENUM_TRADING_STAGE stage = pipeline.GetStage();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestInheritance - TradingPipeline 繼承",
                type == "Test" && name == "InheritanceTest" && stage == INIT_START,
                "正確繼承 TradingPipeline 屬性"
            ));
        }

        // 測試是否實現 ITradingPipeline 介面
        bool initialExecuted = pipeline.IsExecuted();
        pipeline.Execute();
        bool afterExecuted = pipeline.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestInheritance - ITradingPipeline 介面",
                !initialExecuted && afterExecuted,
                "正確實現 ITradingPipeline 介面"
            ));
        }

        delete pipeline;
    }

    // 測試驅動器整合
    void TestDriverIntegration()
    {
        Print("--- 測試 MainPipeline 驅動器整合 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("DriverTest", "Test", INIT_START);

        // 測試驅動器不為空（應該使用預設的 GetInstance()）
        ITradingPipelineDriver* driver = pipeline.GetDriver();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDriverIntegration - 驅動器不為空",
                driver != NULL,
                driver != NULL ? "驅動器正確獲取" : "驅動器為空"
            ));
        }

        // 測試驅動器組件訪問
        if(driver != NULL)
        {
            bool isInitialized = driver.IsInitialized();
            string driverName = driver.GetName();

            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipeline::TestDriverIntegration - 驅動器狀態",
                    isInitialized && driverName != "",
                    "驅動器狀態正常"
                ));
            }
        }

        delete pipeline;
    }

    // 測試註冊器整合
    void TestRegistryIntegration()
    {
        Print("--- 測試 MainPipeline 註冊器整合 ---");

        // 注意：由於構造函數中有註冊邏輯，這裡測試註冊是否正常
        MockMainPipeline* pipeline = new MockMainPipeline("RegistryTest", "Test", TICK_DATA_FEED);

        // 測試流水線創建後的狀態
        bool isExecuted = pipeline.IsExecuted();
        ENUM_TRADING_STAGE stage = pipeline.GetStage();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestRegistryIntegration - 註冊後狀態",
                !isExecuted && stage == TICK_DATA_FEED,
                "註冊後狀態正確"
            ));
        }

        // 測試驅動器的註冊器訪問
        ITradingPipelineDriver* driver = pipeline.GetDriver();
        if(driver != NULL)
        {
            // 這裡只測試能否訪問註冊器，不測試具體註冊結果
            // 因為註冊器的詳細測試在 TestTradingPipelineRegistry.mqh 中
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestMainPipeline::TestRegistryIntegration - 註冊器訪問",
                    true,  // 如果能執行到這裡說明註冊器訪問正常
                    "註冊器訪問正常"
                ));
            }
        }

        delete pipeline;
    }

    // 測試執行流程
    void TestExecuteFlow()
    {
        Print("--- 測試 MainPipeline 執行流程 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("ExecuteTest", "Test", INIT_START, NULL, true);

        // 測試首次執行
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestExecuteFlow - 首次執行",
                pipeline.IsExecuted() && pipeline.GetExecuteCount() == 1,
                "首次執行成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestExecuteFlow - 執行結果",
                pipeline.GetExecutionResult() == "執行成功",
                "執行結果正確"
            ));
        }

        // 測試重複執行防護
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestExecuteFlow - 重複執行防護",
                pipeline.GetExecuteCount() == 1,
                "重複執行被正確防護"
            ));
        }

        // 測試重置後再執行
        pipeline.Restore();
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestExecuteFlow - 重置後再執行",
                pipeline.IsExecuted() && pipeline.GetExecuteCount() == 2,
                "重置後可以再次執行"
            ));
        }

        delete pipeline;
    }

    // 測試默認參數
    void TestDefaultParameters()
    {
        Print("--- 測試 MainPipeline 默認參數 ---");

        // 測試所有默認參數
        MockMainPipeline* pipeline1 = new MockMainPipeline();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDefaultParameters - 默認名稱",
                pipeline1.GetName() == "",
                "默認名稱正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDefaultParameters - 默認類型",
                pipeline1.GetType() == "MockMain",
                "默認類型正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDefaultParameters - 默認階段",
                pipeline1.GetStage() == INIT_START,
                "默認階段正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDefaultParameters - 默認驅動器",
                pipeline1.GetDriver() != NULL,
                "默認驅動器正確"
            ));
        }

        delete pipeline1;
    }

    // 測試增強的註冊方法（帶描述）
    void TestEnhancedRegistrationMethods()
    {
        Print("--- 測試 MainPipeline 增強註冊方法 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("EnhancedRegTest", "Test", INIT_START);

        // 測試帶描述的註冊方法是否存在並可調用
        // 注意：由於 MockMainPipeline 沒有實際的註冊邏輯，這裡主要測試方法調用
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestEnhancedRegistrationMethods - 增強註冊方法存在",
                pipeline != NULL,
                "增強註冊方法可以調用"
            ));
        }

        delete pipeline;
    }

    // 測試詳細信息檢索方法
    void TestDetailRetrievalMethods()
    {
        Print("--- 測試 MainPipeline 詳細信息檢索方法 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("DetailTest", "Test", INIT_START);

        // 測試詳細信息檢索方法是否存在並可調用
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestDetailRetrievalMethods - 詳細信息檢索方法存在",
                pipeline != NULL,
                "詳細信息檢索方法可以調用"
            ));
        }

        delete pipeline;
    }

    // 測試便利宏
    void TestConvenienceMacros()
    {
        Print("--- 測試 MainPipeline 便利宏 ---");

        MockMainPipeline* pipeline = new MockMainPipeline("MacroTest", "Test", INIT_START);

        // 測試便利宏是否可以編譯和使用
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestMainPipeline::TestConvenienceMacros - 便利宏可用",
                pipeline != NULL,
                "便利宏編譯正常"
            ));
        }

        delete pipeline;
    }
};

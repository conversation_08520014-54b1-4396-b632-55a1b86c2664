//+------------------------------------------------------------------+
//|                                         ParameterValidator.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef PARAMETER_VALIDATOR_MQH
#define PARAMETER_VALIDATOR_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Validation Result Structure                                      |
//+------------------------------------------------------------------+
struct ValidationResult
{
    bool              isValid;          // Validation result
    string            errorMessage;     // Error message if invalid
    string            parameterName;    // Parameter name
    string            expectedRange;    // Expected range/format
};

//+------------------------------------------------------------------+
//| Parameter Type Enumeration                                       |
//+------------------------------------------------------------------+
enum ENUM_PARAM_TYPE
{
    PARAM_TYPE_INT = 0,         // Integer parameter
    PARAM_TYPE_DOUBLE = 1,      // Double parameter
    PARAM_TYPE_STRING = 2,      // String parameter
    PARAM_TYPE_BOOL = 3,        // Boolean parameter
    PARAM_TYPE_ENUM = 4,        // Enumeration parameter
    PARAM_TYPE_DATETIME = 5     // DateTime parameter
};

//+------------------------------------------------------------------+
//| ParameterValidator Class                                         |
//| Implementation of comprehensive parameter validation system     |
//+------------------------------------------------------------------+
class ParameterValidator : public BaseComponent
{
private:
    bool              m_strictMode;         // Strict validation mode
    bool              m_logValidation;      // Log validation results
    string            m_validationPrefix;   // Prefix for validation messages

public:
    //--- Constructor and Destructor
                      ParameterValidator(bool strictMode = true);
    virtual          ~ParameterValidator();
    
    //--- Configuration methods
    void              SetStrictMode(bool strict) { m_strictMode = strict; }
    void              SetLogValidation(bool log) { m_logValidation = log; }
    void              SetValidationPrefix(string prefix) { m_validationPrefix = prefix; }
    
    //--- Information methods
    bool              IsStrictMode() const { return m_strictMode; }
    bool              IsLogValidationEnabled() const { return m_logValidation; }
    
    //--- Integer validation methods
    ValidationResult  ValidateInt(string paramName, int value, int minValue = INT_MIN, int maxValue = INT_MAX);
    ValidationResult  ValidateIntRange(string paramName, int value, int minValue, int maxValue);
    ValidationResult  ValidateIntPositive(string paramName, int value);
    ValidationResult  ValidateIntNonNegative(string paramName, int value);
    
    //--- Double validation methods
    ValidationResult  ValidateDouble(string paramName, double value, double minValue = -DBL_MAX, double maxValue = DBL_MAX);
    ValidationResult  ValidateDoubleRange(string paramName, double value, double minValue, double maxValue);
    ValidationResult  ValidateDoublePositive(string paramName, double value);
    ValidationResult  ValidateDoubleNonNegative(string paramName, double value);
    ValidationResult  ValidatePercentage(string paramName, double value);
    ValidationResult  ValidateProbability(string paramName, double value);
    
    //--- String validation methods
    ValidationResult  ValidateString(string paramName, string value, int minLength = 0, int maxLength = INT_MAX);
    ValidationResult  ValidateStringNotEmpty(string paramName, string value);
    ValidationResult  ValidateStringLength(string paramName, string value, int minLength, int maxLength);
    ValidationResult  ValidateStringPattern(string paramName, string value, string pattern);
    ValidationResult  ValidateSymbol(string paramName, string symbol);
    
    //--- Boolean validation methods
    ValidationResult  ValidateBool(string paramName, bool value);
    
    //--- DateTime validation methods
    ValidationResult  ValidateDateTime(string paramName, datetime value);
    ValidationResult  ValidateDateTimeRange(string paramName, datetime value, datetime minTime, datetime maxTime);
    ValidationResult  ValidateDateTimeFuture(string paramName, datetime value);
    ValidationResult  ValidateDateTimePast(string paramName, datetime value);
    
    //--- Trading parameter validation methods
    ValidationResult  ValidateLotSize(string paramName, double lotSize, string symbol = "");
    ValidationResult  ValidatePrice(string paramName, double price);
    ValidationResult  ValidateStopLoss(string paramName, double stopLoss, double entryPrice, int orderType);
    ValidationResult  ValidateTakeProfit(string paramName, double takeProfit, double entryPrice, int orderType);
    ValidationResult  ValidateSpread(string paramName, double spread);
    ValidationResult  ValidateSlippage(string paramName, int slippage);
    ValidationResult  ValidateMagicNumber(string paramName, int magicNumber);
    
    //--- Indicator parameter validation methods
    ValidationResult  ValidateIndicatorPeriod(string paramName, int period, int minPeriod = 1, int maxPeriod = 1000);
    ValidationResult  ValidateMAMethod(string paramName, int method);
    ValidationResult  ValidateAppliedPrice(string paramName, int appliedPrice);
    ValidationResult  ValidateTimeframe(string paramName, ENUM_TIMEFRAMES timeframe);
    
    //--- Risk management validation methods
    ValidationResult  ValidateRiskPercent(string paramName, double riskPercent);
    ValidationResult  ValidateMaxLoss(string paramName, double maxLoss);
    ValidationResult  ValidateMaxOrders(string paramName, int maxOrders);
    ValidationResult  ValidateDrawdownLimit(string paramName, double drawdownPercent);
    
    //--- Batch validation methods
    bool              ValidateAllParameters();
    ValidationResult  ValidateParameterSet(string setName);
    void              AddValidationRule(string paramName, ENUM_PARAM_TYPE type, double minValue, double maxValue);
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Utility methods
    string            FormatValidationMessage(const ValidationResult& result);
    void              LogValidationResult(const ValidationResult& result);
    bool              IsValidSymbol(string symbol);
    bool              IsValidTimeframe(ENUM_TIMEFRAMES timeframe);
    ValidationResult  CreateResult(bool isValid, string paramName, string errorMessage = "", string expectedRange = "");
    
private:
    //--- Internal validation helpers
    bool              IsInRange(double value, double minValue, double maxValue);
    bool              IsInRange(int value, int minValue, int maxValue);
    bool              IsInRange(datetime value, datetime minValue, datetime maxValue);
    string            FormatRange(double minValue, double maxValue);
    string            FormatRange(int minValue, int maxValue);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
ParameterValidator::ParameterValidator(bool strictMode = true) : BaseComponent("ParameterValidator")
{
    m_strictMode = strictMode;
    m_logValidation = true;
    m_validationPrefix = "VALIDATION";
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
ParameterValidator::~ParameterValidator()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize parameter validator                                   |
//+------------------------------------------------------------------+
bool ParameterValidator::OnInitialize()
{
    if (m_logValidation)
    {
        Print(m_validationPrefix, ": Parameter validator initialized (Strict mode: ", 
              (m_strictMode ? "ON" : "OFF"), ")");
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate validator parameters                                    |
//+------------------------------------------------------------------+
bool ParameterValidator::OnValidate()
{
    // Validator doesn't need validation of its own parameters
    return true;
}

//+------------------------------------------------------------------+
//| Validate integer parameter                                       |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateInt(string paramName, int value, int minValue = INT_MIN, int maxValue = INT_MAX)
{
    bool isValid = IsInRange(value, minValue, maxValue);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Value " + IntegerToString(value) + " is outside valid range";
        expectedRange = FormatRange(minValue, maxValue);
    }
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate integer range                                           |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateIntRange(string paramName, int value, int minValue, int maxValue)
{
    return ValidateInt(paramName, value, minValue, maxValue);
}

//+------------------------------------------------------------------+
//| Validate positive integer                                        |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateIntPositive(string paramName, int value)
{
    return ValidateInt(paramName, value, 1, INT_MAX);
}

//+------------------------------------------------------------------+
//| Validate non-negative integer                                    |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateIntNonNegative(string paramName, int value)
{
    return ValidateInt(paramName, value, 0, INT_MAX);
}

//+------------------------------------------------------------------+
//| Validate double parameter                                        |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateDouble(string paramName, double value, double minValue = -DBL_MAX, double maxValue = DBL_MAX)
{
    bool isValid = IsInRange(value, minValue, maxValue) && value != EMPTY_VALUE;
    string errorMsg = "";
    string expectedRange = "";
    
    if (value == EMPTY_VALUE)
    {
        errorMsg = "Value is EMPTY_VALUE";
    }
    else if (!isValid)
    {
        errorMsg = "Value " + DoubleToString(value, 8) + " is outside valid range";
        expectedRange = FormatRange(minValue, maxValue);
    }
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate double range                                            |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateDoubleRange(string paramName, double value, double minValue, double maxValue)
{
    return ValidateDouble(paramName, value, minValue, maxValue);
}

//+------------------------------------------------------------------+
//| Validate positive double                                         |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateDoublePositive(string paramName, double value)
{
    return ValidateDouble(paramName, value, 0.000001, DBL_MAX);
}

//+------------------------------------------------------------------+
//| Validate non-negative double                                     |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateDoubleNonNegative(string paramName, double value)
{
    return ValidateDouble(paramName, value, 0.0, DBL_MAX);
}

//+------------------------------------------------------------------+
//| Validate percentage (0-100)                                      |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidatePercentage(string paramName, double value)
{
    return ValidateDouble(paramName, value, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| Validate probability (0-1)                                       |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateProbability(string paramName, double value)
{
    return ValidateDouble(paramName, value, 0.0, 1.0);
}

//+------------------------------------------------------------------+
//| Validate string parameter                                        |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateString(string paramName, string value, int minLength = 0, int maxLength = INT_MAX)
{
    int length = StringLen(value);
    bool isValid = (length >= minLength && length <= maxLength);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "String length " + IntegerToString(length) + " is outside valid range";
        expectedRange = "Length: " + FormatRange(minLength, maxLength);
    }
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate non-empty string                                        |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateStringNotEmpty(string paramName, string value)
{
    return ValidateString(paramName, value, 1, INT_MAX);
}

//+------------------------------------------------------------------+
//| Validate string length                                           |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateStringLength(string paramName, string value, int minLength, int maxLength)
{
    return ValidateString(paramName, value, minLength, maxLength);
}

//+------------------------------------------------------------------+
//| Validate symbol                                                  |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateSymbol(string paramName, string symbol)
{
    bool isValid = IsValidSymbol(symbol);
    string errorMsg = isValid ? "" : "Invalid trading symbol";
    string expectedRange = "Valid trading symbol";
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate lot size                                                |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateLotSize(string paramName, double lotSize, string symbol = "")
{
    if (symbol == "") symbol = Symbol();
    
    double minLot = MarketInfo(symbol, MODE_MINLOT);
    double maxLot = MarketInfo(symbol, MODE_MAXLOT);
    double lotStep = MarketInfo(symbol, MODE_LOTSTEP);
    
    bool isValid = (lotSize >= minLot && lotSize <= maxLot);
    
    // Check lot step
    if (isValid && lotStep > 0.0)
    {
        double remainder = MathMod(lotSize, lotStep);
        isValid = (MathAbs(remainder) < 0.0001 || MathAbs(remainder - lotStep) < 0.0001);
    }
    
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Invalid lot size for symbol " + symbol;
        expectedRange = "Range: " + DoubleToString(minLot, 2) + " - " + DoubleToString(maxLot, 2) + 
                       ", Step: " + DoubleToString(lotStep, 2);
    }
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate price                                                   |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidatePrice(string paramName, double price)
{
    bool isValid = (price > 0.0 && price != EMPTY_VALUE);
    string errorMsg = isValid ? "" : "Invalid price value";
    string expectedRange = "Positive number";
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate indicator period                                        |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateIndicatorPeriod(string paramName, int period, int minPeriod = 1, int maxPeriod = 1000)
{
    return ValidateInt(paramName, period, minPeriod, maxPeriod);
}

//+------------------------------------------------------------------+
//| Validate MA method                                               |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateMAMethod(string paramName, int method)
{
    bool isValid = (method >= MODE_SMA && method <= MODE_LWMA);
    string errorMsg = isValid ? "" : "Invalid moving average method";
    string expectedRange = "0-3 (SMA, EMA, SMMA, LWMA)";
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate applied price                                           |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateAppliedPrice(string paramName, int appliedPrice)
{
    bool isValid = (appliedPrice >= PRICE_CLOSE && appliedPrice <= PRICE_WEIGHTED);
    string errorMsg = isValid ? "" : "Invalid applied price type";
    string expectedRange = "0-6 (Close, Open, High, Low, Median, Typical, Weighted)";
    
    ValidationResult result = CreateResult(isValid, paramName, errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate risk percentage                                         |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::ValidateRiskPercent(string paramName, double riskPercent)
{
    return ValidateDouble(paramName, riskPercent, 0.1, 50.0);
}

//+------------------------------------------------------------------+
//| Create validation result                                         |
//+------------------------------------------------------------------+
ValidationResult ParameterValidator::CreateResult(bool isValid, string paramName, string errorMessage = "", string expectedRange = "")
{
    ValidationResult result;
    result.isValid = isValid;
    result.parameterName = paramName;
    result.errorMessage = errorMessage;
    result.expectedRange = expectedRange;
    
    return result;
}

//+------------------------------------------------------------------+
//| Log validation result                                            |
//+------------------------------------------------------------------+
void ParameterValidator::LogValidationResult(const ValidationResult& result)
{
    if (!m_logValidation) return;
    
    if (result.isValid)
    {
        if (m_strictMode)
        {
            Print(m_validationPrefix, ": ", result.parameterName, " - VALID");
        }
    }
    else
    {
        Print(m_validationPrefix, ": ", result.parameterName, " - INVALID: ", result.errorMessage);
        if (result.expectedRange != "")
        {
            Print(m_validationPrefix, ": Expected: ", result.expectedRange);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if value is in range                                       |
//+------------------------------------------------------------------+
bool ParameterValidator::IsInRange(double value, double minValue, double maxValue)
{
    return (value >= minValue && value <= maxValue);
}

//+------------------------------------------------------------------+
//| Check if value is in range (integer)                            |
//+------------------------------------------------------------------+
bool ParameterValidator::IsInRange(int value, int minValue, int maxValue)
{
    return (value >= minValue && value <= maxValue);
}

//+------------------------------------------------------------------+
//| Format range string (double)                                     |
//+------------------------------------------------------------------+
string ParameterValidator::FormatRange(double minValue, double maxValue)
{
    return DoubleToString(minValue, 8) + " to " + DoubleToString(maxValue, 8);
}

//+------------------------------------------------------------------+
//| Format range string (integer)                                    |
//+------------------------------------------------------------------+
string ParameterValidator::FormatRange(int minValue, int maxValue)
{
    return IntegerToString(minValue) + " to " + IntegerToString(maxValue);
}

//+------------------------------------------------------------------+
//| Check if symbol is valid                                         |
//+------------------------------------------------------------------+
bool ParameterValidator::IsValidSymbol(string symbol)
{
    if (StringLen(symbol) == 0) return false;
    
    // Check if symbol exists in Market Watch
    double bid = MarketInfo(symbol, MODE_BID);
    double ask = MarketInfo(symbol, MODE_ASK);
    
    return (bid > 0.0 && ask > 0.0);
}

#endif // PARAMETER_VALIDATOR_MQH

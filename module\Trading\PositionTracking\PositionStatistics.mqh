//+------------------------------------------------------------------+
//|                                         PositionStatistics.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_STATISTICS_MQH
#define POSITION_STATISTICS_MQH

#include "../../Base/BaseComponent.mqh"
#include "PositionCollector.mqh"

//+------------------------------------------------------------------+
//| Position Statistics Structure                                    |
//+------------------------------------------------------------------+
struct PositionStats
{
    int               totalPositions;   // Total open positions
    int               buyPositions;     // Number of buy positions
    int               sellPositions;    // Number of sell positions
    double            totalLots;        // Total lot size
    double            buyLots;          // Buy positions lot size
    double            sellLots;         // Sell positions lot size
    double            totalProfit;      // Total profit/loss
    double            buyProfit;        // Buy positions profit
    double            sellProfit;       // Sell positions profit
    double            unrealizedPL;     // Unrealized P&L
    double            realizedPL;       // Realized P&L (from history)
};

//+------------------------------------------------------------------+
//| PositionStatistics Class                                         |
//| Responsible for calculating position statistics                  |
//+------------------------------------------------------------------+
class PositionStatistics : public BaseComponent
{
private:
    PositionStats     m_currentStats;       // Current position statistics
    PositionStats     m_dailyStats;         // Daily statistics
    datetime          m_dailyResetTime;     // Last daily reset time

    // Error codes specific to PositionStatistics
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionStatistics();
    virtual          ~PositionStatistics();

    //--- Statistics calculation methods
    void              UpdateStatistics(PositionInfo& positions[], int count);
    void              UpdateStatistics(PositionCollector* collector);
    PositionStats     GetCurrentStats() const { return m_currentStats; }
    PositionStats     GetDailyStats() const { return m_dailyStats; }

    //--- Analysis methods
    double            GetAverageOpenPrice(PositionInfo& positions[], int count, int orderType = -1);
    double            GetLargestPosition(PositionInfo& positions[], int count);
    double            GetSmallestPosition(PositionInfo& positions[], int count);
    datetime          GetOldestPositionTime(PositionInfo& positions[], int count);
    datetime          GetNewestPositionTime(PositionInfo& positions[], int count);
    double            GetPositionExposure() const;

    //--- Daily statistics management
    void              ResetDailyStats();
    bool              ShouldResetDaily();

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;

private:
    //--- Internal calculation methods
    void              AddPositionToStats(const PositionInfo& position, PositionStats& stats);
    void              ResetStats(PositionStats& stats);
};

// Error codes for PositionStatistics
const BaseErrorDescriptor PositionStatistics::CODE_ERRORS[] = {
    {1311, "Invalid position array for statistics calculation"},
    {1312, "Failed to calculate statistics"},
    {1313, "Invalid position collector reference"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionStatistics::PositionStatistics() : BaseComponent("PositionStatistics")
{
    ZeroMemory(m_currentStats);
    ZeroMemory(m_dailyStats);
    m_dailyResetTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionStatistics::~PositionStatistics()
{
    // No dynamic memory to clean up
}

//+------------------------------------------------------------------+
//| Initialize position statistics                                   |
//+------------------------------------------------------------------+
bool PositionStatistics::OnInitialize()
{
    ResetStats(m_currentStats);
    ResetStats(m_dailyStats);
    m_dailyResetTime = TimeCurrent();
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionStatistics::OnValidate()
{
    // No specific validation needed for statistics calculator
    return true;
}

//+------------------------------------------------------------------+
//| Update position statistics                                       |
//+------------------------------------------------------------------+
bool PositionStatistics::OnUpdate()
{
    if (ShouldResetDaily())
    {
        ResetDailyStats();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update statistics from position array                           |
//+------------------------------------------------------------------+
void PositionStatistics::UpdateStatistics(PositionInfo& positions[], int count)
{
    if (count < 0)
    {
        HandleError(1311, GetErrorDescription(1311));
        return;
    }

    // Reset current statistics
    ResetStats(m_currentStats);

    // Calculate statistics from positions
    for (int i = 0; i < count; i++)
    {
        AddPositionToStats(positions[i], m_currentStats);
    }
}

//+------------------------------------------------------------------+
//| Update statistics from position collector                       |
//+------------------------------------------------------------------+
void PositionStatistics::UpdateStatistics(PositionCollector* collector)
{
    if (collector == NULL)
    {
        HandleError(1313, GetErrorDescription(1313));
        return;
    }

    PositionInfo positions[];
    collector.GetAllPositions(positions);
    int count = collector.GetPositionCount();

    UpdateStatistics(positions, count);
}

//+------------------------------------------------------------------+
//| Get average open price                                           |
//+------------------------------------------------------------------+
double PositionStatistics::GetAverageOpenPrice(PositionInfo& positions[], int count, int orderType = -1)
{
    double totalValue = 0.0;
    double totalLots = 0.0;

    for (int i = 0; i < count; i++)
    {
        if (orderType == -1 || positions[i].type == orderType)
        {
            totalValue += positions[i].openPrice * positions[i].lotSize;
            totalLots += positions[i].lotSize;
        }
    }

    return (totalLots > 0.0) ? totalValue / totalLots : 0.0;
}

//+------------------------------------------------------------------+
//| Get largest position size                                        |
//+------------------------------------------------------------------+
double PositionStatistics::GetLargestPosition(PositionInfo& positions[], int count)
{
    double largest = 0.0;

    for (int i = 0; i < count; i++)
    {
        if (positions[i].lotSize > largest)
        {
            largest = positions[i].lotSize;
        }
    }

    return largest;
}

//+------------------------------------------------------------------+
//| Get smallest position size                                       |
//+------------------------------------------------------------------+
double PositionStatistics::GetSmallestPosition(PositionInfo& positions[], int count)
{
    if (count == 0) return 0.0;

    double smallest = positions[0].lotSize;

    for (int i = 1; i < count; i++)
    {
        if (positions[i].lotSize < smallest)
        {
            smallest = positions[i].lotSize;
        }
    }

    return smallest;
}

//+------------------------------------------------------------------+
//| Get oldest position time                                         |
//+------------------------------------------------------------------+
datetime PositionStatistics::GetOldestPositionTime(PositionInfo& positions[], int count)
{
    if (count == 0) return 0;

    datetime oldest = positions[0].openTime;

    for (int i = 1; i < count; i++)
    {
        if (positions[i].openTime < oldest)
        {
            oldest = positions[i].openTime;
        }
    }

    return oldest;
}

//+------------------------------------------------------------------+
//| Get newest position time                                         |
//+------------------------------------------------------------------+
datetime PositionStatistics::GetNewestPositionTime(PositionInfo& positions[], int count)
{
    if (count == 0) return 0;

    datetime newest = positions[0].openTime;

    for (int i = 1; i < count; i++)
    {
        if (positions[i].openTime > newest)
        {
            newest = positions[i].openTime;
        }
    }

    return newest;
}

//+------------------------------------------------------------------+
//| Get position exposure (net position)                            |
//+------------------------------------------------------------------+
double PositionStatistics::GetPositionExposure() const
{
    return m_currentStats.buyLots - m_currentStats.sellLots;
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                           |
//+------------------------------------------------------------------+
void PositionStatistics::ResetDailyStats()
{
    ResetStats(m_dailyStats);
    m_dailyResetTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Check if daily reset is needed                                  |
//+------------------------------------------------------------------+
bool PositionStatistics::ShouldResetDaily()
{
    datetime currentTime = TimeCurrent();
    datetime currentDay = currentTime - (currentTime % 86400); // Start of current day
    datetime resetDay = m_dailyResetTime - (m_dailyResetTime % 86400); // Start of reset day

    return (currentDay > resetDay);
}

//+------------------------------------------------------------------+
//| Add position to statistics                                       |
//+------------------------------------------------------------------+
void PositionStatistics::AddPositionToStats(const PositionInfo& position, PositionStats& stats)
{
    stats.totalPositions++;
    stats.totalLots += position.lotSize;
    stats.totalProfit += position.totalPL;

    if (position.type == OP_BUY)
    {
        stats.buyPositions++;
        stats.buyLots += position.lotSize;
        stats.buyProfit += position.totalPL;
    }
    else if (position.type == OP_SELL)
    {
        stats.sellPositions++;
        stats.sellLots += position.lotSize;
        stats.sellProfit += position.totalPL;
    }
}

//+------------------------------------------------------------------+
//| Reset statistics structure                                       |
//+------------------------------------------------------------------+
void PositionStatistics::ResetStats(PositionStats& stats)
{
    ZeroMemory(stats);
}

#endif // POSITION_STATISTICS_MQH

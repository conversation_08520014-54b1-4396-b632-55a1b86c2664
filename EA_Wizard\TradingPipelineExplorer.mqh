//+------------------------------------------------------------------+
//|                                        TradingPipelineExplorer.mqh |
//|                                            EAPipelineAdvance_v1     |
//|                                                                      |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineRegistry.mqh"
#include "TradingEvent.mqh"
#include "TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 交易流水線探索器類                                               |
//| 提供對 TradingPipelineRegistry 的查詢和探索功能                 |
//+------------------------------------------------------------------+
class TradingPipelineExplorer
{
private:
    TradingPipelineRegistry* m_registry;            // 註冊器指針
    string m_name;                                  // 探索器名稱
    string m_type;                                  // 探索器類型
    string m_description;                           // 探索器描述
    PipelineResult* m_last_result;                  // 執行結果

public:
    // 構造函數
    TradingPipelineExplorer(TradingPipelineRegistry* registry,
                           string name = "TradingPipelineExplorer",
                           string type = "TradingPipelineExplorer",
                           string description = "交易流水線探索器")
        : m_registry(registry),
          m_name(name),
          m_type(type),
          m_description(description),
          m_last_result(new PipelineResult(false, "探索器尚未執行查詢", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~TradingPipelineExplorer()
    {
        // 探索器不擁有管理器，不需要刪除
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 核心方法：根據交易階段獲取流水線                                 |
    //+------------------------------------------------------------------+
    ITradingPipeline* GetPipeline(ENUM_TRADING_STAGE stage)
    {
        if(m_registry == NULL)
        {
            m_last_result = new PipelineResult(false, "註冊器為空，無法查詢流水線", GetName(), ERROR_LEVEL_ERROR);
            return NULL;
        }

        // 首先確定階段屬於哪個事件
        ENUM_TRADING_EVENT event = GetEventFromStage(stage);
        if(event == -1)
        {
            m_last_result = new PipelineResult(false, "無效的交易階段", GetName(), ERROR_LEVEL_ERROR);
            return NULL;
        }

        // 通過註冊器獲取對應事件的容器
        TradingPipelineContainer* container = m_registry.GetRegisteredEventContainer(event);
        if(container == NULL)
        {
            m_last_result = new PipelineResult(false, "未找到對應事件的容器", GetName(), ERROR_LEVEL_WARNING);
            return NULL;
        }

        // 在容器中查找匹配階段的流水線
        ITradingPipeline* result = FindPipelineByStage(container, stage);
        if(result != NULL)
        {
            m_last_result = new PipelineResult(true, "成功找到流水線", GetName(), ERROR_LEVEL_INFO);
        }
        else
        {
            m_last_result = new PipelineResult(false, "未找到匹配階段的流水線", GetName(), ERROR_LEVEL_WARNING);
        }
        return result;
    }

    //+------------------------------------------------------------------+
    //| 核心方法：根據交易事件獲取流水線                                 |
    //+------------------------------------------------------------------+
    ITradingPipeline* GetPipeline(ENUM_TRADING_EVENT event)
    {
        if(m_registry == NULL)
        {
            m_last_result = new PipelineResult(false, "註冊器為空，無法查詢事件流水線", GetName(), ERROR_LEVEL_ERROR);
            return NULL;
        }

        // 通過註冊器直接獲取對應事件的容器
        TradingPipelineContainer* container = m_registry.GetRegisteredEventContainer(event);
        if(container != NULL)
        {
            m_last_result = new PipelineResult(true, "成功找到事件流水線", GetName(), ERROR_LEVEL_INFO);
        }
        else
        {
            m_last_result = new PipelineResult(false, "未找到對應事件的流水線", GetName(), ERROR_LEVEL_WARNING);
        }
        return container; // 容器本身就是一個 ITradingPipeline
    }

    //+------------------------------------------------------------------+
    //| 輔助方法：根據階段確定所屬事件                                   |
    //+------------------------------------------------------------------+
    ENUM_TRADING_EVENT GetEventFromStage(ENUM_TRADING_STAGE stage)
    {
        // 使用 TradingEventUtils 的靜態方法來判斷階段屬於哪個事件
        if(TradingEventUtils::IsStageOfEvent(stage, TRADING_INIT))
        {
            return TRADING_INIT;
        }
        else if(TradingEventUtils::IsStageOfEvent(stage, TRADING_TICK))
        {
            return TRADING_TICK;
        }
        else if(TradingEventUtils::IsStageOfEvent(stage, TRADING_DEINIT))
        {
            return TRADING_DEINIT;
        }

        return (ENUM_TRADING_EVENT)-1; // 無效階段
    }

    //+------------------------------------------------------------------+
    //| 輔助方法：在容器中查找匹配階段的流水線                           |
    //+------------------------------------------------------------------+
    ITradingPipeline* FindPipelineByStage(TradingPipelineContainer* container, ENUM_TRADING_STAGE stage)
    {
        if(container == NULL)
        {
            return NULL;
        }

        // 檢查容器本身是否為 StagePipeline 且階段匹配
        StagePipeline* stagePipeline = dynamic_cast<StagePipeline*>(container);
        if(stagePipeline != NULL && stagePipeline.GetStage() == stage)
        {
            return container;
        }

        // 遍歷容器中的所有流水線
        int count = container.GetPipelineCount();
        for(int i = 0; i < count; i++)
        {
            ITradingPipeline* pipeline = container.GetPipeline(i);
            if(pipeline == NULL)
            {
                continue;
            }

            // 如果是 TradingPipeline 類型，檢查其階段
            TradingPipeline* tradingPipeline = dynamic_cast<TradingPipeline*>(pipeline);
            if(tradingPipeline != NULL && tradingPipeline.GetStage() == stage)
            {
                return pipeline;
            }

            // 如果是 StagePipeline 類型，檢查其階段
            StagePipeline* subStagePipeline = dynamic_cast<StagePipeline*>(pipeline);
            if(subStagePipeline != NULL && subStagePipeline.GetStage() == stage)
            {
                return pipeline;
            }

            // 如果是容器類型，遞歸查找
            TradingPipelineContainer* subContainer = dynamic_cast<TradingPipelineContainer*>(pipeline);
            if(subContainer != NULL)
            {
                ITradingPipeline* found = FindPipelineByStage(subContainer, stage);
                if(found != NULL)
                {
                    return found;
                }
            }
        }

        return NULL;
    }

    //+------------------------------------------------------------------+
    //| 擴展方法：獲取所有匹配階段的流水線                               |
    //+------------------------------------------------------------------+
    int GetAllPipelinesByStage(ENUM_TRADING_STAGE stage, ITradingPipeline* &pipelines[])
    {
        if(m_registry == NULL)
        {
            ArrayResize(pipelines, 0);
            return 0;
        }

        // 臨時存儲找到的流水線
        ITradingPipeline* tempPipelines[];
        int count = 0;

        // 確定階段屬於哪個事件
        ENUM_TRADING_EVENT event = GetEventFromStage(stage);
        if(event != -1)
        {
            TradingPipelineContainer* container = m_registry.GetRegisteredEventContainer(event);
            if(container != NULL)
            {
                count = CollectPipelinesByStage(container, stage, tempPipelines);
            }
        }

        // 複製結果到輸出數組
        ArrayResize(pipelines, count);
        for(int i = 0; i < count; i++)
        {
            pipelines[i] = tempPipelines[i];
        }

        return count;
    }

    //+------------------------------------------------------------------+
    //| 擴展方法：獲取所有匹配事件的流水線                               |
    //+------------------------------------------------------------------+
    int GetAllPipelinesByEvent(ENUM_TRADING_EVENT event, ITradingPipeline* &pipelines[])
    {
        if(m_registry == NULL)
        {
            ArrayResize(pipelines, 0);
            return 0;
        }

        TradingPipelineContainer* container = m_registry.GetRegisteredEventContainer(event);
        if(container == NULL)
        {
            ArrayResize(pipelines, 0);
            return 0;
        }

        // 獲取容器中的所有流水線
        int count = container.GetPipelineCount() + 1; // +1 包含容器本身
        ArrayResize(pipelines, count);

        pipelines[0] = container; // 容器本身

        for(int i = 0; i < container.GetPipelineCount(); i++)
        {
            pipelines[i + 1] = container.GetPipeline(i);
        }

        return count;
    }

    //+------------------------------------------------------------------+
    //| 輔助方法：收集匹配階段的所有流水線                               |
    //+------------------------------------------------------------------+
    int CollectPipelinesByStage(TradingPipelineContainer* container, ENUM_TRADING_STAGE stage, ITradingPipeline* &pipelines[])
    {
        if(container == NULL)
        {
            return 0;
        }

        ITradingPipeline* tempPipelines[];
        int count = 0;

        // 檢查容器本身是否為 StagePipeline
        StagePipeline* stagePipeline = dynamic_cast<StagePipeline*>(container);
        if(stagePipeline != NULL && stagePipeline.GetStage() == stage)
        {
            ArrayResize(tempPipelines, count + 1);
            tempPipelines[count] = container;
            count++;
        }

        // 檢查容器中的所有流水線
        for(int i = 0; i < container.GetPipelineCount(); i++)
        {
            ITradingPipeline* pipeline = container.GetPipeline(i);
            if(pipeline == NULL)
            {
                continue;
            }

            // 檢查 TradingPipeline 類型
            TradingPipeline* tradingPipeline = dynamic_cast<TradingPipeline*>(pipeline);
            if(tradingPipeline != NULL && tradingPipeline.GetStage() == stage)
            {
                ArrayResize(tempPipelines, count + 1);
                tempPipelines[count] = pipeline;
                count++;
            }

            // 檢查 StagePipeline 類型
            StagePipeline* subStagePipeline = dynamic_cast<StagePipeline*>(pipeline);
            if(subStagePipeline != NULL && subStagePipeline.GetStage() == stage)
            {
                ArrayResize(tempPipelines, count + 1);
                tempPipelines[count] = pipeline;
                count++;
            }

            // 遞歸檢查子容器
            TradingPipelineContainer* subContainer = dynamic_cast<TradingPipelineContainer*>(pipeline);
            if(subContainer != NULL)
            {
                ITradingPipeline* subPipelines[];
                int subCount = CollectPipelinesByStage(subContainer, stage, subPipelines);

                if(subCount > 0)
                {
                    int oldSize = ArraySize(tempPipelines);
                    ArrayResize(tempPipelines, oldSize + subCount);
                    for(int j = 0; j < subCount; j++)
                    {
                        tempPipelines[oldSize + j] = subPipelines[j];
                    }
                    count += subCount;
                }
            }
        }

        // 複製結果
        ArrayResize(pipelines, count);
        for(int i = 0; i < count; i++)
        {
            pipelines[i] = tempPipelines[i];
        }

        return count;
    }

    //+------------------------------------------------------------------+
    //| 查詢方法：檢查是否存在指定階段的流水線                           |
    //+------------------------------------------------------------------+
    bool HasPipelineForStage(ENUM_TRADING_STAGE stage)
    {
        return GetPipeline(stage) != NULL;
    }

    //+------------------------------------------------------------------+
    //| 查詢方法：檢查是否存在指定事件的流水線                           |
    //+------------------------------------------------------------------+
    bool HasPipelineForEvent(ENUM_TRADING_EVENT event)
    {
        return GetPipeline(event) != NULL;
    }

    //+------------------------------------------------------------------+
    //| 信息方法：獲取探索器名稱                                         |
    //+------------------------------------------------------------------+
    string GetName() const
    {
        return m_name;
    }

    //+------------------------------------------------------------------+
    //| 信息方法：獲取探索器類型                                         |
    //+------------------------------------------------------------------+
    string GetType() const
    {
        return m_type;
    }

    //+------------------------------------------------------------------+
    //| 信息方法：獲取探索器描述                                         |
    //+------------------------------------------------------------------+
    string GetDescription() const
    {
        return m_description;
    }

    //+------------------------------------------------------------------+
    //| 信息方法：獲取註冊器指針                                         |
    //+------------------------------------------------------------------+
    TradingPipelineRegistry* GetRegistry() const
    {
        return m_registry;
    }

    //+------------------------------------------------------------------+
    //| 信息方法：檢查註冊器是否有效                                     |
    //+------------------------------------------------------------------+
    bool IsValid() const
    {
        return m_registry != NULL;
    }

    //+------------------------------------------------------------------+
    //| 統計方法：獲取指定事件的流水線數量                               |
    //+------------------------------------------------------------------+
    int GetPipelineCountByEvent(ENUM_TRADING_EVENT event)
    {
        if(m_registry == NULL)
        {
            return 0;
        }

        TradingPipelineContainer* container = m_registry.GetRegisteredEventContainer(event);
        if(container == NULL)
        {
            return 0;
        }

        return container.GetPipelineCount() + 1; // +1 包含容器本身
    }

    //+------------------------------------------------------------------+
    //| 統計方法：獲取指定階段的流水線數量                               |
    //+------------------------------------------------------------------+
    int GetPipelineCountByStage(ENUM_TRADING_STAGE stage)
    {
        ITradingPipeline* pipelines[];
        return GetAllPipelinesByStage(stage, pipelines);
    }

    //+------------------------------------------------------------------+
    //| 統計方法：獲取所有事件的流水線總數                               |
    //+------------------------------------------------------------------+
    int GetTotalPipelineCount()
    {
        if(m_registry == NULL)
        {
            return 0;
        }

        int total = 0;
        total += GetPipelineCountByEvent(TRADING_INIT);
        total += GetPipelineCountByEvent(TRADING_TICK);
        total += GetPipelineCountByEvent(TRADING_DEINIT);

        return total;
    }

    //+------------------------------------------------------------------+
    //| 調試方法：生成探索報告                                           |
    //+------------------------------------------------------------------+
    string GenerateExplorationReport()
    {
        if(m_registry == NULL)
        {
            return "探索器無效：註冊器為空";
        }

        string report = "";
        report += "=== 交易流水線探索報告 ===\n";
        report += StringFormat("探索器名稱：%s\n", m_name);
        report += StringFormat("探索器描述：%s\n", m_description);
        report += StringFormat("註冊器名稱：%s\n", m_registry.GetName());
        report += StringFormat("流水線總數：%d\n", GetTotalPipelineCount());
        report += "\n";

        // 按事件分組報告
        ENUM_TRADING_EVENT events[] = {TRADING_INIT, TRADING_TICK, TRADING_DEINIT};
        for(int i = 0; i < ArraySize(events); i++)
        {
            ENUM_TRADING_EVENT event = events[i];
            string eventName = TradingEventUtils::EventToString(event);
            int count = GetPipelineCountByEvent(event);

            report += StringFormat("事件 %s：%d 個流水線\n", eventName, count);

            if(count > 0)
            {
                ITradingPipeline* pipelines[];
                GetAllPipelinesByEvent(event, pipelines);

                for(int j = 0; j < ArraySize(pipelines); j++)
                {
                    if(pipelines[j] != NULL)
                    {
                        report += StringFormat("  - %s (%s)\n",
                                             pipelines[j].GetName(),
                                             pipelines[j].GetType());
                    }
                }
            }
            report += "\n";
        }

        return report;
    }

    //+------------------------------------------------------------------+
    //| 調試方法：生成階段映射報告                                       |
    //+------------------------------------------------------------------+
    string GenerateStageReport()
    {
        string report = "";
        report += "=== 交易階段映射報告 ===\n";

        // 所有階段
        ENUM_TRADING_STAGE stages[] = {
            // 初始化階段
            INIT_START, INIT_PARAMETERS, INIT_VARIABLES,
            INIT_ENVIRONMENT, INIT_INDICATORS, INIT_COMPLETE,
            // 交易階段
            TICK_DATA_FEED, TICK_SIGNAL_ANALYSIS, TICK_ORDER_MANAGEMENT,
            TICK_RISK_CONTROL, TICK_LOGGING,
            // 清理階段
            DEINIT_CLEANUP, DEINIT_SAVE_STATE, DEINIT_COMPLETE
        };

        for(int i = 0; i < ArraySize(stages); i++)
        {
            ENUM_TRADING_STAGE stage = stages[i];
            string stageName = TradingEventUtils::StageToString(stage);
            ENUM_TRADING_EVENT event = GetEventFromStage(stage);
            string eventName = (event != -1) ? TradingEventUtils::EventToString(event) : "UNKNOWN";
            bool hasPipeline = HasPipelineForStage(stage);

            report += StringFormat("階段 %s -> 事件 %s [%s]\n",
                                 stageName,
                                 eventName,
                                 hasPipeline ? "有流水線" : "無流水線");
        }

        return report;
    }

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }
};

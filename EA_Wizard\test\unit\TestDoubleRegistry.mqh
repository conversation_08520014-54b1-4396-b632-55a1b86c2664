//+------------------------------------------------------------------+
//|                                             TestDoubleRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../DoubleRegistry.mqh"

//+------------------------------------------------------------------+
//| DoubleRegistry 單元測試類                                        |
//+------------------------------------------------------------------+
class TestDoubleRegistry : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestDoubleRegistry(TestRunner* runner = NULL)
        : TestCase("TestDoubleRegistry"), m_runner(runner) {}

    // 析構函數
    virtual ~TestDoubleRegistry() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 DoubleRegistry 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestPrecisionControl();
        TestDoubleSpecificOperations();
        TestArrayOperations();
        TestComparisonMethods();
        TestStatistics();
        TestErrorHandling();

        Print("=== DoubleRegistry 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 DoubleRegistry 構造函數 ---");

        // 測試默認構造函數
        DoubleRegistry* registry1 = new DoubleRegistry();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestConstructor - 默認構造函數",
                registry1 != NULL,
                "默認構造函數創建成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestConstructor - 默認名稱",
                registry1.GetName() == "DoubleRegistry",
                "默認名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestConstructor - 默認精度",
                registry1.GetPrecision() == 0.00001,
                "默認精度設置正確"
            ));
        }

        delete registry1;

        // 測試帶參數構造函數
        DoubleRegistry* registry2 = new DoubleRegistry("CustomDoubleRegistry", "CustomDoubleType", 20, false, 0.001);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestConstructor - 自定義名稱",
                registry2.GetName() == "CustomDoubleRegistry",
                "自定義名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestConstructor - 自定義精度",
                MathAbs(registry2.GetPrecision() - 0.001) < 0.0001,
                "自定義精度設置正確"
            ));
        }

        delete registry2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 DoubleRegistry 基本屬性 ---");

        DoubleRegistry* registry = new DoubleRegistry("PropTest", "PropType", 10, true, 0.0001);

        if(m_runner != NULL)
        {
            // 測試初始狀態
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestBasicProperties - 初始註冊數量",
                registry.GetRegisteredCount() == 0,
                "初始註冊數量為0"
            ));

            // 測試精度獲取
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestBasicProperties - 精度獲取",
                MathAbs(registry.GetPrecision() - 0.0001) < 0.00001,
                "精度獲取正確"
            ));

            // 測試精度設置
            registry.SetPrecision(0.01);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestBasicProperties - 精度設置",
                MathAbs(registry.GetPrecision() - 0.01) < 0.001,
                "精度設置正確"
            ));

            // 測試最後結果
            PipelineResult* result = registry.GetLastResult();
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestBasicProperties - 最後結果不為空",
                result != NULL,
                "最後結果對象存在"
            ));
        }

        delete registry;
    }

    // 測試精度控制
    void TestPrecisionControl()
    {
        Print("--- 測試 DoubleRegistry 精度控制 ---");

        DoubleRegistry* registry = new DoubleRegistry("PrecisionTest", "PrecisionType", 5, true, 0.01);

        if(m_runner != NULL)
        {
            // 測試精度比較
            bool comparison1 = registry.CompareValues(1.234, 1.235);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestPrecisionControl - 精度範圍內比較",
                comparison1 == true,
                "精度範圍內的值比較為相等"
            ));

            bool comparison2 = registry.CompareValues(1.234, 1.245);  // 差值 0.011，大於精度 0.01
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestPrecisionControl - 精度範圍外比較",
                comparison2 == false,
                "精度範圍外的值比較為不等"
            ));

            // 測試不同精度設置
            registry.SetPrecision(0.001);
            bool comparison3 = registry.CompareValues(1.234, 1.236);  // 差值 0.002，大於精度 0.001
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestPrecisionControl - 更高精度比較",
                comparison3 == false,
                "更高精度下的值比較為不等"
            ));

            // 測試零精度
            registry.SetPrecision(0.0);
            bool comparison4 = registry.CompareValues(1.234, 1.234);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestPrecisionControl - 零精度比較",
                comparison4 == true,
                "零精度下完全相等的值比較正確"
            ));
        }

        delete registry;
    }

    // 測試Double特定操作
    void TestDoubleSpecificOperations()
    {
        Print("--- 測試 DoubleRegistry Double特定操作 ---");

        DoubleRegistry* registry = new DoubleRegistry("DoubleTest", "DoubleType", 5, true, 0.00001);

        if(m_runner != NULL)
        {
            // 測試註冊Double值
            bool regResult1 = registry.Register("double1", 123.456789);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 註冊Double值",
                regResult1 == true,
                "Double值註冊成功"
            ));

            // 測試獲取Double值
            double value1 = registry.GetRegisteredValue("double1", -1.0);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 獲取Double值",
                MathAbs(value1 - 123.456789) < 0.000001,
                "Double值獲取正確"
            ));

            // 測試更新Double值
            bool updateResult = registry.UpdateRegisteredValue("double1", 987.654321);
            double updatedValue = registry.GetRegisteredValue("double1", -1.0);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 更新Double值",
                updateResult && MathAbs(updatedValue - 987.654321) < 0.000001,
                "Double值更新成功"
            ));

            // 測試負數Double值
            bool regResult2 = registry.Register("double2", -123.456789);
            double value2 = registry.GetRegisteredValue("double2", 0.0);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 負數Double值",
                regResult2 && MathAbs(value2 + 123.456789) < 0.000001,
                "負數Double值處理正確"
            ));

            // 測試零值
            bool regResult3 = registry.Register("double3", 0.0);
            double value3 = registry.GetRegisteredValue("double3", -1.0);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 零值Double",
                regResult3 && MathAbs(value3) < 0.000001,
                "零值Double處理正確"
            ));

            // 測試小數值
            bool regResult4 = registry.Register("double4", 0.000123456);
            double value4 = registry.GetRegisteredValue("double4", 0.0);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestDoubleSpecificOperations - 小數值Double",
                regResult4 && MathAbs(value4 - 0.000123456) < 0.000000001,
                "小數值Double處理正確"
            ));
        }

        delete registry;
    }

    // 測試數組操作
    void TestArrayOperations()
    {
        Print("--- 測試 DoubleRegistry 數組操作 ---");

        DoubleRegistry* registry = new DoubleRegistry("ArrayTest", "ArrayType", 10, true, 0.00001);

        if(m_runner != NULL)
        {
            // 註冊多個Double值
            registry.Register("array1", 1.1);
            registry.Register("array2", 2.2);
            registry.Register("array3", 3.3);

            // 測試獲取所有鍵
            string keys[];
            int keyCount = registry.GetAllKeys(keys);

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestArrayOperations - 獲取所有鍵數量",
                keyCount == 3,
                "獲取所有鍵數量正確"
            ));

            // 測試獲取所有值
            double values[];
            int valueCount = registry.GetAllValues(values);

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestArrayOperations - 獲取所有值數量",
                valueCount == 3,
                "獲取所有值數量正確"
            ));

            // 驗證值的正確性
            bool hasValue1_1 = false, hasValue2_2 = false, hasValue3_3 = false;
            for(int i = 0; i < ArraySize(values); i++)
            {
                if(MathAbs(values[i] - 1.1) < 0.01) hasValue1_1 = true;
                if(MathAbs(values[i] - 2.2) < 0.01) hasValue2_2 = true;
                if(MathAbs(values[i] - 3.3) < 0.01) hasValue3_3 = true;
            }

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestArrayOperations - 值內容正確性",
                hasValue1_1 && hasValue2_2 && hasValue3_3,
                "所有註冊的值都正確獲取"
            ));
        }

        delete registry;
    }

    // 測試比較方法
    void TestComparisonMethods()
    {
        Print("--- 測試 DoubleRegistry 比較方法 ---");

        DoubleRegistry* registry = new DoubleRegistry("CompareTest", "CompareType", 5, true, 0.01);

        if(m_runner != NULL)
        {
            // 測試相等比較
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestComparisonMethods - 相等比較",
                registry.CompareValues(1.005, 1.006) == true,
                "精度範圍內相等比較正確"
            ));

            // 測試不等比較
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestComparisonMethods - 不等比較",
                registry.CompareValues(1.005, 1.020) == false,
                "精度範圍外不等比較正確"
            ));

            // 測試邊界值比較
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestComparisonMethods - 邊界值比較",
                registry.CompareValues(1.000, 1.010) == true,
                "邊界值比較正確"
            ));

            // 測試負數比較
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestComparisonMethods - 負數比較",
                registry.CompareValues(-1.005, -1.006) == true,
                "負數精度比較正確"
            ));
        }

        delete registry;
    }

    // 測試統計功能
    void TestStatistics()
    {
        Print("--- 測試 DoubleRegistry 統計功能 ---");

        DoubleRegistry* registry = new DoubleRegistry("StatTest", "StatType", 5, true, 0.001);

        if(m_runner != NULL)
        {
            // 註冊一些值
            registry.Register("stat1", 10.5);
            registry.Register("stat2", 20.7);

            // 測試統計信息
            string stats = registry.GetStatistics();
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestStatistics - 統計信息不為空",
                StringLen(stats) > 0,
                "統計信息生成成功"
            ));

            // 檢查統計信息是否包含關鍵信息
            bool hasCount = StringFind(stats, "數量") >= 0 || StringFind(stats, "Count") >= 0;
            bool hasName = StringFind(stats, registry.GetName()) >= 0;
            bool hasPrecision = StringFind(stats, "精度") >= 0 || StringFind(stats, "Precision") >= 0;

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestStatistics - 統計信息包含數量",
                hasCount,
                "統計信息包含數量信息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestStatistics - 統計信息包含名稱",
                hasName,
                "統計信息包含註冊器名稱"
            ));

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestStatistics - 統計信息包含精度",
                hasPrecision,
                "統計信息包含精度信息"
            ));
        }

        delete registry;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("--- 測試 DoubleRegistry 錯誤處理 ---");

        DoubleRegistry* registry = new DoubleRegistry("ErrorTest", "ErrorType", 2, true, 0.001);

        if(m_runner != NULL)
        {
            // 測試重複註冊
            registry.Register("duplicate", 10.5);
            bool duplicateResult = registry.Register("duplicate", 20.7);

            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 重複註冊",
                duplicateResult == false,
                "重複註冊正確失敗"
            ));

            // 測試更新不存在的鍵
            bool updateNonExistent = registry.UpdateRegisteredValue("nonexistent", 30.9);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 更新不存在的鍵",
                updateNonExistent == false,
                "更新不存在的鍵正確失敗"
            ));

            // 測試獲取不存在的鍵
            double defaultValue = -999.999;
            double nonExistentValue = registry.GetRegisteredValue("nonexistent", defaultValue);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 獲取不存在的鍵",
                MathAbs(nonExistentValue - defaultValue) < 0.001,
                "獲取不存在的鍵返回默認值"
            ));

            // 測試取消註冊不存在的鍵
            bool unregisterNonExistent = registry.Unregister("nonexistent");
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 取消註冊不存在的鍵",
                unregisterNonExistent == false,
                "取消註冊不存在的鍵正確失敗"
            ));

            // 測試禁用狀態下的操作
            registry.SetEnabled(false);
            bool disabledRegister = registry.Register("disabled", 40.1);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 禁用狀態註冊",
                disabledRegister == false,
                "禁用狀態下註冊正確失敗"
            ));

            bool disabledUpdate = registry.UpdateRegisteredValue("duplicate", 50.2);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 禁用狀態更新",
                disabledUpdate == false,
                "禁用狀態下更新正確失敗"
            ));

            // 測試無效精度設置
            registry.SetEnabled(true);
            registry.SetPrecision(-0.001);  // 負精度
            double retrievedPrecision = registry.GetPrecision();
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 負精度處理",
                retrievedPrecision >= 0,
                "負精度設置被正確處理"
            ));

            // 測試超出容量
            registry.Register("cap1", 60.3);  // 第二個項目
            bool overCapacity = registry.Register("cap2", 70.4);  // 第三個項目，應該失敗
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 超出容量",
                overCapacity == false,
                "超出容量時註冊正確失敗"
            ));

            // 測試極值處理
            bool regMaxDouble = registry.Unregister("duplicate") && registry.Register("maxDouble", DBL_MAX);
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 最大Double值",
                regMaxDouble,
                "最大Double值處理正確"
            ));

            // 測試NaN和無窮大值（如果支持）
            registry.Clear();
            bool regNaN = registry.Register("nanValue", MathSqrt(-1));  // NaN
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - NaN值處理",
                true,  // 只要不崩潰就算成功
                "NaN值處理不會導致崩潰"
            ));

            // 測試精度邊界比較
            registry.Clear();
            registry.SetPrecision(0.1);
            registry.Register("boundary1", 1.05);
            bool boundaryComparison = registry.CompareValues(1.05, 1.14);  // 差值0.09，小於精度0.1
            m_runner.RecordResult(new TestResult(
                "TestDoubleRegistry::TestErrorHandling - 精度邊界比較",
                boundaryComparison == true,
                "精度邊界比較正確"
            ));
        }

        delete registry;
    }
};

#include "../../mql4-lib/Collection/Vector.mqh"

template <typename Request>
class BatchTradeingRequest{
private:
    Vector<Request> m_requests;
public:
    void            AddRequest(Request request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    Request         GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| 訂單執行請求類別                                                 |
//+------------------------------------------------------------------+

class OrderRequest{
private:
    int         m_op;
    double      m_lots;
    int         m_stoploss;
    int         m_takeprofit;
    string      m_comment;
public:
    OrderRequest(int op,double lots,int stoploss,int takeprofit,string comment)
    : m_op(op), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}
    int         GetOp() const { return m_op; }
    double      GetLots() const { return m_lots; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
    string      GetComment() const { return m_comment; }
};

class BatchOrderRequest : public BatchTradeingRequest<OrderRequest*>{};

class ModifyRequest{
private:
    int         m_ticket;
    int         m_stoploss;
    int         m_takeprofit;
public:
    ModifyRequest(int ticket,int stoploss,int takeprofit)
    : m_ticket(ticket), m_stoploss(stoploss), m_takeprofit(takeprofit) {}
    int         GetTicket() const { return m_ticket; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
};

class BatchModifyRequest : public BatchTradeingRequest<ModifyRequest*>{};

//+------------------------------------------------------------------+
//| 關閉訂單請求類別                                                 |
//+------------------------------------------------------------------+
class CloseRequest{
private:
    int         m_ticket;
    double      m_lots;        // 0 表示完全關閉
public:
    CloseRequest(int ticket, double lots = 0.0)
    : m_ticket(ticket), m_lots(lots) {}
    int         GetTicket() const { return m_ticket; }
    double      GetLots() const { return m_lots; }
    bool        IsPartialClose() const { return m_lots > 0.0; }
};

//+------------------------------------------------------------------+
//| 批次關閉訂單請求類別                                             |
//+------------------------------------------------------------------+
class BatchCloseRequest : public BatchTradeingRequest<CloseRequest*>{};


//+------------------------------------------------------------------+
//| 交易結果類別                                                     |
//+------------------------------------------------------------------+
class TradingResult{
private:
    int         m_ticket;
    bool        m_success;
    string      m_message;
public:
    TradingResult(int ticket, bool success, string message)
    : m_ticket(ticket), m_success(success), m_message(message) {}
    int         GetTicket() const { return m_ticket; }
    bool        IsSuccess() const { return m_success; }
    string      GetMessage() const { return m_message; }
};

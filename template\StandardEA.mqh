//+------------------------------------------------------------------+
//|                                                   StandardEA.mq4 |
//|                                                    EA_Wizard     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../../mql4_module/EA_Wizard/TradingController.mqh"
#include "Config/Input.mqh"
#include "OnInit/index.mqh"
#include "OnTick/index.mqh"
#include "OnDeinit/index.mqh"

TradingController* controller = new TradingController(TradingPipelineDriver::GetInstance());

int OnInit()
{
   // 初始化代碼
   ENUM_INIT_RETCODE result = controller.OnInit();

   return(result);
}

void OnDeinit(const int reason)
{
   // 執行清理代碼
   controller.OnDeinit(reason);
}

void OnTick()
{
   // 執行 EA 處理器
   controller.OnTick();
}

//+------------------------------------------------------------------+
//| 自定義函數                                                        |
//+------------------------------------------------------------------+
// 您可以在這裡添加自定義函數

//+------------------------------------------------------------------+
//|                                                 OrderMatcher.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ORDER_MATCHER_MQH
#define ORDER_MATCHER_MQH

#include "../../../../mql4_module/mql4-lib/Trade/Order.mqh"
#include "../../../../mql4_module/mql4-lib/Lang/String.mqh"

//+------------------------------------------------------------------+
//| OrderMatcher Module                                              |
//| Provides flexible order matching capabilities for position      |
//| tracking system. Implements the OrderMatcher interface from     |
//| mql4-lib and provides concrete matcher classes for common       |
//| matching scenarios.                                              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Base OrderMatcher Implementation                                 |
//| Simple base class that always matches (useful for composition)  |
//+------------------------------------------------------------------+
class BaseOrderMatcher : public OrderMatcher
{
public:
    virtual bool      matches() const override { return true; }
};

//+------------------------------------------------------------------+
//| Ticket-based Order Matcher                                      |
//| Matches orders by specific ticket number                        |
//+------------------------------------------------------------------+
class TicketOrderMatcher : public OrderMatcher
{
private:
    int               m_ticket;

public:
                      TicketOrderMatcher(int ticket) : m_ticket(ticket) {}

    virtual bool      matches() const override
    {
        return OrderTicket() == m_ticket;
    }
};

//+------------------------------------------------------------------+
//| Symbol-based Order Matcher                                      |
//| Matches orders by trading symbol                                 |
//+------------------------------------------------------------------+
class SymbolOrderMatcher : public OrderMatcher
{
private:
    string            m_symbol;

public:
                      SymbolOrderMatcher(string symbol) : m_symbol(symbol) {}

    virtual bool      matches() const override
    {
        return OrderSymbol() == m_symbol;
    }
};

//+------------------------------------------------------------------+
//| Magic Number Order Matcher                                      |
//| Matches orders by magic number                                   |
//+------------------------------------------------------------------+
class MagicNumberOrderMatcher : public OrderMatcher
{
private:
    int               m_magicNumber;

public:
                      MagicNumberOrderMatcher(int magicNumber) : m_magicNumber(magicNumber) {}

    virtual bool      matches() const override
    {
        return OrderMagicNumber() == m_magicNumber;
    }
};

//+------------------------------------------------------------------+
//| Order Type Matcher                                              |
//| Matches orders by order type (OP_BUY, OP_SELL, etc.)           |
//+------------------------------------------------------------------+
class OrderTypeMatcher : public OrderMatcher
{
private:
    int               m_orderType;

public:
                      OrderTypeMatcher(int orderType) : m_orderType(orderType) {}

    virtual bool      matches() const override
    {
        return OrderType() == m_orderType;
    }
};

//+------------------------------------------------------------------+
//| Comment-based Order Matcher                                     |
//| Matches orders by comment substring                              |
//+------------------------------------------------------------------+
class CommentOrderMatcher : public OrderMatcher
{
private:
    string            m_comment;
    bool              m_exactMatch;

public:
                      CommentOrderMatcher(string comment, bool exactMatch = false)
                        : m_comment(comment), m_exactMatch(exactMatch) {}

    virtual bool      matches() const override
    {
        if (m_exactMatch)
        {
            return OrderComment() == m_comment;
        }
        else
        {
            return StringFind(OrderComment(), m_comment) >= 0;
        }
    }
};

//+------------------------------------------------------------------+
//| Profit Range Order Matcher                                      |
//| Matches orders within a profit range                            |
//+------------------------------------------------------------------+
class ProfitRangeOrderMatcher : public OrderMatcher
{
private:
    double            m_minProfit;
    double            m_maxProfit;

public:
                      ProfitRangeOrderMatcher(double minProfit, double maxProfit)
                        : m_minProfit(minProfit), m_maxProfit(maxProfit) {}

    virtual bool      matches() const override
    {
        double profit = OrderProfit() + OrderSwap() + OrderCommission();
        return profit >= m_minProfit && profit <= m_maxProfit;
    }
};

//+------------------------------------------------------------------+
//| Lot Size Range Order Matcher                                    |
//| Matches orders within a lot size range                          |
//+------------------------------------------------------------------+
class LotSizeRangeOrderMatcher : public OrderMatcher
{
private:
    double            m_minLots;
    double            m_maxLots;

public:
                      LotSizeRangeOrderMatcher(double minLots, double maxLots)
                        : m_minLots(minLots), m_maxLots(maxLots) {}

    virtual bool      matches() const override
    {
        double lots = OrderLots();
        return lots >= m_minLots && lots <= m_maxLots;
    }
};

//+------------------------------------------------------------------+
//| Time Range Order Matcher                                        |
//| Matches orders opened within a time range                       |
//+------------------------------------------------------------------+
class TimeRangeOrderMatcher : public OrderMatcher
{
private:
    datetime          m_startTime;
    datetime          m_endTime;

public:
                      TimeRangeOrderMatcher(datetime startTime, datetime endTime)
                        : m_startTime(startTime), m_endTime(endTime) {}

    virtual bool      matches() const override
    {
        datetime openTime = OrderOpenTime();
        return openTime >= m_startTime && openTime <= m_endTime;
    }
};

//+------------------------------------------------------------------+
//| Pending Orders Matcher                                          |
//| Matches only pending orders (OP_BUYLIMIT, OP_SELLLIMIT, etc.)  |
//+------------------------------------------------------------------+
class PendingOrderMatcher : public OrderMatcher
{
public:
    virtual bool      matches() const override
    {
        int orderType = OrderType();
        return orderType == OP_BUYLIMIT || orderType == OP_SELLLIMIT ||
               orderType == OP_BUYSTOP || orderType == OP_SELLSTOP;
    }
};

//+------------------------------------------------------------------+
//| Market Orders Matcher                                           |
//| Matches only market orders (OP_BUY, OP_SELL)                   |
//+------------------------------------------------------------------+
class MarketOrderMatcher : public OrderMatcher
{
public:
    virtual bool      matches() const override
    {
        int orderType = OrderType();
        return orderType == OP_BUY || orderType == OP_SELL;
    }
};

//+------------------------------------------------------------------+
//| Buy Orders Matcher                                              |
//| Matches all buy-related orders (OP_BUY, OP_BUYLIMIT, OP_BUYSTOP)|
//+------------------------------------------------------------------+
class BuyOrderMatcher : public OrderMatcher
{
public:
    virtual bool      matches() const override
    {
        int orderType = OrderType();
        return orderType == OP_BUY || orderType == OP_BUYLIMIT || orderType == OP_BUYSTOP;
    }
};

//+------------------------------------------------------------------+
//| Sell Orders Matcher                                             |
//| Matches all sell-related orders (OP_SELL, OP_SELLLIMIT, OP_SELLSTOP)|
//+------------------------------------------------------------------+
class SellOrderMatcher : public OrderMatcher
{
public:
    virtual bool      matches() const override
    {
        int orderType = OrderType();
        return orderType == OP_SELL || orderType == OP_SELLLIMIT || orderType == OP_SELLSTOP;
    }
};

//+------------------------------------------------------------------+
//| Composite Order Matchers                                        |
//| Advanced matchers that combine multiple criteria                |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| AND Composite Matcher                                           |
//| Matches orders that satisfy ALL provided matchers               |
//+------------------------------------------------------------------+
class AndOrderMatcher : public OrderMatcher
{
private:
    OrderMatcher*     m_matchers[];
    int               m_count;

public:
                      AndOrderMatcher()
    {
        m_count = 0;
        ArrayResize(m_matchers, 0);
    }

    virtual          ~AndOrderMatcher()
    {
        // Note: We don't delete the matchers as they might be managed externally
        ArrayFree(m_matchers);
    }

    void              addMatcher(OrderMatcher* matcher)
    {
        if (CheckPointer(matcher) != POINTER_INVALID)
        {
            ArrayResize(m_matchers, m_count + 1);
            m_matchers[m_count] = matcher;
            m_count++;
        }
    }

    virtual bool      matches() const override
    {
        for (int i = 0; i < m_count; i++)
        {
            if (CheckPointer(m_matchers[i]) != POINTER_INVALID)
            {
                if (!m_matchers[i].matches())
                {
                    return false;
                }
            }
        }
        return true;
    }
};

//+------------------------------------------------------------------+
//| OR Composite Matcher                                            |
//| Matches orders that satisfy ANY of the provided matchers        |
//+------------------------------------------------------------------+
class OrOrderMatcher : public OrderMatcher
{
private:
    OrderMatcher*     m_matchers[];
    int               m_count;

public:
                      OrOrderMatcher()
    {
        m_count = 0;
        ArrayResize(m_matchers, 0);
    }

    virtual          ~OrOrderMatcher()
    {
        ArrayFree(m_matchers);
    }

    void              addMatcher(OrderMatcher* matcher)
    {
        if (CheckPointer(matcher) != POINTER_INVALID)
        {
            ArrayResize(m_matchers, m_count + 1);
            m_matchers[m_count] = matcher;
            m_count++;
        }
    }

    virtual bool      matches() const override
    {
        if (m_count == 0) return false;

        for (int i = 0; i < m_count; i++)
        {
            if (CheckPointer(m_matchers[i]) != POINTER_INVALID)
            {
                if (m_matchers[i].matches())
                {
                    return true;
                }
            }
        }
        return false;
    }
};

//+------------------------------------------------------------------+
//| NOT Matcher                                                     |
//| Inverts the result of another matcher                           |
//+------------------------------------------------------------------+
class NotOrderMatcher : public OrderMatcher
{
private:
    OrderMatcher*     m_matcher;

public:
                      NotOrderMatcher(OrderMatcher* matcher) : m_matcher(matcher) {}

    virtual bool      matches() const override
    {
        if (CheckPointer(m_matcher) == POINTER_INVALID)
        {
            return true;
        }
        return !m_matcher.matches();
    }
};

#endif // ORDER_MATCHER_MQH

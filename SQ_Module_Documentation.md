# SQ.mq4 Expert Advisor 模組化文檔

[![MQL4](https://img.shields.io/badge/MQL4-Modular%20Architecture-blue.svg)](https://www.mql5.com)
[![Documentation](https://img.shields.io/badge/Documentation-Complete-green.svg)](https://github.com)
[![Version](https://img.shields.io/badge/Version-OrderReliable%20v36-yellow.svg)](LICENSE)

## 目錄

- [模組分類架構](#模組分類架構)
- [核心控制模組](#核心控制模組)
- [交易策略模組](#交易策略模組)
- [訂單管理模組](#訂單管理模組)
- [風險管理模組](#風險管理模組)
- [技術指標模組](#技術指標模組)
- [輔助功能模組](#輔助功能模組)
- [OrderReliable 庫模組](#orderreliable-庫模組)
- [模組依賴關係](#模組依賴關係)
- [函數調用頻率分析](#函數調用頻率分析)

## 模組分類架構

SQ.mq4 Expert Advisor 採用分層模組化架構，共分為 7 個主要模組：

```
SQ Expert Advisor 架構
├── 🎯 核心控制模組 (Core Control Module)
├── 📈 交易策略模組 (Trading Strategy Module)
├── 📋 訂單管理模組 (Order Management Module)
├── ⚠️ 風險管理模組 (Risk Management Module)
├── 📊 技術指標模組 (Technical Indicator Module)
├── 🔧 輔助功能模組 (Utility Module)
└── 🛡️ OrderReliable 庫模組 (OrderReliable Library Module)
```

### 模組層級結構

| 層級   | 模組名稱         | 職責範圍         | 依賴關係    |
| ------ | ---------------- | ---------------- | ----------- |
| **L1** | 核心控制模組     | 主要執行流程控制 | 無依賴      |
| **L2** | 交易策略模組     | 交易信號生成     | 依賴 L3, L4 |
| **L2** | 訂單管理模組     | 訂單生命週期管理 | 依賴 L3, L4 |
| **L3** | 風險管理模組     | 風險控制和保護   | 依賴 L4     |
| **L3** | 技術指標模組     | 技術分析計算     | 依賴 L4     |
| **L4** | 輔助功能模組     | 基礎工具函數     | 無依賴      |
| **L4** | OrderReliable 庫 | 可靠訂單執行     | 無依賴      |

## 核心控制模組

### 模組概述

核心控制模組是整個 EA 的主要控制中心，負責協調各個子模組的執行順序和數據流向。

### 主要函數

#### `OnTick()` - 主控制器

**調用頻率**: 每個 Tick（極高頻率）
**性能影響**: 關鍵路徑
**功能描述**: EA 的主要入口點，協調所有模組的執行

```mql4
void OnTick() {
    sqInitStart();                           // 初始化和新K線檢測
    sqManageOrders(MagicNumber);            // 訂單管理
    openingOrdersAllowed = sqHandleTradingOptions(); // 交易選項檢查

    // 交易策略執行
    if (sqIsBarOpen() && 背離條件檢測) {
        _ticket = sqOpenOrder(OP_BUY, "Current", 0.1, 0, MagicNumber, "", 0, false, false, CLR_NONE);
        // 設定止損止盈和退出機制
    }
}
```

**輸入參數**: 無
**返回值**: void
**調用關係**: 調用所有其他模組的主要函數

#### `OnInit()` - 初始化控制器

**調用頻率**: EA 啟動時一次
**性能影響**: 低
**功能描述**: EA 初始化設定和驗證

```mql4
int OnInit() {
    VerboseLog("Starting the EA");
    if(!checkComments()) return(INIT_FAILED);
    gPointCoef = calculatePointCoef(Symbol());
    if(sqDisplayInfoPanel) sqInitInfoPanel();
    SQTime = new CSQTime();
    if(!IsTesting() && !IsOptimization()) initTimer();
    return(INIT_SUCCEEDED);
}
```

#### `sqInitStart()` - 初始化和 K 線檢測

**調用頻率**: 每個 Tick
**性能影響**: 中等
**功能描述**: 檢測新 K 線開盤和基本初始化

```mql4
void sqInitStart() {
    // 新K線檢測邏輯
    if(_sqLastOpenBarTime == 0) {
        _sqLastOpenBarTime = Time[0];
        _sqIsBarOpen = true;
    } else {
        if(_sqLastOpenBarTime != Time[0]) {
            // 處理開盤延遲
            bool processBarOpen = true;
            if(OpenBarDelay > 0) {
                long diffInSeconds = TimeCurrent() - Time[0];
                processBarOpen = (diffInSeconds >= OpenBarDelay * 60);
            }
            if(processBarOpen) {
                _sqIsBarOpen = true;
                _sqLastOpenBarTime = Time[0];
            }
        } else {
            _sqIsBarOpen = false;
        }
    }

    // 資料驗證和介面更新
    if(_sqIsBarOpen && Bars<30) {
        Print("NOT ENOUGH DATA: Less Bars than 30");
        return;
    }
    if(!IsTesting() && !IsOptimization()) {
        sqTextFillOpens();
        if(_sqIsBarOpen) sqTextFillTotals();
    }
}
```

**關鍵變數**:

- `_sqLastOpenBarTime`: 上次開盤時間
- `_sqIsBarOpen`: 新 K 線開盤標誌
- `OpenBarDelay`: 開盤延遲分鐘數

## 交易策略模組

### 模組概述

交易策略模組實現基於 MACD 指標的背離檢測策略，負責生成交易信號和執行開倉操作。

### 核心策略邏輯

#### MACD 背離檢測策略

**策略原理**: 當 MACD 指標創新高而價格創新低時產生買入信號

```mql4
// 主要交易規則
if (sqIsBarOpen()
    && ((sqMACD(NULL,0, MACDFast, MACDSlow, MACDSmooth, PRICE_CLOSE, 0, 1)
         > sqIndicatorHighest(DivergencePeriod, 0, "sqMACD(...)"))
    && (iLow(NULL,0, 1) < sqIndicatorLowest(DivergencePeriod, 0, "iLow(...)")))) {

    // 執行買入操作
    _ticket = sqOpenOrder(OP_BUY, "Current", 0.1, 0, MagicNumber, "", 0, false, false, CLR_NONE);

    if(_ticket > 0 && OrderSelect(_ticket, SELECT_BY_TICKET)) {
        // 設定止損止盈
        sqSetSLandPT(_ticket,
                     sqGetSLLevel("Current", OP_BUY, 0, 1, StopLoss),
                     sqGetPTLevel("Current", OP_BUY, 0, 1, ProfitTarget));

        // 初始化退出機制
        sqSetGlobalVariable(_ticket, "MoveSL2BE", sqStringHash("MoveSL2BE"));
        sqSetGlobalVariable(_ticket, "MoveSL2BEType", SLPTTYPE_RANGE);
        sqSetGlobalVariable(_ticket, "ExitAfterBars", ExitAfterBars);
    }
}
```

### 主要函數

#### `sqOpenOrder()` - 開倉執行函數

**調用頻率**: 信號觸發時
**性能影響**: 高（涉及網路通訊）
**功能描述**: 執行市價或掛單開倉操作

```mql4
int sqOpenOrder(int orderType, string symbol, double size, double price,
                int magicNumber, string comment, datetime expirationInTime,
                bool useStopLoss, bool useTakeProfit, color arrowColor) {

    string correctedSymbol = correctSymbol(symbol);
    string commentToUse = (comment != "") ? comment : CustomComment;

    int ticket = OrderSendReliable(correctedSymbol, orderType, size, price,
                                   correctSlippage(sqMaxEntrySlippage, correctedSymbol),
                                   0, 0, commentToUse, magicNumber, expirationInTime, arrowColor);

    if(ticket > 0) {
        sqResetGlobalVariablesForTicket(ticket);
    }

    return(ticket);
}
```

**輸入參數**:

- `orderType`: 訂單類型 (OP_BUY, OP_SELL, OP_BUYLIMIT, OP_SELLLIMIT, OP_BUYSTOP, OP_SELLSTOP)
- `symbol`: 交易品種
- `size`: 交易手數
- `price`: 開倉價格（市價單為 0）
- `magicNumber`: 魔術數字
- `comment`: 訂單註釋
- `expirationInTime`: 到期時間
- `useStopLoss/useTakeProfit`: 是否使用止損止盈
- `arrowColor`: 箭頭顏色

**返回值**: 訂單票號（成功）或 -1（失敗）

### 策略參數配置

| 參數名稱           | 預設值 | 說明            | 調整建議    |
| ------------------ | ------ | --------------- | ----------- |
| `MACDFast`         | 25     | MACD 快線週期   | 12-50 範圍  |
| `MACDSlow`         | 50     | MACD 慢線週期   | 26-100 範圍 |
| `MACDSmooth`       | 5      | MACD 信號線平滑 | 5-15 範圍   |
| `DivergencePeriod` | 5      | 背離檢測週期    | 3-10 範圍   |

## 訂單管理模組

### 模組概述

訂單管理模組負責管理所有開倉訂單的生命週期，包括止損止盈管理、跟蹤止損、時間退出等功能。

### 主要函數

#### `sqManageOrders()` - 訂單管理主控制器

**調用頻率**: 每個 Tick
**性能影響**: 中等
**功能描述**: 遍歷所有訂單並執行相應的管理操作

```mql4
void sqManageOrders(int magicNumber) {
    if(_sqIsBarOpen) {
        for(int i=OrdersTotal()-1; i>=0; i--) {
            if (OrderSelect(i,SELECT_BY_POS)==true) {
                if(OrderMagicNumber() != magicNumber || isMarketClosed(OrderSymbol())) {
                    continue;
                }

                int ticket = OrderTicket();

                if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
                    // 處理實時訂單的退出方法
                    sqManageSL2BE(ticket);           // 移動止損到盈虧平衡點
                    sqManageTrailingStop(ticket);    // 跟蹤止損
                    sqManageExitAfterXBars(ticket);  // X根K線後退出
                }

                sqManageOrderExpiration(ticket);     // 訂單到期管理
            }

            if(OrdersTotal() <= 0) return;
        }
    }
}
```

#### `sqClosePositionAtMarket()` - 市價平倉函數

**調用頻率**: 退出信號觸發時
**性能影響**: 高（涉及網路通訊）
**功能描述**: 以市價關閉指定大小的倉位

```mql4
bool sqClosePositionAtMarket(double size) {
    int ticket = OrderTicket();
    Verbose("Closing order with ticket: ", IntegerToString(ticket));

    int orderType = OrderType();
    if(orderType != OP_BUY && orderType != OP_SELL) {
        Verbose("Trying to close non-live order");
        return(false);
    }

    if(!sqCheckConnected()) return(false);

    double price = sqGetClosePrice(orderType, OrderSymbol(), 0);
    bool result = OrderCloseReliable(ticket, size, price,
                                     correctSlippage(sqMaxCloseSlippage, OrderSymbol()));

    if(result) {
        Verbose("Order deleted successfuly");
        return(true);
    }

    return(false);
}
```

### 訂單選擇和篩選函數

#### `sqSelectOrder()` - 智能訂單選擇

**功能描述**: 根據多個條件篩選和選擇訂單

```mql4
bool sqSelectOrder(int magicNumber, string symbol, int direction, string comment,
                   bool goFromNewest=true, bool skipPending=true, bool skipFilled=false) {
    int cc = 0;

    if(orderSelectTimeout > 0) Sleep(orderSelectTimeout);

    if(goFromNewest) {
        for (cc = OrdersTotal() - 1; cc >= 0; cc--) {
            if(orderFits(cc, magicNumber, symbol, direction, comment, skipPending, skipFilled)) {
                return true;
            }
        }
    } else {
        for (cc = 0; cc < OrdersTotal(); cc++) {
            if(orderFits(cc, magicNumber, symbol, direction, comment, skipPending, skipFilled)) {
                return true;
            }
        }
    }
    return(false);
}
```

**輸入參數**:

- `magicNumber`: 魔術數字篩選
- `symbol`: 交易品種篩選
- `direction`: 方向篩選（1=多頭, -1=空頭, 0=全部）
- `comment`: 註釋篩選
- `goFromNewest`: 是否從最新訂單開始搜尋
- `skipPending`: 是否跳過掛單
- `skipFilled`: 是否跳過已成交訂單

**返回值**: 是否找到符合條件的訂單

## 風險管理模組

### 模組概述

風險管理模組提供多層次的風險控制機制，包括止損止盈管理、移動止損、跟蹤止損、時間控制等功能。

### 主要風險控制機制

#### 1. 移動止損到盈虧平衡點 (`sqManageSL2BE`)

**觸發條件**: 盈利達到指定點數後
**功能描述**: 將止損移動到開倉價格，鎖定盈虧平衡

```mql4
void sqManageSL2BE(int ticket) {
    if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;

    double sl2beValue = sqGetGlobalVariable(ticket, "MoveSL2BE");
    if(sl2beValue <= 0) return;

    int sl2beType = (int)sqGetGlobalVariable(ticket, "MoveSL2BEType");
    double currentSL = OrderStopLoss();
    double openPrice = OrderOpenPrice();

    if(OrderType() == OP_BUY) {
        double plValue = NormalizeDouble(Bid - openPrice, Digits);
        if(plValue >= sl2beValue && (currentSL == 0 || currentSL < openPrice)) {
            sqOrderModifySL(ticket, openPrice, sl2beType);
        }
    } else { // OP_SELL
        double plValue = NormalizeDouble(openPrice - Ask, Digits);
        if(plValue >= sl2beValue && (currentSL == 0 || currentSL > openPrice)) {
            sqOrderModifySL(ticket, openPrice, sl2beType);
        }
    }
}
```

#### 2. 跟蹤止損管理 (`sqManageTrailingStop`)

**功能描述**: 根據價格變動動態調整止損位置

#### 3. 時間退出管理 (`sqManageExitAfterXBars`)

**功能描述**: 在指定 K 線數量後強制平倉

```mql4
void sqManageExitAfterXBars(int ticket) {
    if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;

    int exitAfterBars = (int)sqGetGlobalVariable(ticket, "ExitAfterBars");
    if(exitAfterBars <= 0) return;

    int barsOpen = sqGetOpenBarsForOrder(ticket, exitAfterBars+10);
    if(barsOpen >= exitAfterBars) {
        Verbose("Order with ticket: ", IntegerToString(ticket), " reached ExitAfterBars limit");
        sqClosePositionAtMarket(OrderLots());
    }
}
```

### 交易選項控制

#### `sqHandleTradingOptions()` - 交易選項檢查

**功能描述**: 檢查各種交易限制條件

```mql4
bool sqHandleTradingOptions() {
    // 週末交易控制
    if(DontTradeOnWeekends && !sqIsWeekendTradingAllowed()) {
        return false;
    }

    // 時間範圍限制
    if(LimitTimeRange && !sqIsInTimeRange()) {
        return false;
    }

    // 日終退出檢查
    if(ExitAtEndOfDay && sqIsEndOfDay()) {
        sqCloseAllPositions();
        return false;
    }

    // 週五退出檢查
    if(ExitOnFriday && sqIsFridayExit()) {
        sqCloseAllPositions();
        return false;
    }

    return true;
}
```

### 風險參數配置

| 參數名稱             | 預設值 | 說明             | 風險等級 |
| -------------------- | ------ | ---------------- | -------- |
| `StopLoss`           | 50     | 固定止損點數     | 中等     |
| `ProfitTarget`       | 50     | 固定止盈點數     | 中等     |
| `MoveSL2BE`          | 40     | 移動止損觸發點數 | 低       |
| `ExitAfterBars`      | 25     | 強制退出 K 線數  | 低       |
| `sqMaxEntrySlippage` | 5      | 最大入場滑點     | 中等     |
| `sqMaxCloseSlippage` | 0      | 最大平倉滑點     | 低       |

## 技術指標模組

### 模組概述

技術指標模組提供各種技術分析指標的計算功能，是交易策略模組的重要支撐。主要包括 MACD、移動平均線、以及各種輔助指標函數。

### 核心指標函數

#### `sqMACD()` - MACD 指標計算

**調用頻率**: 每個新 K 線
**性能影響**: 中等
**功能描述**: 計算 MACD 指標值

```mql4
double sqMACD(string symbol, int timeframe, int fast_ema_period, int slow_ema_period,
              int signal_period, int applied_price, int mode, int shift) {

    fast_ema_period = sqFixRanges(fast_ema_period, 1, 200, 12);
    slow_ema_period = sqFixRanges(slow_ema_period, 1, 200, 26);
    signal_period = sqFixRanges(signal_period, 1, 50, 9);
    applied_price = sqFixRanges(applied_price, 0, 6, 0);
    mode = sqFixRanges(mode, 0, 1, 0);

    return roundValue(iMACD(symbol, timeframe, fast_ema_period, slow_ema_period,
                           signal_period, applied_price, mode, shift));
}
```

**輸入參數**:

- `symbol`: 交易品種
- `timeframe`: 時間框架
- `fast_ema_period`: 快線週期
- `slow_ema_period`: 慢線週期
- `signal_period`: 信號線週期
- `applied_price`: 應用價格類型
- `mode`: 模式（0=主線, 1=信號線）
- `shift`: 偏移量

**返回值**: MACD 指標值

#### `sqMA()` - 移動平均線計算

**調用頻率**: 根據策略需求
**性能影響**: 低
**功能描述**: 計算各種類型的移動平均線

```mql4
double sqMA(string symbol, int timeframe, int ma_period, int ma_shift,
            int ma_method, int applied_price, int shift) {

    ma_method = sqFixRanges(ma_method, 0, 3, 0);
    applied_price = sqFixRanges(applied_price, 0, 6, 0);

    return roundValue(iMA(symbol, timeframe, ma_period, ma_shift, ma_method, applied_price, shift));
}
```

#### `sqIndicatorHighest()` - 指標最高值檢測

**功能描述**: 在指定週期內尋找指標的最高值

```mql4
double sqIndicatorHighest(int period, int shift, string indicatorCall) {
    double maxValue = -999999999;
    double currentValue = 0;

    for(int i = shift; i < shift + period; i++) {
        // 動態執行指標計算
        currentValue = executeIndicatorCall(indicatorCall, i);
        if(currentValue > maxValue) {
            maxValue = currentValue;
        }
    }

    return roundValue(maxValue);
}
```

#### `sqIndicatorLowest()` - 指標最低值檢測

**功能描述**: 在指定週期內尋找指標的最低值

```mql4
double sqIndicatorLowest(int period, int shift, string indicatorCall) {
    double minValue = 999999999;
    double currentValue = 0;

    for(int i = shift; i < shift + period; i++) {
        currentValue = executeIndicatorCall(indicatorCall, i);
        if(currentValue < minValue) {
            minValue = currentValue;
        }
    }

    return roundValue(minValue);
}
```

### 輔助技術指標

#### `sqTEMA()` - 三重指數移動平均線

```mql4
double sqTEMA(string symbol, int timeframe, int ma_period, int applied_price, int shift) {
    return roundValue(iCustom(symbol, timeframe, "SqTEMA", ma_period, applied_price, 0, shift));
}
```

#### `sqIsUptrend()` - 趨勢判斷

```mql4
bool sqIsUptrend(string symbol, int timeframe, int method) {
    if(method == 0) {
        return (iClose(symbol, timeframe, 1) > sqMA(symbol, timeframe, 200, 0, MODE_SMA, PRICE_CLOSE, 1));
    }
    return(false);
}
```

### 指標參數驗證

所有技術指標函數都包含參數驗證機制：

```mql4
int sqFixRanges(int value, int min, int max, int defaultValue) {
    if(value < min || value > max) {
        return defaultValue;
    }
    return value;
}
```

## 輔助功能模組

### 模組概述

輔助功能模組提供基礎工具函數，包括數值處理、字串操作、時間管理、全域變數管理等功能。

### 數值處理函數

#### `roundValue()` - 數值四捨五入

```mql4
double roundValue(double value) {
    return NormalizeDouble(value, Digits);
}
```

#### `sqConvertToRealPips()` - 點數轉換

```mql4
double sqConvertToRealPips(string symbol, double pips) {
    double tickSize = MarketInfo(symbol, MODE_TICKSIZE);
    double point = MarketInfo(symbol, MODE_POINT);

    if(tickSize == 0 || point == 0) return pips;

    return pips * point;
}
```

#### `sqFixMarketPrice()` - 市價修正

```mql4
double sqFixMarketPrice(double price, string symbol) {
    symbol = correctSymbol(symbol);
    double tickSize = MarketInfo(symbol, MODE_TICKSIZE);

    if(tickSize == 0) return price;

    int digits = (int) MarketInfo(symbol, MODE_DIGITS);
    double finalPrice = tickSize * MathRound(NormalizeDouble(price, digits) / tickSize);
    return NormalizeDouble(finalPrice, digits);
}
```

### 全域變數管理

#### `sqSetGlobalVariable()` - 設定全域變數

```mql4
void sqSetGlobalVariable(int ticket, string variableName, double value) {
    string globalVarName = StringConcatenate("SQ_", IntegerToString(ticket), "_", variableName);
    GlobalVariableSet(globalVarName, value);
}
```

#### `sqGetGlobalVariable()` - 獲取全域變數

```mql4
double sqGetGlobalVariable(int ticket, string variableName) {
    string globalVarName = StringConcatenate("SQ_", IntegerToString(ticket), "_", variableName);
    if(GlobalVariableCheck(globalVarName)) {
        return GlobalVariableGet(globalVarName);
    }
    return 0;
}
```

### 字串和雜湊函數

#### `sqStringHash()` - 字串雜湊

```mql4
long sqStringHash(string str) {
    long hash = 0;
    for(int i = 0; i < StringLen(str); i++) {
        hash = hash * 31 + StringGetCharacter(str, i);
    }
    return hash;
}
```

### 時間管理類 (`CSQTime`)

```mql4
class CSQTime {
public:
    datetime addDays(datetime time, int days) {
        return time + days * 24 * 3600;
    }

    datetime setDayOfWeek(datetime time, int dayOfWeek) {
        int currentDayOfWeek = TimeDayOfWeek(time);
        int diff = dayOfWeek - currentDayOfWeek;
        return time + diff * 24 * 3600;
    }

    datetime setHHMM(datetime time, string timeStr) {
        string parts[];
        StringSplit(timeStr, ':', parts);
        if(ArraySize(parts) >= 2) {
            int hour = (int)StringToInteger(parts[0]);
            int minute = (int)StringToInteger(parts[1]);

            MqlDateTime dt;
            TimeToStruct(time, dt);
            dt.hour = hour;
            dt.min = minute;
            dt.sec = 0;

            return StructToTime(dt);
        }
        return time;
    }
};
```

### 連接和市場狀態檢查

#### `sqCheckConnected()` - 連接檢查

```mql4
bool sqCheckConnected() {
    if(!IsConnected()) {
        Verbose("No connection to trade server");
        return false;
    }
    if(!IsTradeAllowed()) {
        Verbose("Trade is not allowed");
        return false;
    }
    return true;
}
```

#### `isMarketClosed()` - 市場狀態檢查

```mql4
bool isMarketClosed(string symbol) {
    double bid = MarketInfo(symbol, MODE_BID);
    double ask = MarketInfo(symbol, MODE_ASK);
    return (bid == 0 || ask == 0);
}
```

## OrderReliable 庫模組

### 模組概述

OrderReliable 庫是一個專門設計用於提高 MetaTrader 4 訂單執行可靠性的第三方庫。該模組封裝了所有訂單操作，提供錯誤重試、網路問題處理等功能。

### 版本資訊

- **版本**: v36
- **功能**: 可靠的訂單執行
- **錯誤處理**: 自動重試機制
- **網路處理**: 連接問題自動恢復

### 核心函數

#### `OrderSendReliable()` - 可靠開倉

**功能描述**: OrderSend() 的可靠版本，具有錯誤重試機制

```mql4
int OrderSendReliable(string symbol, int cmd, double volume, double price,
                      int slippage, double stoploss, double takeprofit,
                      string comment, int magic, datetime expiration, color arrow_color) {

    int ticket = -1;
    int retryCount = 0;
    int maxRetries = 5;

    while(retryCount < maxRetries) {
        ticket = OrderSend(symbol, cmd, volume, price, slippage, stoploss,
                          takeprofit, comment, magic, expiration, arrow_color);

        if(ticket > 0) {
            OrderReliablePrint("OrderSend successful, ticket: " + IntegerToString(ticket));
            return ticket;
        }

        int error = GetLastError();
        if(!IsRetriableError(error)) {
            OrderReliablePrint("Non-retriable error: " + IntegerToString(error));
            break;
        }

        retryCount++;
        Sleep(1000 * retryCount); // 遞增延遲
        RefreshRates();
    }

    return -1;
}
```

#### `OrderCloseReliable()` - 可靠平倉

**功能描述**: OrderClose() 的可靠版本

```mql4
bool OrderCloseReliable(int ticket, double lots, double price, int slippage, color arrow_color) {
    bool result = false;
    int retryCount = 0;
    int maxRetries = 5;

    while(retryCount < maxRetries) {
        result = OrderClose(ticket, lots, price, slippage, arrow_color);

        if(result) {
            OrderReliablePrint("OrderClose successful for ticket: " + IntegerToString(ticket));
            return true;
        }

        int error = GetLastError();
        if(!IsRetriableError(error)) {
            OrderReliablePrint("Non-retriable error in OrderClose: " + IntegerToString(error));
            break;
        }

        retryCount++;
        Sleep(1000 * retryCount);
        RefreshRates();
    }

    return false;
}
```

#### `OrderModifyReliable()` - 可靠修改

**功能描述**: OrderModify() 的可靠版本

```mql4
bool OrderModifyReliable(int ticket, double price, double stoploss, double takeprofit,
                        datetime expiration, color arrow_color) {
    bool result = false;
    int retryCount = 0;
    int maxRetries = 5;

    while(retryCount < maxRetries) {
        result = OrderModify(ticket, price, stoploss, takeprofit, expiration, arrow_color);

        if(result) {
            OrderReliablePrint("OrderModify successful for ticket: " + IntegerToString(ticket));
            return true;
        }

        int error = GetLastError();
        if(!IsRetriableError(error)) {
            OrderReliablePrint("Non-retriable error in OrderModify: " + IntegerToString(error));
            break;
        }

        retryCount++;
        Sleep(1000 * retryCount);
        RefreshRates();
    }

    return false;
}
```

### 錯誤處理機制

#### `IsRetriableError()` - 可重試錯誤判斷

```mql4
bool IsRetriableError(int error) {
    switch(error) {
        case ERR_SERVER_BUSY:
        case ERR_NO_CONNECTION:
        case ERR_TOO_FREQUENT_REQUESTS:
        case ERR_TRADE_TIMEOUT:
        case ERR_PRICE_CHANGED:
        case ERR_OFF_QUOTES:
        case ERR_BROKER_BUSY:
        case ERR_REQUOTE:
            return true;
        default:
            return false;
    }
}
```

#### `OrderReliablePrint()` - 日誌輸出

```mql4
void OrderReliablePrint(string s) {
    if (ErrorLevel >= 99 || (!(IsTesting() || IsOptimization()))) {
        if (ErrorLevel > 0) {
            Print(OrderReliable_Fname + " " + OrderReliableVersion + ":     " + s);
        }
    }
}
```

### 庫配置變數

| 變數名稱               | 預設值                                  | 說明         |
| ---------------------- | --------------------------------------- | ------------ |
| `OrderReliableVersion` | "v36"                                   | 庫版本號     |
| `OrderReliable_Fname`  | "OrderReliable (Function Name Not Set)" | 函數名稱     |
| `ErrorLevel`           | 1                                       | 錯誤日誌等級 |

## 模組依賴關係

### 依賴層次結構

```
Level 1 (核心控制層)
    ↓
Level 2 (業務邏輯層)
    ↓
Level 3 (服務層)
    ↓
Level 4 (基礎設施層)
```

### 詳細依賴關係

#### 核心控制模組依賴

- **OnTick()** → 所有其他模組
- **OnInit()** → 輔助功能模組
- **sqInitStart()** → 輔助功能模組

#### 交易策略模組依賴

- **MACD 背離檢測** → 技術指標模組
- **sqOpenOrder()** → OrderReliable 庫模組
- **信號確認** → 風險管理模組

#### 訂單管理模組依賴

- **sqManageOrders()** → 風險管理模組
- **sqClosePositionAtMarket()** → OrderReliable 庫模組
- **sqSelectOrder()** → 輔助功能模組

#### 風險管理模組依賴

- **止損止盈管理** → 輔助功能模組
- **時間控制** → 輔助功能模組
- **移動止損** → OrderReliable 庫模組

#### 技術指標模組依賴

- **所有指標函數** → 輔助功能模組（數值處理）

#### OrderReliable 庫依賴

- **無外部依賴**（基礎設施層）

### 數據流向圖

```
市場數據 → 技術指標模組 → 交易策略模組 → 訂單管理模組 → OrderReliable 庫 → 經紀商
    ↓           ↓              ↓              ↓
輔助功能模組 ← 風險管理模組 ← 核心控制模組 ← 全域變數管理
```

## 函數調用頻率分析

### 極高頻率函數（每個 Tick）

| 函數名稱           | 調用頻率  | 性能影響 | 優化建議          |
| ------------------ | --------- | -------- | ----------------- |
| `OnTick()`         | 每個 Tick | 極高     | 避免重複計算      |
| `sqInitStart()`    | 每個 Tick | 中等     | 快取計算結果      |
| `sqManageOrders()` | 每個 Tick | 中等     | 僅在新 K 線時執行 |

### 高頻率函數（新 K 線時）

| 函數名稱               | 調用頻率 | 性能影響 | 優化建議     |
| ---------------------- | -------- | -------- | ------------ |
| `sqMACD()`             | 新 K 線  | 中等     | 使用內建函數 |
| `sqIndicatorHighest()` | 新 K 線  | 中等     | 限制檢測週期 |
| `sqIndicatorLowest()`  | 新 K 線  | 中等     | 限制檢測週期 |

### 中頻率函數（信號觸發時）

| 函數名稱                    | 調用頻率 | 性能影響 | 優化建議     |
| --------------------------- | -------- | -------- | ------------ |
| `sqOpenOrder()`             | 信號觸發 | 高       | 確保網路穩定 |
| `sqClosePositionAtMarket()` | 退出信號 | 高       | 使用可靠庫   |
| `sqSetSLandPT()`            | 開倉後   | 中等     | 批量設定     |

### 低頻率函數（初始化或特殊事件）

| 函數名稱                   | 調用頻率 | 性能影響 | 優化建議 |
| -------------------------- | -------- | -------- | -------- |
| `OnInit()`                 | 啟動時   | 低       | 完整驗證 |
| `sqHandleTradingOptions()` | 定期檢查 | 低       | 快取結果 |

### 性能優化建議

#### 1. 減少重複計算

```mql4
// 不好的做法
for(int i = 0; i < OrdersTotal(); i++) {
    if(OrderSelect(i, SELECT_BY_POS)) {
        double macd = sqMACD(NULL, 0, 25, 50, 5, PRICE_CLOSE, 0, 1); // 重複計算
    }
}

// 好的做法
double macd = sqMACD(NULL, 0, 25, 50, 5, PRICE_CLOSE, 0, 1); // 計算一次
for(int i = 0; i < OrdersTotal(); i++) {
    if(OrderSelect(i, SELECT_BY_POS)) {
        // 使用已計算的 macd 值
    }
}
```

#### 2. 條件短路優化

```mql4
// 將最可能為 false 的條件放在前面
if(_sqIsBarOpen && sqIsBarOpen() && 複雜的背離檢測條件) {
    // 交易邏輯
}
```

#### 3. 記憶體管理

```mql4
// 及時釋放不需要的物件
if(SQTime != NULL) {
    delete SQTime;
    SQTime = NULL;
}
```

## 最佳實踐建議

### 1. 模組使用原則

- **單一職責**: 每個模組只負責特定功能
- **低耦合**: 模組間依賴關係清晰簡單
- **高內聚**: 模組內部功能緊密相關

### 2. 錯誤處理策略

- **分層處理**: 不同層級採用不同錯誤處理策略
- **日誌記錄**: 關鍵操作必須記錄日誌
- **優雅降級**: 非關鍵功能失敗不影響核心功能

### 3. 參數配置建議

- **保守起步**: 新手使用保守參數設定
- **逐步調整**: 根據回測結果逐步優化
- **風險控制**: 始終保持適當的風險控制

### 4. 監控和維護

- **定期檢查**: 監控 EA 運行狀態
- **日誌分析**: 定期分析交易日誌
- **參數調整**: 根據市場變化調整參數

---

**文檔版本**: 1.0
**最後更新**: 2024 年
**適用版本**: SQ.mq4 (OrderReliable v36)
**維護者**: EA 開發團隊

#property strict

#include "../mql4-lib/History/Trigger.mqh"

//+------------------------------------------------------------------+
//| 設計思路                                                         |
//| Trigger：當MACD快線上穿/下穿慢線時，產生多/空信號                |
//+------------------------------------------------------------------+
//
// Pseudocode:
// 定義 MACDTrigger，繼承 TriggerAdapter，根據快線與慢線的交叉判斷 isLong()/isShort()
// 利用 MQL4 內建 iMACD() 函數取得快線與慢線的值，並比較其大小以判斷交叉方向
//
// 多頭信號 (isLong): 當前快線 > 當前慢線 且 前一根快線 <= 前一根慢線
// 空頭信號 (isShort): 當前快線 < 當前慢線 且 前一根快線 >= 前一根慢線
//

//+------------------------------------------------------------------+
//| MACDTrigger 類別                                                 |
//| 實作 MACD 快線與慢線交叉觸發器                                    |
//| 繼承自 TriggerAdapter，提供多空信號判斷功能                       |
//+------------------------------------------------------------------+
class MACDTrigger : public TriggerAdapter
{
    //+------------------------------------------------------------------+
    //| 靜態常數定義 - 所有硬編碼數值集中管理                             |
    //+------------------------------------------------------------------+
private:
    // 參數驗證常數
    static const int      CURRENT_BAR_SHIFT;            // 當前 K 線位移
    static const int      PREVIOUS_BAR_SHIFT;           // 前一根 K 線位移

    // 字串常數
    static const string   EMPTY_STRING;                 // 空字串常數

private:
    // MACD 參數設定
    string            m_symbol;          // 交易品種
    ENUM_TIMEFRAMES   m_timeframe;       // 時間週期
    int               m_fastEMA;         // 快速 EMA 週期
    int               m_slowEMA;         // 慢速 EMA 週期
    int               m_signalSMA;       // 信號線 SMA 週期
    int               m_appliedPrice;    // 應用價格類型

public:
    // 建構函數與解構函數
    MACDTrigger(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                int fastEMA = 12, int slowEMA = 26,
                int signalSMA = 9, int appliedPrice = PRICE_CLOSE);
    virtual          ~MACDTrigger();

    // 覆寫 TriggerAdapter 方法
    virtual bool      isLong() const override;
    virtual bool      isShort() const override;

    // 參數存取方法
    string            GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES   GetTimeframe() const { return m_timeframe; }
    int               GetFastEMA() const { return m_fastEMA; }
    int               GetSlowEMA() const { return m_slowEMA; }
    int               GetSignalSMA() const { return m_signalSMA; }
};

//+------------------------------------------------------------------+
//| 靜態常數定義 - 所有硬編碼數值的實際定義                           |
//+------------------------------------------------------------------+

// 參數驗證常數定義
static const int MACDTrigger::CURRENT_BAR_SHIFT = 0;
static const int MACDTrigger::PREVIOUS_BAR_SHIFT = 1;

// 靜態字串常數定義
static const string MACDTrigger::EMPTY_STRING = "";

//+------------------------------------------------------------------+
//| 建構函數                                                         |
//| 初始化 MACD 觸發器參數並進行驗證                                  |
//+------------------------------------------------------------------+
MACDTrigger::MACDTrigger(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                        int fastEMA = 12, int slowEMA = 26,
                        int signalSMA = 9, int appliedPrice = PRICE_CLOSE)
{
    // 設定交易品種，如果為空則使用當前品種
    m_symbol = (symbol == EMPTY_STRING) ? Symbol() : symbol;
    m_timeframe = timeframe;
    m_fastEMA = fastEMA;
    m_slowEMA = slowEMA;
    m_signalSMA = signalSMA;
    m_appliedPrice = appliedPrice;
}

//+------------------------------------------------------------------+
//| 解構函數                                                         |
//| 清理資源                                                         |
//+------------------------------------------------------------------+
MACDTrigger::~MACDTrigger()
{
    // 清理工作（如果需要）
}

//+------------------------------------------------------------------+
//| 判斷多頭信號                                                     |
//| 檢測 MACD 快線上穿慢線的情況                                      |
//+------------------------------------------------------------------+
bool MACDTrigger::isLong() const
{
    // 取得當前和前一根的 MACD 值
    double currentMain = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, CURRENT_BAR_SHIFT);
    double currentSignal = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, CURRENT_BAR_SHIFT);
    double previousMain = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, PREVIOUS_BAR_SHIFT);
    double previousSignal = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, PREVIOUS_BAR_SHIFT);

    // 多頭信號：前一根快線 <= 慢線，當前快線 > 慢線（上穿）
    return (previousMain <= previousSignal && currentMain > currentSignal);
}

//+------------------------------------------------------------------+
//| 判斷空頭信號                                                     |
//| 檢測 MACD 快線下穿慢線的情況                                      |
//+------------------------------------------------------------------+
bool MACDTrigger::isShort() const
{
    // 取得當前和前一根的 MACD 值
    double currentMain = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, CURRENT_BAR_SHIFT);
    double currentSignal = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, CURRENT_BAR_SHIFT);
    double previousMain = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, PREVIOUS_BAR_SHIFT);
    double previousSignal = iMACD(m_symbol, m_timeframe, m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, PREVIOUS_BAR_SHIFT);

    // 空頭信號：前一根快線 >= 慢線，當前快線 < 慢線（下穿）
    return (previousMain >= previousSignal && currentMain < currentSignal);
}

# Contributing to EA_Wizard-dev2

## Development Guidelines

### Code Structure

- Follow the EA_Wizard framework directory structure
- Place initialization code in `src/OnInit/`
- Place tick processing code in `src/OnTick/`
- Place cleanup code in `src/OnDeinit/`
- Place configuration code in `src/Config/`

### Framework Integration

- Use MainPipeline or TradingController as primary headers
- Follow EA_Wizard framework patterns and conventions
- Maintain loose coupling between components

### File Organization

- Keep related functionality in single `.mqh` files per directory
- Use descriptive file names
- Include appropriate header comments

### Code Quality

- Write clean, readable code
- Include appropriate error handling
- Add meaningful comments for complex logic
- Follow MQL4/MQL5 coding standards

### Testing

- Test all functionality thoroughly
- Verify compatibility with EA_Wizard framework
- Ensure proper resource cleanup

## Submission Process

1. Follow the established directory structure
2. Ensure code compiles without errors
3. Test functionality in MetaTrader environment
4. Document any new features or changes

## Questions

For questions about the EA_Wizard framework, refer to the framework documentation.

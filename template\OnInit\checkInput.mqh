#property strict

#include "../../../mql4_module/EA_Wizard/MainPipeline.mqh"
#include "../Config/Input.mqh"

// 檢查輸入參數階段
class CheckInput : public MainPipeline
{
public:
    CheckInput() : MainPipeline(INIT_PARAMETERS, "CheckInput"){}
    void Main() override { // 實現 Main 方法
        
        if(!Register((long)MagicNumber, "MagicNumber"))
            SetResult(false, "魔術數字註冊失敗");
        if(!Register((double)Lots, "Lots"))
            SetResult(false, "交易手數註冊失敗");
        if(!Register((double)StopLoss, "StopLoss"))
            SetResult(false, "止損點數註冊失敗");
        if(!Register((double)TakeProfit, "TakeProfit"))
            SetResult(false, "止盈點數註冊失敗");

        SetResult(true, "輸入參數檢查成功");
    }
}check_input_stage;

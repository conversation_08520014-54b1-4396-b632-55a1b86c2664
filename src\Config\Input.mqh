//+------------------------------------------------------------------+
//|                                                        Input.mqh |
//|                                            EA_Wizard Framework  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef INPUT_MQH
#define INPUT_MQH

//+------------------------------------------------------------------+
//| Input Parameter Management System                                |
//+------------------------------------------------------------------+
//| This file contains the input parameter management system for the |
//| EA_Wizard framework. It defines all external input parameters   |
//| that can be configured by users through the EA properties.      |
//|                                                                  |
//| Purpose:                                                         |
//| - Define external input parameters with default values          |
//| - Provide parameter descriptions and constraints                 |
//| - Handle input parameter validation and processing              |
//|                                                                  |
//| Expected Parameters:                                             |
//| - Magic Number: Unique identifier for EA trades                 |
//| - Spread Control: Maximum allowed spread for trading            |
//| - Slippage Tolerance: Maximum slippage allowed for orders       |
//| - Risk Management: Position sizing and risk parameters          |
//| - Trading Hours: Time-based trading restrictions                |
//|                                                                  |
//| Future Implementation:                                           |
//| - External input parameter declarations                         |
//| - Parameter validation functions                                |
//| - Input parameter grouping and organization                     |
//| - Dynamic parameter updates                                     |
//+------------------------------------------------------------------+

#include "Enum.mqh"

// TODO: Add external input parameter declarations
// TODO: Add parameter validation functions
// TODO: Add parameter grouping and organization

// Protection Level Settings
input ENUM_PROTECTION_LEVEL InpProtectionLevel = PROTECTION_MODERATE;  // Account Protection Level

input string InpAccountProtectionParams = "Account Protection Limits"; // Account Protection Limits (will be overridden by protection level if set to 0)
input double InpMaxLossPercent = 20.0;          // Maximum Loss Percentage (0 = use protection level default)
input double InpMaxDailyLoss = 0.0;             // Maximum Daily Loss Amount (0 = auto-calculate)
input double InpMaxDrawdownPercent = 30.0;      // Maximum Drawdown Percentage (0 = use protection level default)

// Position Limits
input string InpPositionLimits = "Position Limits"; // Position Limits (will be overridden by protection level if set to 0)
input int InpMaxOpenOrders = 20;                // Maximum Open Orders (0 = use protection level default)
input double InpMaxLotSize = 2.0;               // Maximum Lot Size per Trade (0 = use protection level default)
input double InpMaxTotalLotSize = 10.0;         // Maximum Total Lot Size (0 = use protection level default)
input double InpMaxSpread = 5.0;                // Maximum Allowed Spread (0 = use protection level default)

#endif // INPUT_MQH

1. 總覽與簡介 (Overview & Introduction)
1.1. 願景 (Vision)
打造一個具備嚴格且多層次風險「熔斷機制」的馬丁格爾EA。該EA允許交易者在預先設定的、絕對的虧損上限內，探索馬丁格爾策略的可能性，並在風險達到臨界點時自動化執行離場紀律。

1.2. 問題陳述 (Problem Statement)
傳統的馬丁格爾EA最大的問題在於其「失控」的風險敞口：

無限加倉: 在持續的單邊行情中，EA會不斷逆勢加倉，直到耗盡所有保證金。

虧損失控: 加倉的乘數效應會讓浮動虧損以指數級增長，迅速超出交易者的承受能力。

執行限制: 當手數大到一定程度，會觸及經紀商的最大手數限制，導致策略失效，留下一個巨大的虧損倉位。

心理崩潰: 交易者眼看浮虧巨大，難以做出手動砍倉的理性決策，最終導致災難性後果。

1.3. 目標與目的 (Goals & Objectives)
本模組旨在將上述「無限風險」變為「有限且已知的風險」。

限制加倉: 設定一個絕對的加倉層級上限。

定義最大虧損: 讓使用者清晰地知道，在最壞情況下，一個交易序列的最大可能虧損是多少。

自動化止損: 在觸及風險上限時，強制執行平倉，移除人為的情緒猶豫。

提供控制權: 賦予使用者對策略所有關鍵風險參數的完全控制權。

2. 功能性需求 (Functional Requirements)
FR-1: 馬丁格爾序列啟動控制 (Sequence Initiation Control)
FR-1.1: 初始訂單手數 (Initial Lot Size)

描述: 使用者可以設定馬丁格爾序列的第一張訂單的手數大小。這是所有後續計算的基礎。

驗收標準: 序列的第一張訂單手數必須等於 Initial_Lot。

FR-2: 加倉邏輯與限制模組 (Averaging Logic & Limits Module)
FR-2.1: 加倉距離 (Grid Step Distance)

描述: 使用者可以設定價格反向運行多少點 (Points) 後，EA 才執行下一次加倉。例如，做多後，價格下跌 300 點才加開第二張多單。

驗收標準: 只有當 |現價 - 上一張訂單開倉價| >= Grid_Step_Points 時，才允許觸發下一次加倉邏輯。

FR-2.2: 手數乘數 (Lot Multiplier)

描述: 使用者可以設定每次加倉時，手數相對於上一張訂單的乘數。這是馬丁格爾策略的核心風險參數。

驗收標準: 第 N 層訂單的手數 = (第 N-1 層訂單的手數) * Lot_Multiplier。手數計算結果需按經紀商規則進行標準化。

FR-2.3: 最大加倉層級 (Max Levels) -【核心風控】

描述: 此為最重要的安全閥。使用者必須設定一個整數，代表整個序列允許的最大訂單數量（包括初始訂單）。例如，設定為 6，則EA最多只會持有 6 張逆勢加倉的訂單。

驗收標準: 當持倉數量達到 Max_Levels 時，EA 必須立即停止任何新的加倉行為，無論價格如何變動。

FR-2.4: 整體盈利目標 (Overall Profit Target)

描述: 使用者可以設定一個以帳戶貨幣計的金額（例如 $10）。當整個訂單序列的總浮動盈虧（包含手續費和隔夜利息）達到此目標時，EA會平掉序列中的所有訂單。

驗收標準: 當 (所有序列訂單的總利潤) >= Overall_Profit_Target_Amount 時，所有訂單被成功平倉。

FR-3: 終極風險熔斷機制 (Ultimate Risk Circuit Breaker) -【核心風控】
FR-3.1: 到達最大層級後的處理機制 (Action on Max Levels Reached)

描述: 當加倉達到 Max_Levels 上限但整體仍處於虧損時，EA必須執行預設的最終處理方案。這是交易者預先做出的「最壞打算」。

可選操作:

立即平倉 (Close Sequence Immediately): (強烈建議) 承認本次序列失敗，立即平掉該序列所有訂單，接受虧損，然後等待新的初始信號。

等待回本 (Hold and Wait for Break-even): 停止加倉，但持有現有倉位，等待價格回調至整個序列的綜合成本價時自動平倉。（警告：此選項可能導致帳戶長時間被巨大浮虧套牢）

驗收標準: EA 能根據使用者選擇，在觸發 Max_Levels 時準確執行對應操作。

FR-3.2: 帳戶級別最大虧損保護 (Account-Level Equity Protection)

描述: 作為獨立於任何交易序列的最終保護層。使用者可以設定一個帳戶淨值 (Equity) 虧損的百分比。如果帳戶淨值低於 (初始淨值 * (1 - Equity_Stop_Percent/100))，EA將強制平掉所有倉位並永久停止運行（直到手動重啟）。

驗收標準: 當帳戶淨值觸及虧損線時，所有訂單被平倉，且EA在日誌中輸出警告並停止交易活動。

3. 用戶介面 - EA 輸入參數 (User Interface - EA Input Parameters)
C++

//--- Martingale Strategy Inputs ---
extern string  MG_Section_Title = "====== Martingale Settings (HIGH RISK) ======";

// FR-1 & FR-2: Sequence Settings
extern double  MG_Initial_Lot = 0.01;                 // 初始手數
extern int     MG_Grid_Step_Points = 300;             // 加倉間隔點數 (Points)
extern double  MG_Lot_Multiplier = 1.8;               // 手數乘數 (e.g., 1.6, 1.8, 2.0) - WARNING: >1.6 is very aggressive
extern double  MG_Overall_Profit_Target_Amount = 10.0;// 整體盈利目標 ($)

// FR-2 & FR-3: CORE RISK CONTROLS
extern string  MG_Risk_Control_Title = "====== CORE RISK CONTROLS ======";
extern int     MG_Max_Levels = 6;                     // !!! 最大加倉層級 (e.g., 5-8) - MOST IMPORTANT SETTING !!!
enum EnumActionOnMaxLevel {
    CLOSE_IMMEDIATELY,  // 到達上限立即平倉 (Recommended)
    WAIT_FOR_BREAKEVEN  // 等待回本 (High Risk)
};
extern EnumActionOnMaxLevel MG_Action_On_Max_Level = CLOSE_IMMEDIATELY; // 到達上限後的操作

// FR-3: Ultimate Circuit Breaker
extern string  MG_Account_Protection_Title = "====== Account Protection ======";
extern double  MG_Equity_Stop_Percent = 30.0;         // 帳戶淨值虧損保護 (%) (0 為不啟用)
4. 假設與依賴 (Assumptions & Dependencies)
假設: EA 的初始進場信號由外部邏輯決定。本 PRD 不定義「何時」開啟第一個倉位。

依賴: EA 必須能夠正確識別和管理由自己創建的訂單序列（通常透過 Magic Number 和訂單備註）。

依賴: 經紀商提供足夠的保證金和允許的手數範圍來執行策略（在限制範圍內）。
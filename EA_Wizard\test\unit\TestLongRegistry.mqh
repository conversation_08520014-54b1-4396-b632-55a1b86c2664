//+------------------------------------------------------------------+
//|                                               TestLongRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../LongRegistry.mqh"

//+------------------------------------------------------------------+
//| LongRegistry 單元測試類                                          |
//+------------------------------------------------------------------+
class TestLongRegistry : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestLongRegistry(TestRunner* runner = NULL)
        : TestCase("TestLongRegistry"), m_runner(runner) {}

    // 析構函數
    virtual ~TestLongRegistry() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 LongRegistry 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestLongSpecificOperations();
        TestArrayOperations();
        TestStatistics();
        TestInheritance();
        TestErrorHandling();
        TestCapacityAndLimits();

        Print("=== LongRegistry 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 LongRegistry 構造函數 ---");

        // 測試默認構造函數
        LongRegistry* registry1 = new LongRegistry();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 默認構造函數",
                registry1 != NULL,
                "默認構造函數創建成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 默認名稱",
                registry1.GetName() == "LongRegistry",
                "默認名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 默認類型",
                registry1.GetType() == "LongRegistry",
                "默認類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 默認最大註冊數",
                registry1.GetMaxRegistrations() == 50,
                "默認最大註冊數設置正確"
            ));
        }

        delete registry1;

        // 測試帶參數構造函數
        LongRegistry* registry2 = new LongRegistry("CustomLongRegistry", "CustomLongType", 20, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 自定義名稱",
                registry2.GetName() == "CustomLongRegistry",
                "自定義名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 自定義類型",
                registry2.GetType() == "CustomLongType",
                "自定義類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestConstructor - 自定義最大註冊數",
                registry2.GetMaxRegistrations() == 20,
                "自定義最大註冊數設置正確"
            ));
        }

        delete registry2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 LongRegistry 基本屬性 ---");

        LongRegistry* registry = new LongRegistry("PropTest", "PropType", 10, true);

        if(m_runner != NULL)
        {
            // 測試初始狀態
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestBasicProperties - 初始註冊數量",
                registry.GetRegisteredCount() == 0,
                "初始註冊數量為0"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestBasicProperties - 初始啟用狀態",
                registry.IsEnabled() == true,
                "初始啟用狀態為true"
            ));

            // 測試最後結果
            PipelineResult* result = registry.GetLastResult();
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestBasicProperties - 最後結果不為空",
                result != NULL,
                "最後結果對象存在"
            ));

            // 測試結果消息包含Long註冊器信息
            string resultMessage = result.GetMessage();
            bool hasLongInfo = StringFind(resultMessage, "Long") >= 0;
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestBasicProperties - 結果消息包含Long信息",
                hasLongInfo,
                "結果消息正確包含Long註冊器信息"
            ));
        }

        delete registry;
    }

    // 測試Long特定操作
    void TestLongSpecificOperations()
    {
        Print("--- 測試 LongRegistry Long特定操作 ---");

        LongRegistry* registry = new LongRegistry("LongTest", "LongType", 5, true);

        if(m_runner != NULL)
        {
            // 測試註冊Long值
            bool regResult1 = registry.Register("long1", 1234567890);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 註冊Long值",
                regResult1 == true,
                "Long值註冊成功"
            ));

            // 測試獲取Long值
            long value1 = registry.GetRegisteredValue("long1", -1);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 獲取Long值",
                value1 == 1234567890,
                "Long值獲取正確"
            ));

            // 測試更新Long值
            bool updateResult = registry.UpdateRegisteredValue("long1", 9876543210);
            long updatedValue = registry.GetRegisteredValue("long1", -1);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 更新Long值",
                updateResult && updatedValue == 9876543210,
                "Long值更新成功"
            ));

            // 測試負數Long值
            bool regResult2 = registry.Register("long2", -1234567890);
            long value2 = registry.GetRegisteredValue("long2", 0);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 負數Long值",
                regResult2 && value2 == -1234567890,
                "負數Long值處理正確"
            ));

            // 測試零值
            bool regResult3 = registry.Register("long3", 0);
            long value3 = registry.GetRegisteredValue("long3", -1);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 零值Long",
                regResult3 && value3 == 0,
                "零值Long處理正確"
            ));

            // 測試最大Long值
            bool regResult4 = registry.Register("long4", LONG_MAX);
            long value4 = registry.GetRegisteredValue("long4", 0);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestLongSpecificOperations - 最大Long值",
                regResult4 && value4 == LONG_MAX,
                "最大Long值處理正確"
            ));
        }

        delete registry;
    }

    // 測試數組操作
    void TestArrayOperations()
    {
        Print("--- 測試 LongRegistry 數組操作 ---");

        LongRegistry* registry = new LongRegistry("ArrayTest", "ArrayType", 10, true);

        if(m_runner != NULL)
        {
            // 註冊多個Long值
            registry.Register("array1", 100);
            registry.Register("array2", 200);
            registry.Register("array3", 300);

            // 測試獲取所有鍵
            string keys[];
            int keyCount = registry.GetAllKeys(keys);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestArrayOperations - 獲取所有鍵數量",
                keyCount == 3,
                "獲取所有鍵數量正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestArrayOperations - 鍵數組大小",
                ArraySize(keys) == 3,
                "鍵數組大小正確"
            ));

            // 測試獲取所有值
            long values[];
            int valueCount = registry.GetAllValues(values);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestArrayOperations - 獲取所有值數量",
                valueCount == 3,
                "獲取所有值數量正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestArrayOperations - 值數組大小",
                ArraySize(values) == 3,
                "值數組大小正確"
            ));

            // 驗證值的正確性（檢查是否包含預期值）
            bool hasValue100 = false, hasValue200 = false, hasValue300 = false;
            for(int i = 0; i < ArraySize(values); i++)
            {
                if(values[i] == 100) hasValue100 = true;
                if(values[i] == 200) hasValue200 = true;
                if(values[i] == 300) hasValue300 = true;
            }

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestArrayOperations - 值內容正確性",
                hasValue100 && hasValue200 && hasValue300,
                "所有註冊的值都正確獲取"
            ));
        }

        delete registry;
    }

    // 測試統計功能
    void TestStatistics()
    {
        Print("--- 測試 LongRegistry 統計功能 ---");

        LongRegistry* registry = new LongRegistry("StatTest", "StatType", 5, true);

        if(m_runner != NULL)
        {
            // 註冊一些值
            registry.Register("stat1", 1000);
            registry.Register("stat2", 2000);

            // 測試統計信息
            string stats = registry.GetStatistics();
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestStatistics - 統計信息不為空",
                StringLen(stats) > 0,
                "統計信息生成成功"
            ));

            // 檢查統計信息是否包含關鍵信息
            bool hasCount = StringFind(stats, "數量") >= 0 || StringFind(stats, "Count") >= 0;
            bool hasName = StringFind(stats, registry.GetName()) >= 0;

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestStatistics - 統計信息包含數量",
                hasCount,
                "統計信息包含數量信息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestStatistics - 統計信息包含名稱",
                hasName,
                "統計信息包含註冊器名稱"
            ));
        }

        delete registry;
    }

    // 測試繼承關係
    void TestInheritance()
    {
        Print("--- 測試 LongRegistry 繼承關係 ---");

        LongRegistry* registry = new LongRegistry("InheritTest", "InheritType", 5, true);

        if(m_runner != NULL)
        {
            // 測試基類方法可用性
            registry.Register("inherit1", 500);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestInheritance - 基類Register方法",
                registry.IsRegistered("inherit1") == true,
                "基類Register方法正常工作"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestInheritance - 基類GetRegisteredCount方法",
                registry.GetRegisteredCount() == 1,
                "基類GetRegisteredCount方法正常工作"
            ));

            // 測試基類屬性訪問
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestInheritance - 基類GetName方法",
                registry.GetName() == "InheritTest",
                "基類GetName方法正常工作"
            ));

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestInheritance - 基類GetType方法",
                registry.GetType() == "InheritType",
                "基類GetType方法正常工作"
            ));

            // 測試基類清理方法
            registry.Clear();
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestInheritance - 基類Clear方法",
                registry.GetRegisteredCount() == 0,
                "基類Clear方法正常工作"
            ));
        }

        delete registry;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("--- 測試 LongRegistry 錯誤處理 ---");

        LongRegistry* registry = new LongRegistry("ErrorTest", "ErrorType", 2, true);

        if(m_runner != NULL)
        {
            // 測試重複註冊
            registry.Register("duplicate", 100);
            bool duplicateResult = registry.Register("duplicate", 200);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 重複註冊",
                duplicateResult == false,
                "重複註冊正確失敗"
            ));

            // 測試更新不存在的鍵
            bool updateNonExistent = registry.UpdateRegisteredValue("nonexistent", 300);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 更新不存在的鍵",
                updateNonExistent == false,
                "更新不存在的鍵正確失敗"
            ));

            // 測試獲取不存在的鍵
            long defaultValue = -999;
            long nonExistentValue = registry.GetRegisteredValue("nonexistent", defaultValue);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 獲取不存在的鍵",
                nonExistentValue == defaultValue,
                "獲取不存在的鍵返回默認值"
            ));

            // 測試取消註冊不存在的鍵
            bool unregisterNonExistent = registry.Unregister("nonexistent");
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 取消註冊不存在的鍵",
                unregisterNonExistent == false,
                "取消註冊不存在的鍵正確失敗"
            ));

            // 測試禁用狀態下的操作
            registry.SetEnabled(false);
            bool disabledRegister = registry.Register("disabled", 400);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 禁用狀態註冊",
                disabledRegister == false,
                "禁用狀態下註冊正確失敗"
            ));

            bool disabledUpdate = registry.UpdateRegisteredValue("duplicate", 500);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestErrorHandling - 禁用狀態更新",
                disabledUpdate == false,
                "禁用狀態下更新正確失敗"
            ));
        }

        delete registry;
    }

    // 測試容量和限制
    void TestCapacityAndLimits()
    {
        Print("--- 測試 LongRegistry 容量和限制 ---");

        LongRegistry* registry = new LongRegistry("CapacityTest", "CapacityType", 3, true);

        if(m_runner != NULL)
        {
            // 註冊到最大容量
            bool reg1 = registry.Register("cap1", 1000);
            bool reg2 = registry.Register("cap2", 2000);
            bool reg3 = registry.Register("cap3", 3000);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 註冊到最大容量",
                reg1 && reg2 && reg3 && registry.GetRegisteredCount() == 3,
                "成功註冊到最大容量"
            ));

            // 測試超出容量
            bool reg4 = registry.Register("cap4", 4000);
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 超出容量註冊",
                reg4 == false,
                "超出容量時註冊正確失敗"
            ));

            // 測試容量滿時的狀態
            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 容量滿時數量",
                registry.GetRegisteredCount() == registry.GetMaxRegistrations(),
                "容量滿時數量等於最大容量"
            ));

            // 測試取消註冊後可以重新註冊
            bool unregResult = registry.Unregister("cap1");
            bool regAfterUnreg = registry.Register("cap4", 4000);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 取消註冊後重新註冊",
                unregResult && regAfterUnreg && registry.GetRegisteredCount() == 3,
                "取消註冊後可以重新註冊"
            ));

            // 測試清理後的容量恢復
            registry.Clear();
            bool regAfterClear = registry.Register("afterClear", 5000);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 清理後容量恢復",
                regAfterClear && registry.GetRegisteredCount() == 1,
                "清理後容量正確恢復"
            ));

            // 測試邊界值處理
            bool regMinLong = registry.Register("minLong", LONG_MIN);
            long retrievedMinLong = registry.GetRegisteredValue("minLong", 0);

            m_runner.RecordResult(new TestResult(
                "TestLongRegistry::TestCapacityAndLimits - 最小Long值處理",
                regMinLong && retrievedMinLong == LONG_MIN,
                "最小Long值處理正確"
            ));
        }

        delete registry;
    }
};

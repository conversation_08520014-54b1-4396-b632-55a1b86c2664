#property strict

#include "../mql4-lib/History/Trigger.mqh"

//+------------------------------------------------------------------+
//| 設計思路                                                         |
//| Trigger：當RSI穿越超買/超賣線時，產生多/空信號                    |
//+------------------------------------------------------------------+
//
// Pseudocode:
// 定義 RSITrigger，繼承 TriggerAdapter，根據RSI穿越超買/超賣線判斷 isLong()/isShort()
// 利用 MQL4 內建 iRSI() 函數取得RSI值，並比較其與超買/超賣線的關係以判斷穿越方向
//
// 多頭信號 (isLong): 當前RSI > 超賣線 且 前一根RSI <= 超賣線 (上穿超賣線)
// 空頭信號 (isShort): 當前RSI < 超買線 且 前一根RSI >= 超買線 (下穿超買線)
//

//+------------------------------------------------------------------+
//| RSITrigger 類別                                                 |
//| 實作 RSI 超買/超賣線穿越觸發器                                    |
//| 繼承自 TriggerAdapter，提供多空信號判斷功能                       |
//+------------------------------------------------------------------+
class RSITrigger : public TriggerAdapter
{
    //+------------------------------------------------------------------+
    //| 靜態常數定義 - 所有硬編碼數值集中管理                             |
    //+------------------------------------------------------------------+
private:
    // 參數驗證常數
    static const int      CURRENT_BAR_SHIFT;            // 當前 K 線位移
    static const int      PREVIOUS_BAR_SHIFT;           // 前一根 K 線位移

    // RSI 預設參數常數
    static const double   DEFAULT_OVERSOLD_LEVEL;       // 預設超賣線
    static const double   DEFAULT_OVERBOUGHT_LEVEL;     // 預設超買線

    // 字串常數
    static const string   EMPTY_STRING;                 // 空字串常數

private:
    // RSI 參數設定
    string            m_symbol;          // 交易品種
    ENUM_TIMEFRAMES   m_timeframe;       // 時間週期
    int               m_period;          // RSI 週期
    int               m_appliedPrice;    // 應用價格類型
    double            m_oversoldLevel;   // 超賣線水平
    double            m_overboughtLevel; // 超買線水平

public:
    // 建構函數與解構函數
    RSITrigger(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
               int period = 14, int appliedPrice = PRICE_CLOSE);
    virtual          ~RSITrigger();

    // 覆寫 TriggerAdapter 方法
    virtual bool      isLong() const override;
    virtual bool      isShort() const override;

    // 參數存取方法
    string            GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES   GetTimeframe() const { return m_timeframe; }
    int               GetPeriod() const { return m_period; }
    int               GetAppliedPrice() const { return m_appliedPrice; }
    double            GetOversoldLevel() const { return m_oversoldLevel; }
    double            GetOverboughtLevel() const { return m_overboughtLevel; }
};

//+------------------------------------------------------------------+
//| 靜態常數定義 - 所有硬編碼數值的實際定義                           |
//+------------------------------------------------------------------+

// 參數驗證常數定義
static const int RSITrigger::CURRENT_BAR_SHIFT = 0;
static const int RSITrigger::PREVIOUS_BAR_SHIFT = 1;

// RSI 預設參數常數定義
static const double RSITrigger::DEFAULT_OVERSOLD_LEVEL = 30.0;
static const double RSITrigger::DEFAULT_OVERBOUGHT_LEVEL = 70.0;

// 靜態字串常數定義
static const string RSITrigger::EMPTY_STRING = "";

//+------------------------------------------------------------------+
//| 建構函數                                                         |
//| 初始化 RSI 觸發器參數並進行驗證                                   |
//+------------------------------------------------------------------+
RSITrigger::RSITrigger(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                       int period = 14, int appliedPrice = PRICE_CLOSE)
{
    // 設定交易品種，如果為空則使用當前品種
    m_symbol = (symbol == EMPTY_STRING) ? Symbol() : symbol;
    m_timeframe = timeframe;
    m_period = period;
    m_appliedPrice = appliedPrice;
    m_oversoldLevel = DEFAULT_OVERSOLD_LEVEL;
    m_overboughtLevel = DEFAULT_OVERBOUGHT_LEVEL;
}

//+------------------------------------------------------------------+
//| 解構函數                                                         |
//| 清理資源                                                         |
//+------------------------------------------------------------------+
RSITrigger::~RSITrigger()
{
    // 清理工作（如果需要）
}

//+------------------------------------------------------------------+
//| 判斷多頭信號                                                     |
//| 檢測 RSI 上穿超賣線的情況                                         |
//+------------------------------------------------------------------+
bool RSITrigger::isLong() const
{
    // 取得當前和前一根的 RSI 值
    double currentRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, CURRENT_BAR_SHIFT);
    double previousRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, PREVIOUS_BAR_SHIFT);

    // 多頭信號：前一根RSI <= 超賣線，當前RSI > 超賣線（上穿超賣線）
    return (previousRSI <= m_oversoldLevel && currentRSI > m_oversoldLevel);
}

//+------------------------------------------------------------------+
//| 判斷空頭信號                                                     |
//| 檢測 RSI 下穿超買線的情況                                         |
//+------------------------------------------------------------------+
bool RSITrigger::isShort() const
{
    // 取得當前和前一根的 RSI 值
    double currentRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, CURRENT_BAR_SHIFT);
    double previousRSI = iRSI(m_symbol, m_timeframe, m_period, m_appliedPrice, PREVIOUS_BAR_SHIFT);

    // 空頭信號：前一根RSI >= 超買線，當前RSI < 超買線（下穿超買線）
    return (previousRSI >= m_overboughtLevel && currentRSI < m_overboughtLevel);
}

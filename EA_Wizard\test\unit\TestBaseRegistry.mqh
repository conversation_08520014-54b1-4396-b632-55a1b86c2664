//+------------------------------------------------------------------+
//|                                              TestBaseRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../base/BaseRegistry.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體實現類                                               |
//+------------------------------------------------------------------+
class TestBaseRegistryImpl : public BaseRegistry<string, int>
{
public:
    TestBaseRegistryImpl(string name = "TestRegistry",
                        string type = "TestType",
                        int maxRegistrations = 10,
                        bool owned = true)
        : BaseRegistry<string, int>(name, type, maxRegistrations, owned) {}

    virtual ~TestBaseRegistryImpl() {}

    // 公開受保護的方法用於測試
    bool TestValidateKey(string key)
    {
        return ValidateKey(key);
    }

    void TestUpdateResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        UpdateResult(success, message, errorLevel);
    }

    bool TestIsRegistryAvailable()
    {
        return IsRegistryAvailable();
    }

    bool TestCheckMaxRegistrations()
    {
        return CheckMaxRegistrations();
    }

    // 公開 GetLastResult 方法用於測試
    PipelineResult* GetLastResult()
    {
        return BaseRegistry<string, int>::GetLastResult();
    }
};

//+------------------------------------------------------------------+
//| BaseRegistry 單元測試類                                         |
//+------------------------------------------------------------------+
class TestBaseRegistry : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestBaseRegistry(TestRunner* runner = NULL)
        : TestCase("TestBaseRegistry"), m_runner(runner) {}

    // 析構函數
    virtual ~TestBaseRegistry() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 BaseRegistry 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestRegisterAndRetrieve();
        TestUpdateAndUnregister();
        TestValidation();
        TestErrorHandling();
        TestCapacityLimits();
        TestStateManagement();
        TestRegisteredDetailFunctionality();
        TestRegistrationWithDescription();

        Print("=== BaseRegistry 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 BaseRegistry 構造函數 ---");

        // 測試默認構造函數
        TestBaseRegistryImpl* registry1 = new TestBaseRegistryImpl();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 默認構造函數",
                registry1 != NULL,
                "默認構造函數創建成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 默認名稱",
                registry1.GetName() == "TestRegistry",
                "默認名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 默認類型",
                registry1.GetType() == "TestType",
                "默認類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 默認最大註冊數",
                registry1.GetMaxRegistrations() == 10,
                "默認最大註冊數設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 默認啟用狀態",
                registry1.IsEnabled() == true,
                "默認啟用狀態正確"
            ));
        }

        delete registry1;

        // 測試帶參數構造函數
        TestBaseRegistryImpl* registry2 = new TestBaseRegistryImpl("CustomRegistry", "CustomType", 20, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 自定義名稱",
                registry2.GetName() == "CustomRegistry",
                "自定義名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 自定義類型",
                registry2.GetType() == "CustomType",
                "自定義類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestConstructor - 自定義最大註冊數",
                registry2.GetMaxRegistrations() == 20,
                "自定義最大註冊數設置正確"
            ));
        }

        delete registry2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 BaseRegistry 基本屬性 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("PropTest", "PropType", 15, true);

        if(m_runner != NULL)
        {
            // 測試初始狀態
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestBasicProperties - 初始註冊數量",
                registry.GetRegisteredCount() == 0,
                "初始註冊數量為0"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestBasicProperties - 初始啟用狀態",
                registry.IsEnabled() == true,
                "初始啟用狀態為true"
            ));

            // 測試啟用/禁用
            registry.SetEnabled(false);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestBasicProperties - 禁用狀態",
                registry.IsEnabled() == false,
                "禁用狀態設置正確"
            ));

            registry.SetEnabled(true);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestBasicProperties - 重新啟用",
                registry.IsEnabled() == true,
                "重新啟用狀態設置正確"
            ));

            // 測試最後結果
            PipelineResult* result = registry.GetLastResult();
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestBasicProperties - 最後結果不為空",
                result != NULL,
                "最後結果對象存在"
            ));
        }

        delete registry;
    }

    // 測試註冊和獲取
    void TestRegisterAndRetrieve()
    {
        Print("--- 測試 BaseRegistry 註冊和獲取 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("RegTest", "RegType", 5, true);

        if(m_runner != NULL)
        {
            // 測試註冊
            bool regResult1 = registry.Register("key1", 100);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 註冊成功",
                regResult1 == true,
                "第一個項目註冊成功"
            ));

            // 測試註冊數量
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 註冊數量更新",
                registry.GetRegisteredCount() == 1,
                "註冊數量正確更新"
            ));

            // 測試獲取
            int value1 = registry.GetRegisteredValue("key1", -1);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 獲取值",
                value1 == 100,
                "獲取的值正確"
            ));

            // 測試是否已註冊
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 檢查已註冊",
                registry.IsRegistered("key1") == true,
                "正確檢測到已註冊的鍵"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 檢查未註冊",
                registry.IsRegistered("key2") == false,
                "正確檢測到未註冊的鍵"
            ));

            // 測試重複註冊
            bool regResult2 = registry.Register("key1", 200);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 重複註冊失敗",
                regResult2 == false,
                "重複註冊正確失敗"
            ));

            // 測試獲取不存在的鍵
            int value2 = registry.GetRegisteredValue("nonexistent", -999);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisterAndRetrieve - 獲取不存在的鍵",
                value2 == -999,
                "獲取不存在的鍵返回默認值"
            ));
        }

        delete registry;
    }

    // 測試更新和取消註冊
    void TestUpdateAndUnregister()
    {
        Print("--- 測試 BaseRegistry 更新和取消註冊 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("UpdateTest", "UpdateType", 5, true);

        if(m_runner != NULL)
        {
            // 先註冊一個項目
            registry.Register("updateKey", 300);

            // 測試更新
            bool updateResult = registry.UpdateRegisteredValue("updateKey", 400);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 更新成功",
                updateResult == true,
                "更新已註冊項目成功"
            ));

            // 驗證更新後的值
            int updatedValue = registry.GetRegisteredValue("updateKey", -1);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 更新後的值",
                updatedValue == 400,
                "更新後的值正確"
            ));

            // 測試更新不存在的鍵
            bool updateResult2 = registry.UpdateRegisteredValue("nonexistent", 500);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 更新不存在的鍵",
                updateResult2 == false,
                "更新不存在的鍵正確失敗"
            ));

            // 測試取消註冊
            bool unregResult = registry.Unregister("updateKey");
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 取消註冊成功",
                unregResult == true,
                "取消註冊成功"
            ));

            // 驗證取消註冊後的狀態
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 取消註冊後數量",
                registry.GetRegisteredCount() == 0,
                "取消註冊後數量正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 取消註冊後檢查",
                registry.IsRegistered("updateKey") == false,
                "取消註冊後檢查狀態正確"
            ));

            // 測試取消註冊不存在的鍵
            bool unregResult2 = registry.Unregister("nonexistent");
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestUpdateAndUnregister - 取消註冊不存在的鍵",
                unregResult2 == false,
                "取消註冊不存在的鍵正確失敗"
            ));
        }

        delete registry;
    }

    // 測試驗證功能
    void TestValidation()
    {
        Print("--- 測試 BaseRegistry 驗證功能 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("ValidTest", "ValidType", 5, true);

        if(m_runner != NULL)
        {
            // 測試鍵驗證（基類默認返回true）
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestValidation - 鍵驗證",
                registry.TestValidateKey("validKey") == true,
                "基類鍵驗證默認返回true"
            ));

            // 測試註冊器可用性檢查
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestValidation - 註冊器可用性",
                registry.TestIsRegistryAvailable() == true,
                "啟用狀態下註冊器可用"
            ));

            // 禁用註冊器後測試
            registry.SetEnabled(false);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestValidation - 禁用後不可用",
                registry.TestIsRegistryAvailable() == false,
                "禁用狀態下註冊器不可用"
            ));

            // 重新啟用
            registry.SetEnabled(true);

            // 測試最大註冊數檢查
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestValidation - 最大註冊數檢查",
                registry.TestCheckMaxRegistrations() == true,
                "未達到最大數量時檢查通過"
            ));
        }

        delete registry;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("--- 測試 BaseRegistry 錯誤處理 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("ErrorTest", "ErrorType", 2, true);

        if(m_runner != NULL)
        {
            // 測試結果更新
            registry.TestUpdateResult(true, "測試成功消息", ERROR_LEVEL_INFO);
            PipelineResult* result1 = registry.GetLastResult();

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestErrorHandling - 成功結果",
                result1 != NULL && result1.IsSuccess() == true,
                "成功結果記錄正確"
            ));

            registry.TestUpdateResult(false, "測試錯誤消息", ERROR_LEVEL_ERROR);
            PipelineResult* result2 = registry.GetLastResult();

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestErrorHandling - 錯誤結果",
                result2 != NULL && result2.IsSuccess() == false,
                "錯誤結果記錄正確"
            ));

            // 測試禁用狀態下的操作
            registry.SetEnabled(false);
            bool regResult = registry.Register("disabledKey", 100);

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestErrorHandling - 禁用狀態註冊",
                regResult == false,
                "禁用狀態下註冊正確失敗"
            ));

            bool updateResult = registry.UpdateRegisteredValue("disabledKey", 200);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestErrorHandling - 禁用狀態更新",
                updateResult == false,
                "禁用狀態下更新正確失敗"
            ));
        }

        delete registry;
    }

    // 測試容量限制
    void TestCapacityLimits()
    {
        Print("--- 測試 BaseRegistry 容量限制 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("CapacityTest", "CapacityType", 2, true);

        if(m_runner != NULL)
        {
            // 註冊到最大容量
            bool reg1 = registry.Register("key1", 100);
            bool reg2 = registry.Register("key2", 200);

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestCapacityLimits - 註冊到最大容量",
                reg1 && reg2 && registry.GetRegisteredCount() == 2,
                "成功註冊到最大容量"
            ));

            // 測試超出容量
            bool reg3 = registry.Register("key3", 300);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestCapacityLimits - 超出容量註冊",
                reg3 == false,
                "超出容量時註冊正確失敗"
            ));

            // 測試容量檢查
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestCapacityLimits - 容量檢查",
                registry.TestCheckMaxRegistrations() == false,
                "達到最大容量時檢查正確返回false"
            ));

            // 清理後測試
            registry.Clear();
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestCapacityLimits - 清理後數量",
                registry.GetRegisteredCount() == 0,
                "清理後數量正確歸零"
            ));

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestCapacityLimits - 清理後容量檢查",
                registry.TestCheckMaxRegistrations() == true,
                "清理後容量檢查正確通過"
            ));
        }

        delete registry;
    }

    // 測試狀態管理
    void TestStateManagement()
    {
        Print("--- 測試 BaseRegistry 狀態管理 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("StateTest", "StateType", 5, true);

        if(m_runner != NULL)
        {
            // 註冊一些項目
            registry.Register("state1", 100);
            registry.Register("state2", 200);
            registry.Register("state3", 300);

            // 測試統計信息
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestStateManagement - 註冊數量統計",
                registry.GetRegisteredCount() == 3,
                "註冊數量統計正確"
            ));

            // 測試清理功能
            registry.Clear();
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestStateManagement - 清理後狀態",
                registry.GetRegisteredCount() == 0,
                "清理後狀態正確"
            ));

            // 測試清理後的結果消息
            PipelineResult* clearResult = registry.GetLastResult();
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestStateManagement - 清理結果",
                clearResult != NULL && clearResult.IsSuccess() == true,
                "清理操作結果正確"
            ));

            // 測試清理後重新註冊
            bool regAfterClear = registry.Register("afterClear", 999);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestStateManagement - 清理後重新註冊",
                regAfterClear == true && registry.GetRegisteredCount() == 1,
                "清理後可以重新註冊"
            ));
        }

        delete registry;
    }

    // 測試 RegisteredDetail 功能
    void TestRegisteredDetailFunctionality()
    {
        Print("--- 測試 BaseRegistry RegisteredDetail 功能 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("DetailTest", "DetailType", 5, true);

        if(m_runner != NULL)
        {
            // 註冊一個項目
            bool regResult = registry.Register("detailKey", 999);
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisteredDetailFunctionality - 註冊成功",
                regResult == true,
                "項目註冊成功"
            ));

            // 獲取 RegisteredDetail 對象
            RegisteredDetail<int>* detail = registry.GetRegisteredDetail("detailKey");

            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisteredDetailFunctionality - 詳細信息對象不為空",
                detail != NULL,
                "成功獲取詳細信息對象"
            ));

            if(detail != NULL)
            {
                // 測試詳細信息的有效性
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegisteredDetailFunctionality - 詳細信息有效",
                    detail.IsValid() == true,
                    "詳細信息對象有效"
                ));

                // 測試值獲取
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegisteredDetailFunctionality - 值正確",
                    detail.GetValue() == 999,
                    "詳細信息中的值正確"
                ));

                // 測試來源
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegisteredDetailFunctionality - 來源正確",
                    detail.GetSource() == "DetailTest",
                    "詳細信息中的來源正確"
                ));

                // 測試訪問次數（第一次訪問應該是2，因為GetValue()會增加計數）
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegisteredDetailFunctionality - 訪問次數",
                    detail.GetAccessCount() >= 1,
                    "訪問次數正確記錄"
                ));

                // 測試註冊時間
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegisteredDetailFunctionality - 註冊時間",
                    detail.GetRegistrationTime() > 0,
                    "註冊時間正確記錄"
                ));

                delete detail;
            }

            // 測試獲取不存在項目的詳細信息
            RegisteredDetail<int>* invalidDetail = registry.GetRegisteredDetail("nonexistent");
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegisteredDetailFunctionality - 不存在項目的詳細信息",
                invalidDetail != NULL && !invalidDetail.IsValid(),
                "不存在項目返回無效的詳細信息對象"
            ));

            if(invalidDetail != NULL)
            {
                delete invalidDetail;
            }
        }

        delete registry;
    }

    // 測試帶描述的註冊功能
    void TestRegistrationWithDescription()
    {
        Print("--- 測試 BaseRegistry 帶描述註冊功能 ---");

        TestBaseRegistryImpl* registry = new TestBaseRegistryImpl("DescTest", "DescType", 5, true);

        if(m_runner != NULL)
        {
            // 測試帶描述的註冊
            bool regResult = registry.Register("descKey", 777, "測試描述信息");
            m_runner.RecordResult(new TestResult(
                "TestBaseRegistry::TestRegistrationWithDescription - 帶描述註冊成功",
                regResult == true,
                "帶描述的項目註冊成功"
            ));

            // 獲取詳細信息並檢查描述
            RegisteredDetail<int>* detail = registry.GetRegisteredDetail("descKey");

            if(detail != NULL && detail.IsValid())
            {
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegistrationWithDescription - 描述正確",
                    detail.GetDescription() == "測試描述信息",
                    "描述信息正確保存"
                ));

                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegistrationWithDescription - 值正確",
                    detail.GetValue() == 777,
                    "帶描述註冊的值正確"
                ));

                delete detail;
            }
            else
            {
                m_runner.RecordResult(new TestResult(
                    "TestBaseRegistry::TestRegistrationWithDescription - 詳細信息獲取失敗",
                    false,
                    "無法獲取帶描述項目的詳細信息"
                ));
            }
        }

        delete registry;
    }
};

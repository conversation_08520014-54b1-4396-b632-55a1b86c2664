# Module Directory Structure

## Overview

The `module/` directory serves as a centralized location for reusable components and modules within the EA_Wizard framework. This directory contains modular components that can be shared across different Expert Advisors and trading systems.

## Directory Structure

```
module/
├── README.md                 # This documentation file
├── Indicators/              # Technical indicator modules
│   ├── BollingerBands/      # Bollinger Bands implementation
│   ├── MACD/                # MACD indicator implementation
│   ├── RSI/                 # RSI indicator implementation
│   └── Common/              # Shared indicator utilities
├── RiskManagement/          # Risk management modules
│   ├── PositionSizing/      # Position sizing algorithms
│   ├── StopLoss/            # Stop loss management
│   ├── TakeProfit/          # Take profit management
│   └── AccountProtection/   # Account protection systems
├── Trading/                 # Trading logic modules
│   ├── Martingale/          # Martingale position scaling
│   ├── SignalGeneration/    # Signal generation systems
│   ├── OrderManagement/     # Order execution and management
│   └── PositionTracking/    # Position tracking utilities
├── Utils/                   # Utility modules
│   ├── Logging/             # Logging utilities
│   ├── Validation/          # Parameter validation
│   ├── ErrorHandling/       # Error handling systems
│   └── Configuration/       # Configuration management
└── Base/                    # Base classes and interfaces
    ├── BaseComponent.mqh    # Base component class
    ├── BaseIndicator.mqh    # Base indicator interface
    └── BaseStrategy.mqh     # Base trading strategy interface
```

## Module Organization Principles

### 1. Single Responsibility
Each module should have a single, well-defined responsibility and should not depend on other modules unless absolutely necessary.

### 2. Reusability
Modules are designed to be reusable across different Expert Advisors and trading strategies within the EA_Wizard framework.

### 3. Independence
Each module should be self-contained with its own parameters, constants, and implementation logic.

### 4. OOP Design
All modules follow Object-Oriented Programming principles with proper encapsulation, inheritance, and polymorphism where appropriate.

## Module Categories

### Indicators
Contains technical indicator implementations that extend or utilize MQL4's built-in indicator functions:
- **BollingerBands/**: Bollinger Bands calculation and signal generation
- **MACD/**: MACD indicator with trend and momentum analysis
- **RSI/**: RSI implementation with overbought/oversold detection
- **Common/**: Shared utilities for indicator calculations

### Risk Management
Implements various risk management strategies and controls:
- **PositionSizing/**: Algorithms for calculating appropriate position sizes
- **StopLoss/**: Dynamic and static stop loss management
- **TakeProfit/**: Take profit calculation and management
- **AccountProtection/**: Account-level protection mechanisms

### Trading
Core trading logic and execution modules:
- **Martingale/**: Martingale position scaling implementation
- **SignalGeneration/**: Multi-indicator signal generation systems
- **OrderManagement/**: Order placement, modification, and closure
- **PositionTracking/**: Position monitoring and tracking utilities

### Utils
General utility modules used across the framework:
- **Logging/**: Comprehensive logging system
- **Validation/**: Input parameter validation
- **ErrorHandling/**: Error detection and handling mechanisms
- **Configuration/**: Configuration file management

### Base
Base classes and interfaces that other modules inherit from:
- **BaseComponent.mqh**: Common functionality for all components
- **BaseIndicator.mqh**: Interface for indicator implementations
- **BaseStrategy.mqh**: Base class for trading strategies

## Usage Guidelines

### Including Modules
To use a module in your EA, include the appropriate header file:

```mql4
#include "module/Indicators/BollingerBands/BollingerBands.mqh"
#include "module/RiskManagement/PositionSizing/FixedLot.mqh"
```

### Module Naming Convention
- Directory names use PascalCase (e.g., `BollingerBands`, `RiskManagement`)
- File names match their class names with `.mqh` extension
- Class names use PascalCase and descriptive naming

### Documentation Requirements
Each module should include:
- Header comments explaining purpose and usage
- Parameter documentation
- Method documentation with examples
- Dependencies and requirements

## Integration with EA_Wizard Framework

The module directory integrates seamlessly with the EA_Wizard framework structure:
- Modules can be referenced from `src/OnInit/`, `src/OnTick/`, and `src/OnDeinit/`
- Configuration parameters can be managed through `src/Config/`
- Modules maintain independence while supporting framework integration

## Development Standards

### Code Quality
- Follow MQL4 coding standards and best practices
- Implement proper error handling and validation
- Use meaningful variable and function names
- Include comprehensive comments and documentation

### Testing
- Each module should be testable independently
- Include unit tests where applicable
- Validate functionality with historical data
- Test error conditions and edge cases

### Performance
- Optimize for real-time trading performance
- Minimize memory allocation and deallocation
- Cache frequently used calculations
- Profile critical code paths

## Contributing

When adding new modules:
1. Follow the established directory structure
2. Implement proper OOP design patterns
3. Include comprehensive documentation
4. Test thoroughly before integration
5. Update this README.md with new module information

## License

This module directory is part of the EA_Wizard framework and follows the same licensing terms as the main project.

# EA_Wizard 交易自動化框架

##主要路徑

- EA_Wizard 項目根目錄:
  C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard

- 開發目標路徑:
  C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\EA_Wizard\src

EA_Wizard 框架路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\mql4_module\EA_Wizard

mql4-lib 庫路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Projects\mql4_module\mql4-lib

MQL4 標準 Include 路徑:
C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\1DAFD9A7C67DC84FE37EAA1FC1E5CF75\MQL4\Include

## 2. 核心功能需求

### 2.1 Martingale 策略核心

- **手數序列**: 固定 4 層 [0.01, 0.01, 0.02, 0.04]
- **加單觸發**: 前一筆訂單虧損 300 點時觸發下一層
- **最大層數**: 4 層（超過停止加單）
- **止損設定**: 無止損 (StopLoss = 0)
- **獲利計算**: 300 點/每 0.01 手數比例計算止賺

### 2.2 技術指標信號系統

- **Bollinger Bands**: 判斷超買超賣區域
- **MACD**: 確認趨勢方向和動量
- **RSI**: 輔助判斷超買超賣狀態
- **信號邏輯**: 三指標同時超買 → 賣出，超賣 → 買入

### 2.3 風險控制系統

- **最大虧損**: 帳戶餘額 20%限制
- **最大持倉**: 20 筆訂單限制
- **單一貨幣對**: Symbol()交易
- **點差檢查**: 最大允許點差限制

### 2.4 交易控制機制

- **魔術數字**: EA 訂單識別
- **滑點控制**: 可接受滑點範圍
- **時間管理**: 服務器時間同步

### 2.5 監控通知系統

- **交易警報**: 開倉/平倉通知
- **風險警報**: 最大虧損/持倉警報
- **策略警報**: Martingale 層數變化
- **指標警報**: 技術指標信號變化

## 3. 技術架構需求

### 3.2 標準化目錄結構

```
EA_Wizard/src/ 目錄標準化結構:

src/
├── OnInit/                     # 初始化相關Pipeline類
│   ├── index.mqh              # OnInit目錄統一入口點
│   └── *filename.mqh            # *mqh 文件
├── OnTick/                     # OnTick處理相關Pipeline類
│   ├── index.mqh              # OnTick目錄統一入口點
│   └── *filename.mqh            # *mqh 文件
├── OnDeinit/                   # 清理和資源釋放相關Pipeline類
│   ├── index.mqh              # OnDeinit目錄統一入口點
│   └── *filename.mqh            # *mqh 文件
├── Config/                     # 配置相關文件
│   ├── index.mqh              # Config目錄統一入口點
│   ├── Confit.mqh             # 配置初始化Pipeline類
│   └── Input.mqh              # 輸入參數配置類
└── StandardEA.mqh             # 主EA文件

目錄結構規範:
1. 每個目錄內必須有 index.mqh 作為統一入口點
2. index.mqh 負責 #include 該目錄內的所有相關 .mqh 文件
3. 索引文件按執行順序或依賴關係組織include語句
4. StandardEA.mqh 只引用四個索引文件和TradingController
```

## 4. 詳細功能規格

### 4.1 Martingale 策略實現

```
層級配置:
- 第1層: 0.01手, 虧損300點觸發第2層
- 第2層: 0.01手, 虧損300點觸發第3層
- 第3層: 0.02手, 虧損300點觸發第4層
- 第4層: 0.04手, 最終層級

止賺計算:
- 0.01手 → 300點止賺
- 0.02手 → 600點止賺
- 0.04手 → 1200點止賺
```

### 4.2 技術指標參數

```
Bollinger Bands:
- 週期: 20
- 標準差: 2.0
- 應用價格: PRICE_CLOSE

MACD:
- 快速EMA: 12
- 慢速EMA: 26
- 信號線: 9
- 應用價格: PRICE_CLOSE

RSI:
- 週期: 14
- 超買閾值: 70
- 超賣閾值: 30
- 應用價格: PRICE_CLOSE
```

### 4.3 信號邏輯定義

```
買入信號條件:
- RSI < 30 (超賣)
- 價格觸及Bollinger下軌
- MACD線上穿信號線且在零軸下方

賣出信號條件:
- RSI > 70 (超買)
- 價格觸及Bollinger上軌
- MACD線下穿信號線且在零軸上方
```

### 4.4 風險控制規則

```
帳戶保護:
- 最大虧損 = 帳戶餘額 × 20%
- 檢查頻率: 每次OnTick()
- 觸發動作: 停止開新倉，保留現有倉位

持倉限制:
- 最大同時持倉: 20筆訂單
- 檢查時機: 開倉前驗證
- 超限處理: 暫停交易信號

點差控制:
- 最大允許點差: 5點 (可配置)
- 檢查時機: 開倉前驗證
- 超限處理: 延遲交易直到點差正常
```

## 5. 用戶界面需求

### 5.1 輸入參數

```
基礎設定:
- MagicNumber: 魔術數字 (預設: 12345)
- MaxSpread: 最大點差 (預設: 5)
- Slippage: 滑點容忍 (預設: 3)

Martingale設定:
- MartingaleLevels: 層數 (固定: 4)
- LossPoints: 加單觸發點數 (固定: 300)
- LotSequence: 手數序列 (固定: [0.01,0.01,0.02,0.04])

風險設定:
- MaxLossPercent: 最大虧損百分比 (預設: 20)
- MaxOrders: 最大持倉數 (預設: 20)

指標設定:
- BB_Period: BB週期 (預設: 20)
- BB_Deviation: BB標準差 (預設: 2.0)
- MACD_Fast: MACD快線 (預設: 12)
- MACD_Slow: MACD慢線 (預設: 26)
- MACD_Signal: MACD信號線 (預設: 9)
- RSI_Period: RSI週期 (預設: 14)
- RSI_Overbought: RSI超買 (預設: 70)
- RSI_Oversold: RSI超賣 (預設: 30)

```

```
##專案要求
-嚴謹遵循 EA_Wizard 框架內的README.md 文件指引進行開發
-EA_Wizard/src/ 目錄標準化結構中,建議開發專案的代碼分別在OnInit/, OnTick/, OnDeinit/, Config/ 目錄下,避免和其他專案的代碼混淆
-在OnInit/, OnTick/, OnDeinit/ 目錄下,建議相關的代碼壓縮在一個 mqh 文件,避免和其他mqh 文件混淆
-因應開發需求,可在mqh 文件建立模組
```

#property strict

#include "../../../../mql4_module/mql4-lib/Collection/HashMap.mqh"
#include "../../../../mql4_module/mql4-lib/Collection/OrderedIntMap.mqh"

//+------------------------------------------------------------------+
//| 錯誤描述符結構                                                    |
//+------------------------------------------------------------------+
struct EAErrorDescriptor
{
    int         errorCode;      // 錯誤代碼
    string      errorMessage;   // 錯誤訊息
};

//+------------------------------------------------------------------+
//| EAErrorStorage 類別                                              |
//| 用於存儲和管理錯誤資訊的容器類別                                   |
//| 遵循 EA_Wizard 框架的編碼慣例和錯誤處理模式                      |
//+------------------------------------------------------------------+
class EAErrorStorage
{
private:
    // 錯誤存儲相關成員變數
    HashMap<int, string>    m_errorMap;         // 錯誤映射表
    EAErrorDescriptor       m_errors[];         // 錯誤描述符陣列
    OrderedIntMap           m_errorCodeCounts;  // 錯誤代碼計數器

    // 狀態管理相關成員變數
    bool                    m_hasOverlap;       // 重疊標誌

    //+------------------------------------------------------------------+
    //| 建構子和解構子                                                   |
    //+------------------------------------------------------------------+

public:
                        EAErrorStorage();
    virtual            ~EAErrorStorage();

    //+------------------------------------------------------------------+
    //| 錯誤添加方法                                                     |
    //+------------------------------------------------------------------+

public:
    void                AppendError(int errorCode, string errorDescription);
    void                AppendError(const EAErrorDescriptor& errors[]);

    //+------------------------------------------------------------------+
    //| 錯誤查詢方法                                                     |
    //+------------------------------------------------------------------+

public:
    string              GetErrorDescription(int errorCode) const;
    bool                ContainsError(int errorCode) const;


    //+------------------------------------------------------------------+
    //| 重疊檢查方法                                                     |
    //+------------------------------------------------------------------+

public:
    int                 OverlapCodes(int& overlapCodes[]) const;
    bool                IsOverlap() { return m_hasOverlap; }


};

//+------------------------------------------------------------------+
//| EAErrorStorage 方法實作                                         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 建構子 - 初始化所有成員變數                                      |
//+------------------------------------------------------------------+
EAErrorStorage::EAErrorStorage()
{
    m_hasOverlap = false;

    // 清空錯誤陣列
    ArrayResize(m_errors, 0);
}

//+------------------------------------------------------------------+
//| 解構子 - 執行清理操作                                            |
//+------------------------------------------------------------------+
EAErrorStorage::~EAErrorStorage()
{
    // 清理錯誤陣列
    ArrayFree(m_errors);
}

//+------------------------------------------------------------------+
//| 錯誤添加方法實作                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 添加單個錯誤 - 將錯誤代碼和描述添加到存儲中                       |
//+------------------------------------------------------------------+
void EAErrorStorage::AppendError(int errorCode, string errorDescription)
{
    // 檢查錯誤代碼是否已存在
    if (m_errorMap.contains(errorCode))
    {
        // 更新計數器
        m_errorCodeCounts.increment(errorCode);
        m_hasOverlap = true;
        return;
    }

    // 添加到錯誤映射表
    m_errorMap.set(errorCode, errorDescription);

    // 添加到錯誤陣列
    int size = ArraySize(m_errors);
    ArrayResize(m_errors, size + 1);
    m_errors[size].errorCode = errorCode;
    m_errors[size].errorMessage = errorDescription;

    // 更新計數器
    m_errorCodeCounts.increment(errorCode);
}

//+------------------------------------------------------------------+
//| 添加錯誤陣列 - 批量添加錯誤描述符                                 |
//+------------------------------------------------------------------+
void EAErrorStorage::AppendError(const EAErrorDescriptor& errors[])
{
    int errorCount = ArraySize(errors);

    // 逐個添加錯誤
    for (int i = 0; i < errorCount; i++)
    {
        AppendError(errors[i].errorCode, errors[i].errorMessage);
    }
}



//+------------------------------------------------------------------+
//| 錯誤查詢方法實作                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 獲取錯誤描述 - 根據錯誤代碼返回對應的錯誤描述                     |
//+------------------------------------------------------------------+
string EAErrorStorage::GetErrorDescription(int errorCode) const
{
    // 檢查錯誤代碼是否存在
    if (m_errorMap.contains(errorCode))
    {
        return m_errorMap[errorCode];
    }

    // 返回預設錯誤訊息
    return StringFormat("未知錯誤代碼: %d", errorCode);
}

//+------------------------------------------------------------------+
//| 檢查錯誤是否存在 - 判斷指定錯誤代碼是否已存儲                     |
//+------------------------------------------------------------------+
bool EAErrorStorage::ContainsError(int errorCode) const
{
    return m_errorMap.contains(errorCode);
}



//+------------------------------------------------------------------+
//| 重疊檢查方法實作                                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 提取重疊錯誤代碼 - 返回所有在計數器中出現重疊的錯誤代碼           |
//+------------------------------------------------------------------+
int EAErrorStorage::OverlapCodes(int& overlapCodes[]) const
{
    // 清空輸出陣列
    ArrayResize(overlapCodes, 0);

    int overlapCount = 0;
    int totalCodes = m_errorCodeCounts.size();

    // 遍歷所有錯誤代碼計數器
    for (int i = 0; i < totalCodes; i++)
    {
        int errorCode = m_errorCodeCounts.key(i);
        int count = m_errorCodeCounts.value(i);

        // 檢查是否有重疊（計數大於 1）
        if (count > 1)
        {
            // 擴展輸出陣列並添加重疊的錯誤代碼
            ArrayResize(overlapCodes, overlapCount + 1);
            overlapCodes[overlapCount] = errorCode;
            overlapCount++;
        }
    }

    // 對重疊錯誤代碼陣列進行升序排序
    if (overlapCount > 1)
    {
        // 使用 MQL4 內建的 ArraySort 函數進行升序排列
        ArraySort(overlapCodes, overlapCount, 0, MODE_ASCEND);
    }

    return overlapCount;
}




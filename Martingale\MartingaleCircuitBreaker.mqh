//+------------------------------------------------------------------+
//|                                       MartingaleCircuitBreaker.mqh |
//|                                      馬丁格爾熔斷器類別            |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MARTINGALE_CIRCUIT_BREAKER_MQH
#define MARTINGALE_CIRCUIT_BREAKER_MQH

#property strict

//+------------------------------------------------------------------+
//| 馬丁格爾風險管理器枚舉定義                                        |
//+------------------------------------------------------------------+
enum ENUM_MARTINGALE_ACTION
{
    MARTINGALE_CLOSE_IMMEDIATELY,    // 到達上限立即平倉 (建議)
    MARTINGALE_WAIT_FOR_BREAKEVEN    // 等待回本 (高風險)
};

//+------------------------------------------------------------------+
//| 馬丁格爾熔斷器類別                                                |
//| 負責熔斷機制和緊急停止 (單一責任原則)                             |
//+------------------------------------------------------------------+
class MartingaleCircuitBreaker
{
private:
    // 熔斷機制參數
    bool                    m_emergencyStop;        // 緊急停止狀態
    ENUM_MARTINGALE_ACTION  m_actionOnMaxLevel;     // 到達上限後的操作
    
public:
    //+------------------------------------------------------------------+
    //| 建構子                                                           |
    //+------------------------------------------------------------------+
                            MartingaleCircuitBreaker();
    
    //+------------------------------------------------------------------+
    //| 配置方法                                                         |
    //+------------------------------------------------------------------+
    void                    SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action);
    
    //+------------------------------------------------------------------+
    //| 熔斷檢查方法                                                     |
    //+------------------------------------------------------------------+
    bool                    CheckEmergencyStop() const { return m_emergencyStop; }
    void                    ActivateEmergencyStop();
    bool                    ShouldExecuteCircuitBreaker(bool maxLevelReached, bool equityProtectionTriggered);
    
    //+------------------------------------------------------------------+
    //| 查詢方法                                                         |
    //+------------------------------------------------------------------+
    bool                    IsEmergencyStop() const { return m_emergencyStop; }
    ENUM_MARTINGALE_ACTION  GetActionOnMaxLevel() const { return m_actionOnMaxLevel; }
    string                  GetActionDescription() const;
    
    //+------------------------------------------------------------------+
    //| 重置方法                                                         |
    //+------------------------------------------------------------------+
    void                    Reset();
};

//+------------------------------------------------------------------+
//| 建構子實作                                                       |
//+------------------------------------------------------------------+
MartingaleCircuitBreaker::MartingaleCircuitBreaker()
{
    m_emergencyStop = false;
    m_actionOnMaxLevel = MARTINGALE_CLOSE_IMMEDIATELY; // 預設立即平倉
    
    Print("[MartingaleCircuitBreaker] 熔斷器初始化完成 - 預設操作: 立即平倉");
}

//+------------------------------------------------------------------+
//| 設定到達上限後的操作                                             |
//+------------------------------------------------------------------+
void MartingaleCircuitBreaker::SetActionOnMaxLevel(ENUM_MARTINGALE_ACTION action)
{
    m_actionOnMaxLevel = action;
    string actionStr = GetActionDescription();
    Print("[MartingaleCircuitBreaker] 到達上限操作設定為: ", actionStr);
}

//+------------------------------------------------------------------+
//| 啟動緊急停止                                                     |
//+------------------------------------------------------------------+
void MartingaleCircuitBreaker::ActivateEmergencyStop()
{
    if (!m_emergencyStop)
    {
        m_emergencyStop = true;
        Print("[MartingaleCircuitBreaker] *** 緊急停止已啟動 ***");
        Print("  所有交易活動將被暫停");
        Print("  請檢查帳戶狀況並手動重置");
    }
}

//+------------------------------------------------------------------+
//| 檢查是否應執行熔斷機制                                           |
//+------------------------------------------------------------------+
bool MartingaleCircuitBreaker::ShouldExecuteCircuitBreaker(bool maxLevelReached, bool equityProtectionTriggered)
{
    // 檢查緊急停止狀態
    if (m_emergencyStop)
    {
        Print("[MartingaleCircuitBreaker] 熔斷檢查: 緊急停止狀態已啟動");
        return true;
    }
    
    // 檢查是否達到最大層級限制
    if (maxLevelReached)
    {
        Print("[MartingaleCircuitBreaker] 熔斷檢查: 達到最大層級限制");
        Print("  將執行操作: ", GetActionDescription());
        return true;
    }
    
    // 檢查是否觸發淨值保護
    if (equityProtectionTriggered)
    {
        Print("[MartingaleCircuitBreaker] 熔斷檢查: 觸發淨值保護機制");
        Print("  強制執行緊急平倉");
        ActivateEmergencyStop(); // 自動啟動緊急停止
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 取得操作描述                                                     |
//+------------------------------------------------------------------+
string MartingaleCircuitBreaker::GetActionDescription() const
{
    switch(m_actionOnMaxLevel)
    {
        case MARTINGALE_CLOSE_IMMEDIATELY:
            return "立即平倉 (建議)";
        case MARTINGALE_WAIT_FOR_BREAKEVEN:
            return "等待回本 (高風險)";
        default:
            return "未知操作";
    }
}

//+------------------------------------------------------------------+
//| 重置熔斷器                                                       |
//+------------------------------------------------------------------+
void MartingaleCircuitBreaker::Reset()
{
    bool wasEmergencyStop = m_emergencyStop;
    m_emergencyStop = false;
    
    if (wasEmergencyStop)
    {
        Print("[MartingaleCircuitBreaker] 緊急停止狀態已解除");
    }
    Print("[MartingaleCircuitBreaker] 熔斷器已重置");
}

#endif // MARTINGALE_CIRCUIT_BREAKER_MQH

//+------------------------------------------------------------------+
//|                                        TradingParameterModels.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef TRADING_PARAMETER_MODELS_MQH
#define TRADING_PARAMETER_MODELS_MQH

//+------------------------------------------------------------------+
//| Trading Parameter Model Classes                                  |
//| Immutable data containers for trading operation parameters      |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| OpenInfo Class                                                   |
//| Immutable data container for market order parameters            |
//+------------------------------------------------------------------+
class OpenInfo
{
private:
    double            m_lots;           // Lot size
    double            m_stoploss;       // Stop loss price
    double            m_takeprofit;     // Take profit price
    string            m_comment;        // Order comment

public:
    //--- Constructor
    OpenInfo(double lots = 0.0, double stoploss = 0.0, double takeprofit = 0.0, string comment = NULL)
        : m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}

    //--- Destructor
    ~OpenInfo() {}

    //--- Getter methods (immutable - no setters)
    double          GetLots() const { return m_lots; }
    double          GetStopLoss() const { return m_stoploss; }
    double          GetTakeProfit() const { return m_takeprofit; }
    string          GetComment() const { return m_comment; }

    //--- Utility methods
    string          ToString() const
    {
        return StringFormat("Lots: %g, SL: %g, TP: %g, Comment: %s",
                           m_lots, m_stoploss, m_takeprofit, m_comment);
    }
};

//+------------------------------------------------------------------+
//| PendInfo Class                                                   |
//| Immutable data container for pending order parameters           |
//+------------------------------------------------------------------+
class PendInfo
{
private:
    double            m_price;          // Order price
    double            m_lots;           // Lot size
    double            m_stoploss;       // Stop loss price
    double            m_takeprofit;     // Take profit price
    string            m_comment;        // Order comment

public:
    //--- Constructor
    PendInfo(double price = 0.0, double lots = 0.0, double stoploss = 0.0, double takeprofit = 0.0, string comment = NULL)
        : m_price(price), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}

    //--- Destructor
    ~PendInfo() {}

    //--- Getter methods (immutable - no setters)
    double          GetPrice() const { return m_price; }
    double          GetLots() const { return m_lots; }
    double          GetStopLoss() const { return m_stoploss; }
    double          GetTakeProfit() const { return m_takeprofit; }
    string          GetComment() const { return m_comment; }

    //--- Utility methods
    string          ToString() const
    {
        return StringFormat("Price: %g, Lots: %g, SL: %g, TP: %g, Comment: %s",
                           m_price, m_lots, m_stoploss, m_takeprofit, m_comment);
    }
};

//+------------------------------------------------------------------+
//| ModifyInfo Class                                                 |
//| Immutable data container for order modification parameters      |
//+------------------------------------------------------------------+
class ModifyInfo
{
private:
    int               m_ticket;         // Order ticket
    double            m_price;          // New price (for pending orders)
    double            m_stoploss;       // New stop loss price
    double            m_takeprofit;     // New take profit price
    datetime          m_expiration;     // Expiration time (for pending orders)

public:
    //--- Constructor
    ModifyInfo(int ticket = 0, double price = 0.0, double stoploss = 0.0, double takeprofit = 0.0, datetime expiration = 0)
        : m_ticket(ticket), m_price(price), m_stoploss(stoploss), m_takeprofit(takeprofit), m_expiration(expiration) {}

    //--- Destructor
    ~ModifyInfo() {}

    //--- Getter methods (immutable - no setters)
    int             GetTicket() const { return m_ticket; }
    double          GetPrice() const { return m_price; }
    double          GetStopLoss() const { return m_stoploss; }
    double          GetTakeProfit() const { return m_takeprofit; }
    datetime        GetExpiration() const { return m_expiration; }

    //--- Utility methods
    string          ToString() const
    {
        return StringFormat("Ticket: %d, Price: %g, SL: %g, TP: %g, Expiration: %s",
                           m_ticket, m_price, m_stoploss, m_takeprofit, TimeToString(m_expiration));
    }
};

#endif // TRADING_PARAMETER_MODELS_MQH

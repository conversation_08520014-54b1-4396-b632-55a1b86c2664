# SQ Expert Advisor 技術文檔

[![MQL4](https://img.shields.io/badge/MQL4-Expert%20Advisor-blue.svg)](https://www.mql5.com)
[![Strategy](https://img.shields.io/badge/Strategy-MACD%20Divergence-green.svg)](https://github.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 目錄

- [功能概述](#功能概述)
- [技術規格](#技術規格)
- [參數配置](#參數配置)
- [使用方法](#使用方法)
- [程式架構](#程式架構)
- [範例設定](#範例設定)
- [注意事項](#注意事項)

## 功能概述

SQ Expert Advisor 是一個基於 MACD 指標背離檢測的自動交易系統。該 EA 專門設計用於識別價格與 MACD 指標之間的背離現象，並在檢測到背離信號時自動執行交易操作。

### 核心交易策略

- **背離檢測**：當 MACD 指標創新高而價格創新低時產生買入信號
- **趨勢跟隨**：利用 MACD 指標的動量特性捕捉市場趨勢轉換點
- **風險控制**：內建多層風險管理機制，包括止損、止盈和時間控制

### 主要特色

- ✅ 基於成熟的 MACD 背離策略
- ✅ 完整的風險管理系統
- ✅ 多種訂單退出機制
- ✅ 靈活的交易時間控制
- ✅ 高可靠性的訂單執行系統
- ✅ 詳細的交易日誌記錄

## 技術規格

### 支援的交易環境

| 項目 | 規格 |
|------|------|
| **平台** | MetaTrader 4 |
| **交易對** | 所有外匯對、CFD |
| **時間框架** | 所有時間框架（建議 H1 以上）|
| **最小資金** | $1000 USD |
| **建議槓桿** | 1:100 - 1:500 |

### 技術指標配置

#### MACD 指標設定
- **快線週期**：25（可調整）
- **慢線週期**：50（可調整）
- **信號線平滑**：5（可調整）
- **應用價格**：收盤價

#### 背離檢測參數
- **檢測週期**：5根K線（可調整）
- **信號確認**：新K線開盤時觸發

### 風險管理機制

#### 止損止盈系統
- **固定止損**：50點（可調整）
- **固定止盈**：50點（可調整）
- **移動止損**：40點後移至盈虧平衡點

#### 時間控制機制
- **交易時間限制**：可設定特定時間範圍
- **週末交易控制**：可禁用週末交易
- **日終退出**：可設定每日強制平倉時間
- **週五退出**：可設定週五提前平倉

#### 訂單管理邏輯
- **最大滑點控制**：入場5點，平倉無限制
- **訂單到期管理**：支援掛單到期設定
- **X根K線後退出**：25根K線後自動平倉
- **魔術數字識別**：10000（可調整）

## 參數配置

### 交易策略參數

```mql4
extern string CustomComment = "New strategy";     // 自定義註釋
extern int MagicNumber = 10000;                   // 魔術數字
extern int MACDFast = 25;                         // MACD 快線週期
extern int MACDSlow = 50;                         // MACD 慢線週期  
extern int MACDSmooth = 5;                        // MACD 平滑週期
extern int DivergencePeriod = 5;                  // 背離檢測週期
extern int ExitAfterBars = 25;                    // X根K線後退出
extern int MoveSL2BE = 40;                        // 移動止損到盈虧平衡點
extern int ProfitTarget = 50;                     // 止盈目標（點）
extern int StopLoss = 50;                         // 止損（點）
```

### 資金管理參數

```mql4
extern double mmLots = 0.1;                       // 固定交易手數
```

### 交易時間控制參數

```mql4
extern bool DontTradeOnWeekends = false;          // 週末不交易
extern string FridayCloseTime = "23:00";          // 週五關閉時間
extern string SundayOpenTime = "23:00";           // 週日開盤時間
extern bool ExitAtEndOfDay = false;               // 日終退出
extern string EODExitTime = "23:55";              // 日終退出時間
extern bool ExitOnFriday = false;                 // 週五退出
extern string FridayExitTime = "23:00";           // 週五退出時間
extern bool LimitTimeRange = false;               // 限制交易時間範圍
extern string SignalTimeRangeFrom = "08:00";      // 信號時間範圍起始
extern string SignalTimeRangeTo = "16:00";        // 信號時間範圍結束
```

### 技術設定參數

```mql4
extern bool UseSQTickSize = false;                // 使用自定義點值
extern double MainChartTickSizeSQ = 1.0E-4;       // 主圖表點值
extern int OpenBarDelay = 0;                      // 開盤延遲（分鐘）
```

## 使用方法

### 安裝步驟

1. **下載檔案**
   ```
   將 SQ.mq4 複製到 MetaTrader 4 的 Experts 資料夾
   路徑：MT4安裝目錄/MQL4/Experts/
   ```

2. **編譯程式**
   ```
   在 MetaEditor 中開啟 SQ.mq4
   按 F7 或點擊編譯按鈕
   確認無編譯錯誤
   ```

3. **載入圖表**
   ```
   在 MT4 中開啟目標交易對圖表
   從導航器拖拽 SQ EA 到圖表上
   設定相關參數後點擊確定
   ```

### 建議的市場條件

#### 適用市場環境
- **趨勢市場**：中等波動性的趨勢行情
- **時間框架**：H1、H4、D1 效果較佳
- **交易對選擇**：主要貨幣對（EUR/USD、GBP/USD、USD/JPY 等）

#### 最佳配置建議
- **保守型**：手數 0.01，止損 100 點，止盈 100 點
- **平衡型**：手數 0.1，止損 50 點，止盈 50 點  
- **積極型**：手數 0.2，止損 30 點，止盈 80 點

### 注意事項和限制

⚠️ **重要提醒**

1. **回測限制**：背離策略在回測中可能表現與實盤不同
2. **滑點影響**：新聞時段可能出現較大滑點
3. **資金管理**：建議單筆交易風險不超過帳戶資金的 2%
4. **市場條件**：避免在重大新聞發布時段使用
5. **監控需求**：建議定期檢查 EA 運行狀態

## 程式架構

### 主要函數說明

#### 核心交易函數
- `OnTick()`：主要交易邏輯入口點，每個 tick 執行一次
- `sqInitStart()`：初始化函數，處理新K線檢測和基本設定
- `sqManageOrders()`：訂單管理函數，處理所有開倉訂單的管理

#### 訂單操作函數  
- `sqOpenOrder()`：開倉函數，執行買入或賣出操作
- `sqClosePositionAtMarket()`：市價平倉函數
- `sqSetSLandPT()`：設定止損和止盈
- `sqOrderModify()`：修改訂單參數

#### 技術指標函數
- `sqMACD()`：MACD 指標計算
- `sqMA()`：移動平均線計算  
- `sqIndicatorHighest()`：指標最高值檢測
- `sqIndicatorLowest()`：指標最低值檢測

#### 風險管理函數
- `sqManageSL2BE()`：移動止損到盈虧平衡點
- `sqManageTrailingStop()`：跟蹤止損管理
- `sqManageExitAfterXBars()`：X根K線後退出管理
- `sqManageOrderExpiration()`：訂單到期管理

### 類別架構

```mql4
SQ Expert Advisor
├── 交易策略模組
│   ├── MACD 背離檢測
│   ├── 信號確認機制
│   └── 入場條件判斷
├── 風險管理模組  
│   ├── 止損止盈設定
│   ├── 移動止損管理
│   └── 時間控制機制
├── 訂單管理模組
│   ├── OrderReliable 庫
│   ├── 滑點控制
│   └── 錯誤處理
└── 輔助功能模組
    ├── 技術指標計算
    ├── 日誌記錄
    └── 介面顯示
```

## 範例設定

### 保守型設定（適合新手）
```mql4
mmLots = 0.01                    // 小手數交易
StopLoss = 100                   // 較大止損
ProfitTarget = 100               // 較大止盈  
MoveSL2BE = 50                   // 較晚移動止損
ExitAfterBars = 50               // 較長持倉時間
DontTradeOnWeekends = true       // 避免週末風險
```

### 積極型設定（適合有經驗交易者）
```mql4
mmLots = 0.2                     // 較大手數
StopLoss = 30                    // 較小止損
ProfitTarget = 80                // 較大止盈比例
MoveSL2BE = 20                   // 較早移動止損  
ExitAfterBars = 15               // 較短持倉時間
LimitTimeRange = true            // 限制交易時間
SignalTimeRangeFrom = "08:00"    // 歐洲開盤
SignalTimeRangeTo = "16:00"      // 美國開盤前
```

### 日內交易設定
```mql4
ExitAtEndOfDay = true            // 日終強制平倉
EODExitTime = "23:55"            // 接近收盤時平倉
ExitOnFriday = true              // 週五提前平倉
FridayExitTime = "20:00"         // 週五晚間平倉
OpenBarDelay = 5                 // 延遲5分鐘開倉
```

---

**免責聲明**：本 EA 僅供教育和研究目的使用。外匯交易存在高風險，可能導致資金損失。使用前請充分了解風險並進行充分測試。

**技術支援**：如有問題請查閱 MT4 日誌或聯繫開發者。

**版本資訊**：當前版本基於 OrderReliable v36 庫開發，確保訂單執行的穩定性和可靠性。

# Risk Management Module

## Overview

The Risk Management module provides comprehensive tools for managing trading risk, including position sizing, stop loss management, take profit strategies, and account protection systems. All components follow the EA_Wizard framework patterns and integrate seamlessly with trading operations.

## Module Structure

```
RiskManagement/
├── README.md                           # This documentation file
├── PositionSizing/                     # Position sizing strategies
│   └── FixedLotSizing.mqh             # Fixed lot size implementation
├── StopLoss/                          # Stop loss management
│   └── DynamicStopLoss.mqh            # Dynamic stop loss strategies
├── TakeProfit/                        # Take profit management
│   └── DynamicTakeProfit.mqh          # Dynamic take profit strategies
└── AccountProtection/                 # Account protection systems
    └── AccountProtection.mqh          # Comprehensive account protection
```

## Components Overview

### 1. Position Sizing (`PositionSizing/`)

Controls the size of trading positions based on various risk management strategies.

#### FixedLotSizing (`FixedLotSizing.mqh`)

**Purpose**: Simple fixed lot size position sizing strategy

**Features**:

- Fixed lot size regardless of account balance or risk
- Market info validation (min/max lot, lot step)
- Lot size normalization
- Symbol-specific constraints

**Key Methods**:

- `SetFixedLotSize(lotSize)` - Set the fixed lot size
- `CalculatePositionSize()` - Returns the fixed lot size
- `NormalizeLotSize(lotSize)` - Normalize to valid increment
- `IsValidLotSize(lotSize)` - Validate lot size constraints

### 2. Stop Loss Management (`StopLoss/`)

Manages stop loss levels using various calculation methods.

#### DynamicStopLoss (`DynamicStopLoss.mqh`)

**Purpose**: Dynamic stop loss calculation and management

**Stop Loss Types**:

- `SL_NONE` - No stop loss
- `SL_FIXED_POINTS` - Fixed points from entry
- `SL_ATR_BASED` - ATR-based dynamic stop loss
- `SL_SUPPORT_RESISTANCE` - Based on support/resistance levels
- `SL_TRAILING` - Trailing stop loss

**Key Methods**:

- `CalculateStopLoss(orderType, entryPrice, shift)` - Calculate stop loss
- `CalculateTrailingStop(orderType, currentPrice, currentSL)` - Calculate trailing stop
- `ShouldUpdateTrailingStop()` - Check if trailing stop should update
- `IsValidStopLoss()` - Validate stop loss level
- `NormalizeStopLoss()` - Normalize to valid level

**Configuration**:

- `SetStopLossType(type)` - Set calculation method
- `SetFixedPoints(points)` - Set fixed points distance
- `SetATRParameters(period, multiplier)` - Configure ATR-based calculation
- `SetTrailingParameters(distance, step)` - Configure trailing stop

### 3. Take Profit Management (`TakeProfit/`)

Manages take profit levels and partial profit taking strategies.

#### DynamicTakeProfit (`DynamicTakeProfit.mqh`)

**Purpose**: Dynamic take profit calculation and management

**Take Profit Types**:

- `TP_NONE` - No take profit
- `TP_FIXED_POINTS` - Fixed points from entry
- `TP_RISK_REWARD` - Risk-reward ratio based
- `TP_ATR_BASED` - ATR-based dynamic take profit
- `TP_SUPPORT_RESISTANCE` - Based on support/resistance levels
- `TP_PARTIAL` - Partial take profit strategy

**Key Methods**:

- `CalculateTakeProfit(orderType, entryPrice, stopLoss, shift)` - Calculate take profit
- `ShouldTakePartialProfit(orderType, entryPrice, currentPrice)` - Check partial TP trigger
- `CalculatePartialLotSize(originalLotSize)` - Calculate partial position size
- `IsValidTakeProfit()` - Validate take profit level

**Configuration**:

- `SetTakeProfitType(type)` - Set calculation method
- `SetRiskRewardRatio(ratio)` - Set risk-reward ratio
- `SetATRParameters(period, multiplier)` - Configure ATR-based calculation
- `SetPartialParameters(percent, ratio)` - Configure partial TP

### 4. Account Protection (`AccountProtection/`)

Comprehensive account protection and risk monitoring system.

#### AccountProtection (`AccountProtection.mqh`)

**Purpose**: Monitor and protect account from excessive losses

**Protection Levels**:

- `PROTECTION_NONE` - No protection
- `PROTECTION_BASIC` - Basic protection limits
- `PROTECTION_MODERATE` - Moderate protection (default)
- `PROTECTION_STRICT` - Strict protection limits

**Protection Status**:

- `STATUS_NORMAL` - Normal operation
- `STATUS_WARNING` - Warning level reached
- `STATUS_CRITICAL` - Critical level reached
- `STATUS_EMERGENCY` - Emergency stop triggered

**Key Methods**:

- `IsTradingAllowed()` - Check if trading is allowed
- `IsNewOrderAllowed(lotSize)` - Check if new order is allowed
- `CheckAccountLimits()` - Validate account limits
- `CheckPositionLimits(lotSize)` - Validate position limits
- `HaltTrading(reason)` - Halt all trading
- `EmergencyStop(reason)` - Emergency stop with reason

**Monitoring Features**:

- Maximum loss percentage
- Maximum daily loss amount
- Maximum drawdown percentage
- Maximum open orders
- Maximum lot size per trade
- Maximum total lot size
- Maximum allowed spread

## Usage Examples

### Basic Position Sizing

```mql4
#include "module/RiskManagement/PositionSizing/FixedLotSizing.mqh"

// Create fixed lot sizing
FixedLotSizing* lotSizer = new FixedLotSizing(Symbol(), 0.1);
if (!lotSizer.Initialize()) {
    Print("Failed to initialize lot sizer");
    delete lotSizer;
    return;
}

// Calculate position size
double lotSize = lotSizer.CalculatePositionSize();
Print("Position size: ", DoubleToString(lotSize, 2));

// Cleanup
delete lotSizer;
```

### Dynamic Stop Loss

```mql4
#include "module/RiskManagement/StopLoss/DynamicStopLoss.mqh"

// Create dynamic stop loss
DynamicStopLoss* stopLoss = new DynamicStopLoss(Symbol(), Period(), SL_ATR_BASED);
stopLoss.SetATRParameters(14, 2.0); // 14-period ATR, 2.0 multiplier
if (!stopLoss.Initialize()) {
    Print("Failed to initialize stop loss");
    delete stopLoss;
    return;
}

// Calculate stop loss for buy order
double entryPrice = Ask;
double slLevel = stopLoss.CalculateStopLoss(OP_BUY, entryPrice, 0);
Print("Stop loss level: ", DoubleToString(slLevel, Digits));

// Check if valid
if (stopLoss.IsValidStopLoss(OP_BUY, entryPrice, slLevel)) {
    Print("Stop loss is valid");
} else {
    Print("Stop loss is invalid - adjusting...");
    slLevel = stopLoss.NormalizeStopLoss(OP_BUY, entryPrice, slLevel);
}

// Cleanup
delete stopLoss;
```

### Account Protection

```mql4
#include "module/RiskManagement/AccountProtection/AccountProtection.mqh"

// Create account protection with protection level (immutable pattern)
AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE);

// OR create with external config (all configuration set during construction)
AccountProtectionConfig* config = new AccountProtectionConfig(
    15.0,   // maxLossPercent - 15% maximum loss
    25.0,   // maxDrawdownPercent - 25% maximum drawdown
    7.5,    // maxDailyLossPercent - 7.5% maximum daily loss
    10,     // maxOpenOrders - Maximum 10 open orders
    1.0,    // maxLotSize - Maximum 1.0 lot per trade
    3.0,    // maxSpread - Maximum 3 points spread
    5.0     // maxTotalLotSize - Maximum 5.0 total lots
);
AccountProtection* customProtection = new AccountProtection(config);

if (!protection.Initialize()) {
    Print("Failed to initialize account protection");
    delete protection;
    return;
}

// Check before opening new trade
if (protection.IsTradingAllowed()) {
    double lotSize = 0.1;
    if (protection.IsNewOrderAllowed(lotSize)) {
        Print("New order allowed");
        // Proceed with order
    } else {
        Print("New order blocked by risk management");
    }
} else {
    Print("Trading is halted by account protection");
}

// Update protection status
protection.Update();
Print("Protection status: ", protection.GetStatusDescription());
Print("Current loss: ", DoubleToString(protection.GetCurrentLossPercent(), 2), "%");

// Cleanup
delete protection;
```

### Comprehensive Risk Management

```mql4
#include "module/RiskManagement/PositionSizing/FixedLotSizing.mqh"
#include "module/RiskManagement/StopLoss/DynamicStopLoss.mqh"
#include "module/RiskManagement/TakeProfit/DynamicTakeProfit.mqh"
#include "module/RiskManagement/AccountProtection/AccountProtection.mqh"

// Initialize all risk management components
FixedLotSizing* lotSizer = new FixedLotSizing(Symbol(), 0.1);
DynamicStopLoss* stopLoss = new DynamicStopLoss(Symbol(), Period(), SL_ATR_BASED);
DynamicTakeProfit* takeProfit = new DynamicTakeProfit(Symbol(), Period(), TP_RISK_REWARD);
AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE);

// Configure components
stopLoss.SetATRParameters(14, 2.0);
takeProfit.SetRiskRewardRatio(2.0);

// Initialize all
if (!lotSizer.Initialize() || !stopLoss.Initialize() ||
    !takeProfit.Initialize() || !protection.Initialize()) {
    Print("Failed to initialize risk management components");
    // Cleanup and return
}

// Complete trade setup with risk management
if (protection.IsTradingAllowed()) {
    double lotSize = lotSizer.CalculatePositionSize();

    if (protection.IsNewOrderAllowed(lotSize)) {
        double entryPrice = Ask;
        double slLevel = stopLoss.CalculateStopLoss(OP_BUY, entryPrice, 0);
        double tpLevel = takeProfit.CalculateTakeProfit(OP_BUY, entryPrice, slLevel, 0);

        Print("Trade Setup:");
        Print("Entry: ", DoubleToString(entryPrice, Digits));
        Print("Stop Loss: ", DoubleToString(slLevel, Digits));
        Print("Take Profit: ", DoubleToString(tpLevel, Digits));
        Print("Lot Size: ", DoubleToString(lotSize, 2));

        // Execute trade with calculated parameters
    }
}

// Cleanup
delete lotSizer;
delete stopLoss;
delete takeProfit;
delete protection;
```

## Integration with EA_Wizard Framework

Risk Management components integrate with EA_Wizard framework modules:

### OnInit Integration

```mql4
// In OnInit/RiskManagement.mqh
AccountProtection* g_accountProtection = new AccountProtection(PROTECTION_MODERATE);
DynamicStopLoss* g_stopLoss = new DynamicStopLoss(Symbol(), Period(), SL_ATR_BASED);

bool InitializeRiskManagement() {
    return g_accountProtection.Initialize() && g_stopLoss.Initialize();
}
```

### OnTick Integration

```mql4
// In OnTick/RiskManagement.mqh
void UpdateRiskManagement() {
    g_accountProtection.Update();

    if (!g_accountProtection.IsTradingAllowed()) {
        // Halt trading operations
        return;
    }

    // Update trailing stops
    for (int i = 0; i < OrdersTotal(); i++) {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            // Update trailing stop logic
        }
    }
}
```

## Best Practices

### 1. Account Protection

- Always use account protection in live trading
- Set conservative limits initially
- Monitor protection status regularly
- Implement emergency stop procedures

### 2. Position Sizing

- Never risk more than 1-2% per trade
- Consider account balance in sizing decisions
- Validate lot sizes against broker constraints
- Use consistent sizing methodology

### 3. Stop Loss Management

- Always use stop losses in live trading
- Adjust stop loss method to market conditions
- Consider volatility in stop loss calculation
- Implement trailing stops for profitable trades

### 4. Take Profit Strategy

- Use risk-reward ratios of at least 1:1.5
- Consider partial profit taking
- Adjust take profit based on market structure
- Monitor and update take profit levels

### 5. Integration

- Initialize all components during EA startup
- Update components on each tick
- Handle errors gracefully
- Log risk management decisions

## Contributing

When extending risk management components:

1. Follow the BaseComponent inheritance pattern
2. Implement proper error handling
3. Add comprehensive parameter validation
4. Include usage examples
5. Update documentation
6. Test thoroughly with different market conditions

## Dependencies

- `BaseComponent.mqh` - Base component class
- MQL4 Standard Library - Market info functions
- EA_Wizard Framework - Integration components

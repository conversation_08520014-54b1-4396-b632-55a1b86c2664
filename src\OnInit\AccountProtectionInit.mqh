//+------------------------------------------------------------------+
//|                                        AccountProtectionInit.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_INIT_MQH
#define ACCOUNT_PROTECTION_INIT_MQH

#property strict

// Include EA_Wizard framework components
#include "../../../mql4_module/EA_Wizard/MainPipeline.mqh"

// Include account protection module
#include "../../module/RiskManagement/AccountProtection/AccountProtection.mqh"

// Include configuration module
#include "../Config/index.mqh"

//+------------------------------------------------------------------+
//| Account Protection Initialization Pipeline                       |
//| Implements account protection system initialization during      |
//| EA startup following EA_Wizard framework guidelines             |
//+------------------------------------------------------------------+
class AccountProtectionInit : public MainPipeline
{
private:
    // Protection Level Settings
    static const ENUM_PROTECTION_LEVEL ProtectionLevel;  // Account Protection Level

    static const double MaxLossPercent;          // Maximum Loss Percentage (0 = use protection level default)
    static const double MaxDailyLoss;             // Maximum Daily Loss Amount (0 = auto-calculate)
    static const double MaxDrawdownPercent;      // Maximum Drawdown Percentage (0 = use protection level default)

    // Position Limits
    static const int MaxOpenOrders;                // Maximum Open Orders (0 = use protection level default)
    static const double MaxLotSize;               // Maximum Lot Size per Trade (0 = use protection level default)
    static const double MaxTotalLotSize;         // Maximum Total Lot Size (0 = use protection level default)
    static const double MaxSpread;                // Maximum Allowed Spread (0 = use protection level default)

    // Account Protection Infomation
    static const string AccProtectionName;  // Account Protection Object Name
    static const string AccProtectionDesc;  // Account Protection Object Description

    // Monitoring Settings
    static const bool EnableAccountProtection;   // Enable Account Protection System
    static const bool LogProtectionEvents;       // Log Protection Events

    // Account Protection Instance
    AccountProtection* m_accountProtection;      // Account protection instance
    AccountProtectionConfig* m_config;           // Config instance (for external config management)

public:
    //--- Constructor (immutable pattern - all configuration set during construction)
    AccountProtectionInit() : MainPipeline(INIT_VARIABLES, "AccountProtectionInit")
    {
        // Initialize member variables
        m_accountProtection = NULL;
        m_config = NULL;

        // Create AccountProtectionConfig first, then pass to AccountProtection
        // All configuration is set during construction and cannot be modified afterward
        if (ProtectionLevel == PROTECTION_CUSTOM)
        {
            // Create custom configuration with specific parameters
            m_config = new AccountProtectionConfig(
                MaxLossPercent,
                MaxDrawdownPercent,
                MaxDailyLoss,
                MaxOpenOrders,
                MaxLotSize,
                MaxSpread,
                MaxTotalLotSize
            );

            // Create AccountProtection with external config
            m_accountProtection = new AccountProtection(m_config);
        }
        else
        {
            // Use protection level constructor (creates config internally)
            m_accountProtection = new AccountProtection(ProtectionLevel);
            // m_config remains NULL as config is managed internally
        }

        // Set enabled state (this is not part of immutable config, it's a BaseComponent feature)
        if (m_accountProtection != NULL)
        {
            m_accountProtection.SetEnabled(EnableAccountProtection);
        }
    }

    //--- Destructor
    ~AccountProtectionInit()
    {
        // Cleanup external config if we created it
        if (m_config != NULL)
        {
            delete m_config;
            m_config = NULL;
        }
        // AccountProtection cleanup handled by framework
    }

protected:
    //--- Main initialization logic (immutable pattern - no configuration after construction)
    virtual void Main() override
    {
        Print("=== Account Protection Initialization Started ===");

        // Step 1: Initialize account protection system (configuration already set in constructor)
        if (!InitializeAccountProtection())
        {
            SetResult(false, "Failed to initialize account protection system");
            return;
        }

        // Step 2: Register account protection instance
        if (!RegisterAccountProtection())
        {
            SetResult(false, "Failed to register account protection instance");
            return;
        }

        // Step 3: Log successful initialization
        LogInitializationSuccess();
        SetResult(true, "Account protection system initialized successfully");
    }

private:
    //--- Helper methods (ConfigureCustomParameters removed for immutable pattern)
    bool InitializeAccountProtection();
    bool RegisterAccountProtection();
    void LogInitializationSuccess();
};

//+------------------------------------------------------------------+
//| Account Protection Parameters                                   |
//| Self-contained module parameters following EA_Wizard guidelines |
//+------------------------------------------------------------------+

// Protection Level Settings
const ENUM_PROTECTION_LEVEL AccountProtectionInit::ProtectionLevel = PROTECTION_MODERATE;

const double AccountProtectionInit::MaxLossPercent = 20.0;
const double AccountProtectionInit::MaxDailyLoss = 0.0;
const double AccountProtectionInit::MaxDrawdownPercent = 30.0;

// Position Limits
const int AccountProtectionInit::MaxOpenOrders = 20;
const double AccountProtectionInit::MaxLotSize = 2.0;
const double AccountProtectionInit::MaxTotalLotSize = 10.0;
const double AccountProtectionInit::MaxSpread = 5.0;

// Account Protection Infomation
const string AccountProtectionInit::AccProtectionName = EA_OBJ_ACCOUNT_PROTECTION;
const string AccountProtectionInit::AccProtectionDesc = EA_OBJ_ACCOUNT_PROTECTION_DESC;

// Monitoring Settings
const bool AccountProtectionInit::EnableAccountProtection = EA_OBJ_ACCOUNT_PROTECTION_ENABLED;
const bool AccountProtectionInit::LogProtectionEvents = EA_OBJ_ACCOUNT_PROTECTION_LOG_EVENTS;

// Create instance for automatic registration
AccountProtectionInit account_protection_init_stage;

//--- ConfigureCustomParameters method removed for immutable pattern
//--- All configuration is now set during AccountProtection construction

//--- Initialize account protection system
bool AccountProtectionInit::InitializeAccountProtection()
{
    Print("Initializing account protection system...");

    // Check account protection instance
    if (m_accountProtection == NULL)
    {
        Print("ERROR: Failed to create account protection instance");
        return false;
    }

    // Check if account protection is enabled
    if (!m_accountProtection.IsEnabled())
    {
        Print("Account protection is disabled by user settings");
        return false;
    }

    // Initialize the account protection system
    if (!m_accountProtection.Initialize())
    {
        Print("ERROR: Account protection initialization failed: ",
                m_accountProtection.GetLastErrorMessage());
        delete m_accountProtection;
        m_accountProtection = NULL;
        return false;
    }

    // Validate custom parameters
    if (!m_accountProtection.Validate())
    {
        Print("ERROR: Invalid account protection parameters: ",
                m_accountProtection.GetLastErrorMessage());
        return false;
    }

    Print("Account protection system initialized with level: ",
            EnumToString(m_accountProtection.GetProtectionLevel()));
    return true;
}

//--- Register account protection instance with framework
bool AccountProtectionInit::RegisterAccountProtection()
{
    if (!m_accountProtection.IsEnabled() || m_accountProtection == NULL)
    {
        Print("Account protection registration skipped (disabled or not initialized)");
        return true;
    }

    Print("Registering account protection with EA_Wizard framework...");

    // Register with the framework's object registry
    if (!Register(m_accountProtection, AccProtectionName, AccProtectionDesc))
    {
        Print("ERROR: Failed to register account protection with framework");
        return false;
    }

    Print("Account protection registered successfully");
    return true;
}

//--- Log successful initialization
void AccountProtectionInit::LogInitializationSuccess()
{
    if (!LogProtectionEvents) return;

    Print("=== Account Protection Initialization Summary ===");
    Print("Protection Level: ", EnumToString(m_accountProtection.GetProtectionLevel()));
    Print("Protection Enabled: ", m_accountProtection.IsEnabled() ? "YES" : "NO");

    if (m_accountProtection.IsEnabled() && m_accountProtection != NULL)
    {
        Print("Max Loss Percent: ", DoubleToString(m_accountProtection.GetMaxLossPercent(), 2), "%");
        Print("Current Status: ", m_accountProtection.GetStatusDescription());
        Print("Trading Allowed: ", m_accountProtection.IsTradingAllowed() ? "YES" : "NO");
    }

    Print("=== Initialization Complete ===");
}

#endif // ACCOUNT_PROTECTION_INIT_MQH

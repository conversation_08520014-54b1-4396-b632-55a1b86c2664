# 馬丁格爾風險管理模組 - 完整輸入參數清單

## 概述

本文檔列出了馬丁格爾風險管理模組的所有輸入參數，基於 `Martingale\PRD.txt` 中的功能需求規格。這些參數控制著馬丁格爾策略的核心風險管理機制。

---

## 1. 馬丁格爾策略設定 (Martingale Strategy Settings)

### 1.1 序列啟動控制 (FR-1)

| 參數名稱 | 類型 | 預設值 | 說明 | 風險等級 |
|---------|------|--------|------|----------|
| `MG_Initial_Lot` | `double` | `0.01` | 初始手數 - 馬丁格爾序列的第一張訂單手數 | 🟡 中等 |

**驗收標準**: 序列的第一張訂單手數必須等於 `MG_Initial_Lot`

### 1.2 加倉邏輯設定 (FR-2.1, FR-2.2)

| 參數名稱 | 類型 | 預設值 | 說明 | 風險等級 |
|---------|------|--------|------|----------|
| `MG_Grid_Step_Points` | `int` | `300` | 加倉間隔點數 - 價格反向運行多少點後執行下一次加倉 | 🟡 中等 |
| `MG_Lot_Multiplier` | `double` | `1.8` | 手數乘數 - 每次加倉時相對於上一張訂單的乘數<br/>⚠️ **警告**: >1.6 為高風險設定 | 🔴 高風險 |

**驗收標準**: 
- 只有當 `|現價 - 上一張訂單開倉價| >= MG_Grid_Step_Points` 時，才允許觸發下一次加倉
- 第 N 層訂單的手數 = (第 N-1 層訂單的手數) × `MG_Lot_Multiplier`

### 1.3 盈利目標設定 (FR-2.4)

| 參數名稱 | 類型 | 預設值 | 說明 | 風險等級 |
|---------|------|--------|------|----------|
| `MG_Overall_Profit_Target_Amount` | `double` | `10.0` | 整體盈利目標 (帳戶貨幣) - 達到此金額時平掉所有序列訂單 | 🟢 低風險 |

**驗收標準**: 當 (所有序列訂單的總利潤) >= `MG_Overall_Profit_Target_Amount` 時，所有訂單被成功平倉

---

## 2. 核心風險控制 (CORE RISK CONTROLS) 🚨

### 2.1 最大加倉層級限制 (FR-2.3) - 最重要設定

| 參數名稱 | 類型 | 預設值 | 說明 | 風險等級 |
|---------|------|--------|------|----------|
| `MG_Max_Levels` | `int` | `6` | **🚨 最大加倉層級** - 整個序列允許的最大訂單數量<br/>建議範圍: 5-8<br/>⚠️ **這是最重要的安全閥設定** | 🔴 極高風險 |

**驗收標準**: 當持倉數量達到 `MG_Max_Levels` 時，EA 必須立即停止任何新的加倉行為

### 2.2 到達上限處理機制 (FR-3.1)

| 參數名稱 | 類型 | 預設值 | 可選值 | 說明 | 風險等級 |
|---------|------|--------|--------|------|----------|
| `MG_Action_On_Max_Level` | `enum` | `CLOSE_IMMEDIATELY` | `CLOSE_IMMEDIATELY`<br/>`WAIT_FOR_BREAKEVEN` | 到達最大層級後的處理方案<br/>• **立即平倉** (強烈建議)<br/>• **等待回本** (高風險) | 🔴 極高風險 |

**枚舉定義**:
```mql4
enum EnumActionOnMaxLevel {
    CLOSE_IMMEDIATELY,  // 到達上限立即平倉 (Recommended)
    WAIT_FOR_BREAKEVEN  // 等待回本 (High Risk)
};
```

**驗收標準**: EA 能根據使用者選擇，在觸發 `MG_Max_Levels` 時準確執行對應操作

---

## 3. 終極帳戶保護 (Ultimate Account Protection) 🛡️

### 3.1 帳戶級別淨值保護 (FR-3.2)

| 參數名稱 | 類型 | 預設值 | 說明 | 風險等級 |
|---------|------|--------|------|----------|
| `MG_Equity_Stop_Percent` | `double` | `30.0` | 帳戶淨值虧損保護百分比<br/>• 設定為 `0` 表示不啟用<br/>• 當淨值低於 (初始淨值 × (1 - 此值/100)) 時強制平倉 | 🟢 保護機制 |

**驗收標準**: 當帳戶淨值觸及虧損線時，所有訂單被平倉，且EA在日誌中輸出警告並停止交易活動

**計算公式**: 
```
保護觸發線 = 初始淨值 × (1 - MG_Equity_Stop_Percent / 100)
```

---

## 4. 完整 MQL4 輸入參數代碼

```mql4
//+------------------------------------------------------------------+
//| 馬丁格爾風險管理模組 - EA 輸入參數                               |
//+------------------------------------------------------------------+

//--- Martingale Strategy Inputs ---
extern string  MG_Section_Title = "====== Martingale Settings (HIGH RISK) ======";

// FR-1 & FR-2: Sequence Settings
extern double  MG_Initial_Lot = 0.01;                 // 初始手數
extern int     MG_Grid_Step_Points = 300;             // 加倉間隔點數 (Points)
extern double  MG_Lot_Multiplier = 1.8;               // 手數乘數 (e.g., 1.6, 1.8, 2.0) - WARNING: >1.6 is very aggressive
extern double  MG_Overall_Profit_Target_Amount = 10.0;// 整體盈利目標 ($)

// FR-2 & FR-3: CORE RISK CONTROLS
extern string  MG_Risk_Control_Title = "====== CORE RISK CONTROLS ======";
extern int     MG_Max_Levels = 6;                     // !!! 最大加倉層級 (e.g., 5-8) - MOST IMPORTANT SETTING !!!

enum EnumActionOnMaxLevel {
    CLOSE_IMMEDIATELY,  // 到達上限立即平倉 (Recommended)
    WAIT_FOR_BREAKEVEN  // 等待回本 (High Risk)
};
extern EnumActionOnMaxLevel MG_Action_On_Max_Level = CLOSE_IMMEDIATELY; // 到達上限後的操作

// FR-3: Ultimate Circuit Breaker
extern string  MG_Account_Protection_Title = "====== Account Protection ======";
extern double  MG_Equity_Stop_Percent = 30.0;         // 帳戶淨值虧損保護 (%) (0 為不啟用)
```

---

## 5. 風險等級說明

| 風險等級 | 圖示 | 說明 |
|----------|------|------|
| 🟢 低風險/保護機制 | 綠色 | 保護性參數，降低風險 |
| 🟡 中等風險 | 黃色 | 需要謹慎設定的參數 |
| 🔴 高風險 | 紅色 | 可能導致重大虧損的參數 |
| 🔴 極高風險 | 紅色 | 最關鍵的風險控制參數 |

---

## 6. 建議設定組合

### 6.1 保守型設定 (推薦新手)
```
MG_Max_Levels = 4
MG_Initial_Lot = 0.01
MG_Grid_Step_Points = 400
MG_Lot_Multiplier = 1.5
MG_Overall_Profit_Target_Amount = 5.0
MG_Equity_Stop_Percent = 20.0
MG_Action_On_Max_Level = CLOSE_IMMEDIATELY
```

### 6.2 平衡型設定 (中等經驗)
```
MG_Max_Levels = 6
MG_Initial_Lot = 0.01
MG_Grid_Step_Points = 300
MG_Lot_Multiplier = 1.8
MG_Overall_Profit_Target_Amount = 10.0
MG_Equity_Stop_Percent = 30.0
MG_Action_On_Max_Level = CLOSE_IMMEDIATELY
```

### 6.3 激進型設定 (⚠️ 高風險)
```
MG_Max_Levels = 8
MG_Initial_Lot = 0.02
MG_Grid_Step_Points = 250
MG_Lot_Multiplier = 2.0
MG_Overall_Profit_Target_Amount = 20.0
MG_Equity_Stop_Percent = 40.0
MG_Action_On_Max_Level = WAIT_FOR_BREAKEVEN
```

---

## 7. 重要提醒 ⚠️

1. **`MG_Max_Levels` 是最重要的安全閥** - 這個參數直接決定了最大可能虧損
2. **`MG_Lot_Multiplier` > 1.6 為高風險設定** - 乘數越大，虧損增長越快
3. **強烈建議使用 `CLOSE_IMMEDIATELY`** - 避免長時間被套牢
4. **`MG_Equity_Stop_Percent` 是最後防線** - 保護整個帳戶免於災難性虧損
5. **在實盤使用前務必進行充分的回測** - 了解各種市場條件下的表現

---

*文檔版本: 1.0*  
*最後更新: 2024年*  
*基於: Martingale\PRD.txt 功能需求規格*

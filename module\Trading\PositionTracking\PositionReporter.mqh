//+------------------------------------------------------------------+
//|                                           PositionReporter.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_REPORTER_MQH
#define POSITION_REPORTER_MQH

#include "../../Base/BaseComponent.mqh"
#include "PositionCollector.mqh"
#include "PositionStatistics.mqh"
#include "PositionRiskAnalyzer.mqh"

//+------------------------------------------------------------------+
//| Report Format Enumeration                                        |
//+------------------------------------------------------------------+
enum ENUM_REPORT_FORMAT
{
    REPORT_SUMMARY,         // Summary format
    REPORT_DETAILED,        // Detailed format
    REPORT_RISK_ANALYSIS,   // Risk analysis format
    REPORT_CUSTOM           // Custom format
};

//+------------------------------------------------------------------+
//| Report Configuration Structure                                   |
//+------------------------------------------------------------------+
struct ReportConfig
{
    bool              includePositions;     // Include position details
    bool              includeStatistics;    // Include statistics
    bool              includeRiskAnalysis;  // Include risk analysis
    bool              includeTimestamp;     // Include timestamp
    bool              useColors;            // Use color formatting (for terminals that support it)
    int               decimalPlaces;        // Decimal places for numbers
    string            separator;            // Field separator
};

//+------------------------------------------------------------------+
//| PositionReporter Class                                           |
//| Responsible for formatting and displaying position reports       |
//+------------------------------------------------------------------+
class PositionReporter : public BaseComponent
{
private:
    ReportConfig      m_config;             // Report configuration

    // Error codes specific to PositionReporter
    static const BaseErrorDescriptor CODE_ERRORS[];

public:
    //--- Constructor and Destructor
                      PositionReporter();
    virtual          ~PositionReporter();

    //--- Configuration methods
    void              SetReportConfig(const ReportConfig& config) { m_config = config; }
    ReportConfig      GetReportConfig() const { return m_config; }
    void              SetIncludePositions(bool include) { m_config.includePositions = include; }
    void              SetIncludeStatistics(bool include) { m_config.includeStatistics = include; }
    void              SetIncludeRiskAnalysis(bool include) { m_config.includeRiskAnalysis = include; }
    void              SetDecimalPlaces(int places) { m_config.decimalPlaces = MathMax(0, places); }

    //--- Main reporting methods
    void              PrintPositionSummary(const PositionStats& stats);
    void              PrintDetailedPositions(PositionInfo& positions[], int count);
    void              PrintRiskAnalysis(const RiskAnalysisResult& riskAnalysis);
    void              PrintCompleteReport(PositionCollector* collector, PositionStatistics* statistics, PositionRiskAnalyzer* riskAnalyzer);

    //--- Formatted output methods
    string            FormatPositionSummary(const PositionStats& stats);
    string            FormatDetailedPositions(PositionInfo& positions[], int count);
    string            FormatRiskAnalysis(const RiskAnalysisResult& riskAnalysis);
    string            FormatCompleteReport(PositionCollector* collector, PositionStatistics* statistics, PositionRiskAnalyzer* riskAnalyzer);

    //--- Utility formatting methods
    string            FormatNumber(double value, int decimals = -1);
    string            FormatPercentage(double value);
    string            FormatCurrency(double value);
    string            FormatDateTime(datetime time);
    string            PositionTypeToString(int type);
    string            GetReportHeader(ENUM_REPORT_FORMAT format);
    string            GetReportFooter();

    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;

private:
    //--- Internal formatting methods
    string            CreateSeparatorLine(int length = 50);
    string            PadString(string text, int width, bool rightAlign = false);
    string            FormatTableRow(string col1, string col2, string col3 = "", string col4 = "", string col5 = "", string col6 = "");
};

// Error codes for PositionReporter
const BaseErrorDescriptor PositionReporter::CODE_ERRORS[] = {
    {1341, "Invalid report configuration"},
    {1342, "Failed to generate report"},
    {1343, "Invalid position data for reporting"},
    {1344, "Report formatting error"}
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionReporter::PositionReporter() : BaseComponent("PositionReporter")
{
    // Initialize default configuration
    m_config.includePositions = true;
    m_config.includeStatistics = true;
    m_config.includeRiskAnalysis = true;
    m_config.includeTimestamp = true;
    m_config.useColors = false;
    m_config.decimalPlaces = 2;
    m_config.separator = " | ";
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionReporter::~PositionReporter()
{
    // No dynamic memory to clean up
}

//+------------------------------------------------------------------+
//| Initialize position reporter                                     |
//+------------------------------------------------------------------+
bool PositionReporter::OnInitialize()
{
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionReporter::OnValidate()
{
    if (m_config.decimalPlaces < 0 || m_config.decimalPlaces > 8)
    {
        HandleError(1341, GetErrorDescription(1341));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update position reporter                                         |
//+------------------------------------------------------------------+
bool PositionReporter::OnUpdate()
{
    return true;
}

//+------------------------------------------------------------------+
//| Print position summary                                           |
//+------------------------------------------------------------------+
void PositionReporter::PrintPositionSummary(const PositionStats& stats)
{
    string report = FormatPositionSummary(stats);
    Print(report);
}

//+------------------------------------------------------------------+
//| Print detailed positions                                         |
//+------------------------------------------------------------------+
void PositionReporter::PrintDetailedPositions(PositionInfo& positions[], int count)
{
    string report = FormatDetailedPositions(positions, count);
    Print(report);
}

//+------------------------------------------------------------------+
//| Print risk analysis                                              |
//+------------------------------------------------------------------+
void PositionReporter::PrintRiskAnalysis(const RiskAnalysisResult& riskAnalysis)
{
    string report = FormatRiskAnalysis(riskAnalysis);
    Print(report);
}

//+------------------------------------------------------------------+
//| Print complete report                                            |
//+------------------------------------------------------------------+
void PositionReporter::PrintCompleteReport(PositionCollector* collector, PositionStatistics* statistics, PositionRiskAnalyzer* riskAnalyzer)
{
    if (collector == NULL || statistics == NULL || riskAnalyzer == NULL)
    {
        HandleError(1343, GetErrorDescription(1343));
        return;
    }

    string report = FormatCompleteReport(collector, statistics, riskAnalyzer);
    Print(report);
}

//+------------------------------------------------------------------+
//| Format position summary                                          |
//+------------------------------------------------------------------+
string PositionReporter::FormatPositionSummary(const PositionStats& stats)
{
    string report = "";

    if (m_config.includeTimestamp)
    {
        report += "Report Time: " + FormatDateTime(TimeCurrent()) + "\n";
    }

    report += GetReportHeader(REPORT_SUMMARY) + "\n";
    report += FormatTableRow("Total Positions:", IntegerToString(stats.totalPositions)) + "\n";
    report += FormatTableRow("Buy Positions:", IntegerToString(stats.buyPositions),
                           FormatNumber(stats.buyLots) + " lots") + "\n";
    report += FormatTableRow("Sell Positions:", IntegerToString(stats.sellPositions),
                           FormatNumber(stats.sellLots) + " lots") + "\n";
    report += FormatTableRow("Total P&L:", FormatCurrency(stats.totalProfit)) + "\n";
    report += FormatTableRow("Net Exposure:", FormatNumber(stats.buyLots - stats.sellLots) + " lots") + "\n";
    report += GetReportFooter();

    return report;
}

//+------------------------------------------------------------------+
//| Format detailed positions                                        |
//+------------------------------------------------------------------+
string PositionReporter::FormatDetailedPositions(PositionInfo& positions[], int count)
{
    string report = "";

    if (count < 0)
    {
        HandleError(1343, GetErrorDescription(1343));
        return report;
    }

    report += GetReportHeader(REPORT_DETAILED) + "\n";
    report += FormatTableRow("Ticket", "Type", "Lots", "Open Price", "Current", "P&L") + "\n";
    report += CreateSeparatorLine(70) + "\n";

    for (int i = 0; i < count; i++)
    {
        PositionInfo pos = positions[i];
        report += FormatTableRow(
            IntegerToString(pos.ticket),
            PositionTypeToString(pos.type),
            FormatNumber(pos.lotSize),
            FormatNumber(pos.openPrice, Digits),
            FormatNumber(pos.currentPrice, Digits),
            FormatCurrency(pos.totalPL)
        ) + "\n";
    }

    report += GetReportFooter();
    return report;
}

//+------------------------------------------------------------------+
//| Format risk analysis                                             |
//+------------------------------------------------------------------+
string PositionReporter::FormatRiskAnalysis(const RiskAnalysisResult& riskAnalysis)
{
    string report = "";

    report += GetReportHeader(REPORT_RISK_ANALYSIS) + "\n";
    report += FormatTableRow("Current Drawdown:", FormatPercentage(riskAnalysis.currentDrawdown)) + "\n";
    report += FormatTableRow("Max Drawdown:", FormatPercentage(riskAnalysis.maxDrawdown)) + "\n";
    report += FormatTableRow("Profit Factor:", FormatNumber(riskAnalysis.profitFactor)) + "\n";
    report += FormatTableRow("Win Rate:", FormatPercentage(riskAnalysis.winRate)) + "\n";
    report += FormatTableRow("Exposure Ratio:", FormatPercentage(riskAnalysis.exposureRatio)) + "\n";
    report += FormatTableRow("Risk Level:", riskAnalysis.riskLevel) + "\n";
    report += FormatTableRow("Risk Limit Status:",
                           riskAnalysis.riskLimitExceeded ? "EXCEEDED" : "OK") + "\n";
    report += GetReportFooter();

    return report;
}

//+------------------------------------------------------------------+
//| Format complete report                                           |
//+------------------------------------------------------------------+
string PositionReporter::FormatCompleteReport(PositionCollector* collector, PositionStatistics* statistics, PositionRiskAnalyzer* riskAnalyzer)
{
    string report = "";

    if (m_config.includeTimestamp)
    {
        report += "Complete Position Report - " + FormatDateTime(TimeCurrent()) + "\n\n";
    }

    if (m_config.includeStatistics)
    {
        PositionStats stats = statistics.GetCurrentStats();
        report += FormatPositionSummary(stats) + "\n\n";
    }

    if (m_config.includePositions)
    {
        PositionInfo positions[];
        collector.GetAllPositions(positions);
        int count = collector.GetPositionCount();
        report += FormatDetailedPositions(positions, count) + "\n\n";
    }

    if (m_config.includeRiskAnalysis)
    {
        RiskAnalysisResult riskAnalysis = riskAnalyzer.GetLastAnalysis();
        report += FormatRiskAnalysis(riskAnalysis) + "\n";
    }

    return report;
}

//+------------------------------------------------------------------+
//| Format number with specified decimal places                     |
//+------------------------------------------------------------------+
string PositionReporter::FormatNumber(double value, int decimals = -1)
{
    int places = (decimals >= 0) ? decimals : m_config.decimalPlaces;
    return DoubleToString(value, places);
}

//+------------------------------------------------------------------+
//| Format percentage                                                |
//+------------------------------------------------------------------+
string PositionReporter::FormatPercentage(double value)
{
    return FormatNumber(value) + "%";
}

//+------------------------------------------------------------------+
//| Format currency                                                  |
//+------------------------------------------------------------------+
string PositionReporter::FormatCurrency(double value)
{
    return FormatNumber(value) + " " + AccountCurrency();
}

//+------------------------------------------------------------------+
//| Format date and time                                             |
//+------------------------------------------------------------------+
string PositionReporter::FormatDateTime(datetime time)
{
    return TimeToString(time, TIME_DATE | TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Convert position type to string                                 |
//+------------------------------------------------------------------+
string PositionReporter::PositionTypeToString(int type)
{
    switch(type)
    {
        case OP_BUY:  return "BUY";
        case OP_SELL: return "SELL";
        default:      return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Get report header                                                |
//+------------------------------------------------------------------+
string PositionReporter::GetReportHeader(ENUM_REPORT_FORMAT format)
{
    string header = CreateSeparatorLine(50) + "\n";

    switch(format)
    {
        case REPORT_SUMMARY:
            header += "           POSITION SUMMARY\n";
            break;
        case REPORT_DETAILED:
            header += "          DETAILED POSITIONS\n";
            break;
        case REPORT_RISK_ANALYSIS:
            header += "           RISK ANALYSIS\n";
            break;
        default:
            header += "           POSITION REPORT\n";
            break;
    }

    header += CreateSeparatorLine(50);
    return header;
}

//+------------------------------------------------------------------+
//| Get report footer                                                |
//+------------------------------------------------------------------+
string PositionReporter::GetReportFooter()
{
    return CreateSeparatorLine(50);
}

//+------------------------------------------------------------------+
//| Create separator line                                            |
//+------------------------------------------------------------------+
string PositionReporter::CreateSeparatorLine(int length = 50)
{
    string line = "";
    for (int i = 0; i < length; i++)
    {
        line += "=";
    }
    return line;
}

//+------------------------------------------------------------------+
//| Pad string to specified width                                    |
//+------------------------------------------------------------------+
string PositionReporter::PadString(string text, int width, bool rightAlign = false)
{
    int textLen = StringLen(text);
    if (textLen >= width) return text;

    string padding = "";
    for (int i = 0; i < width - textLen; i++)
    {
        padding += " ";
    }

    return rightAlign ? padding + text : text + padding;
}

//+------------------------------------------------------------------+
//| Format table row                                                 |
//+------------------------------------------------------------------+
string PositionReporter::FormatTableRow(string col1, string col2, string col3 = "", string col4 = "", string col5 = "", string col6 = "")
{
    string row = PadString(col1, 12) + m_config.separator + PadString(col2, 8);

    if (col3 != "")
    {
        row += m_config.separator + PadString(col3, 8);
    }

    if (col4 != "")
    {
        row += m_config.separator + PadString(col4, 12);
    }

    if (col5 != "")
    {
        row += m_config.separator + PadString(col5, 12);
    }

    if (col6 != "")
    {
        row += m_config.separator + PadString(col6, 12);
    }

    return row;
}

#endif // POSITION_REPORTER_MQH

//+------------------------------------------------------------------+
//|                                              IndicatorUtils.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef INDICATOR_UTILS_MQH
#define INDICATOR_UTILS_MQH

//+------------------------------------------------------------------+
//| IndicatorUtils Class                                             |
//| Common utilities for technical indicator calculations           |
//+------------------------------------------------------------------+
class IndicatorUtils
{
public:
    //--- Price calculation utilities
    static double     CalculateTypicalPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift);
    static double     CalculateWeightedPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift);
    static double     CalculateMedianPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift);
    static double     CalculateTrueRange(string symbol, ENUM_TIMEFRAMES timeframe, int shift);
    
    //--- Moving average utilities
    static double     CalculateSimpleMA(const double& array[], int period, int shift);
    static double     CalculateExponentialMA(const double& array[], int period, int shift, double prevEMA);
    static double     CalculateSmoothedMA(const double& array[], int period, int shift, double prevSMA);
    static double     CalculateLinearWeightedMA(const double& array[], int period, int shift);
    
    //--- Statistical utilities
    static double     CalculateStandardDeviation(const double& array[], int period, int shift, double mean);
    static double     CalculateVariance(const double& array[], int period, int shift, double mean);
    static double     CalculateCorrelation(const double& array1[], const double& array2[], int period, int shift);
    
    //--- Signal processing utilities
    static bool       IsArrayIncreasing(const double& array[], int period, int shift);
    static bool       IsArrayDecreasing(const double& array[], int period, int shift);
    static int        FindArrayPeak(const double& array[], int startShift, int endShift, bool findMax = true);
    static double     CalculateSlope(const double& array[], int period, int shift);
    
    //--- Validation utilities
    static bool       ValidateArray(const double& array[], int size);
    static bool       ValidateShift(int shift, int maxBars);
    static bool       ValidatePeriod(int period, int minPeriod = 1, int maxPeriod = 1000);
    static bool       ValidatePrice(double price);
    
    //--- Conversion utilities
    static int        TimeframeToMinutes(ENUM_TIMEFRAMES timeframe);
    static string     TimeframeToString(ENUM_TIMEFRAMES timeframe);
    static string     SignalTypeToString(int signalType);
    static string     SignalStrengthToString(int strength);
};

//+------------------------------------------------------------------+
//| Calculate typical price (HLC/3)                                 |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateTypicalPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift)
{
    double high = iHigh(symbol, timeframe, shift);
    double low = iLow(symbol, timeframe, shift);
    double close = iClose(symbol, timeframe, shift);
    
    if (high == EMPTY_VALUE || low == EMPTY_VALUE || close == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    return (high + low + close) / 3.0;
}

//+------------------------------------------------------------------+
//| Calculate weighted price (HLCC/4)                               |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateWeightedPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift)
{
    double high = iHigh(symbol, timeframe, shift);
    double low = iLow(symbol, timeframe, shift);
    double close = iClose(symbol, timeframe, shift);
    
    if (high == EMPTY_VALUE || low == EMPTY_VALUE || close == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    return (high + low + 2 * close) / 4.0;
}

//+------------------------------------------------------------------+
//| Calculate median price (HL/2)                                   |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateMedianPrice(string symbol, ENUM_TIMEFRAMES timeframe, int shift)
{
    double high = iHigh(symbol, timeframe, shift);
    double low = iLow(symbol, timeframe, shift);
    
    if (high == EMPTY_VALUE || low == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    return (high + low) / 2.0;
}

//+------------------------------------------------------------------+
//| Calculate True Range                                             |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateTrueRange(string symbol, ENUM_TIMEFRAMES timeframe, int shift)
{
    double high = iHigh(symbol, timeframe, shift);
    double low = iLow(symbol, timeframe, shift);
    double prevClose = iClose(symbol, timeframe, shift + 1);
    
    if (high == EMPTY_VALUE || low == EMPTY_VALUE || prevClose == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    double tr1 = high - low;
    double tr2 = MathAbs(high - prevClose);
    double tr3 = MathAbs(low - prevClose);
    
    return MathMax(tr1, MathMax(tr2, tr3));
}

//+------------------------------------------------------------------+
//| Calculate Simple Moving Average                                  |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateSimpleMA(const double& array[], int period, int shift)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift + period > ArraySize(array))
        return EMPTY_VALUE;
    
    double sum = 0.0;
    for (int i = 0; i < period; i++)
    {
        if (array[shift + i] == EMPTY_VALUE)
            return EMPTY_VALUE;
        sum += array[shift + i];
    }
    
    return sum / period;
}

//+------------------------------------------------------------------+
//| Calculate Exponential Moving Average                             |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateExponentialMA(const double& array[], int period, int shift, double prevEMA)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift >= ArraySize(array))
        return EMPTY_VALUE;
    
    if (array[shift] == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    double alpha = 2.0 / (period + 1.0);
    return alpha * array[shift] + (1.0 - alpha) * prevEMA;
}

//+------------------------------------------------------------------+
//| Calculate Smoothed Moving Average                                |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateSmoothedMA(const double& array[], int period, int shift, double prevSMA)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift >= ArraySize(array))
        return EMPTY_VALUE;
    
    if (array[shift] == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    return (prevSMA * (period - 1) + array[shift]) / period;
}

//+------------------------------------------------------------------+
//| Calculate Linear Weighted Moving Average                         |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateLinearWeightedMA(const double& array[], int period, int shift)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift + period > ArraySize(array))
        return EMPTY_VALUE;
    
    double sum = 0.0;
    double weightSum = 0.0;
    
    for (int i = 0; i < period; i++)
    {
        if (array[shift + i] == EMPTY_VALUE)
            return EMPTY_VALUE;
        
        int weight = period - i;
        sum += array[shift + i] * weight;
        weightSum += weight;
    }
    
    return (weightSum > 0) ? sum / weightSum : EMPTY_VALUE;
}

//+------------------------------------------------------------------+
//| Calculate Standard Deviation                                     |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateStandardDeviation(const double& array[], int period, int shift, double mean)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift + period > ArraySize(array))
        return EMPTY_VALUE;
    
    double sum = 0.0;
    for (int i = 0; i < period; i++)
    {
        if (array[shift + i] == EMPTY_VALUE)
            return EMPTY_VALUE;
        
        double diff = array[shift + i] - mean;
        sum += diff * diff;
    }
    
    return MathSqrt(sum / period);
}

//+------------------------------------------------------------------+
//| Calculate Variance                                               |
//+------------------------------------------------------------------+
static double IndicatorUtils::CalculateVariance(const double& array[], int period, int shift, double mean)
{
    if (!ValidateArray(array, ArraySize(array)) || !ValidatePeriod(period))
        return EMPTY_VALUE;
    
    if (shift + period > ArraySize(array))
        return EMPTY_VALUE;
    
    double sum = 0.0;
    for (int i = 0; i < period; i++)
    {
        if (array[shift + i] == EMPTY_VALUE)
            return EMPTY_VALUE;
        
        double diff = array[shift + i] - mean;
        sum += diff * diff;
    }
    
    return sum / period;
}

//+------------------------------------------------------------------+
//| Check if array is increasing                                    |
//+------------------------------------------------------------------+
static bool IndicatorUtils::IsArrayIncreasing(const double& array[], int period, int shift)
{
    if (!ValidateArray(array, ArraySize(array)) || period < 2)
        return false;
    
    if (shift + period > ArraySize(array))
        return false;
    
    for (int i = 0; i < period - 1; i++)
    {
        if (array[shift + i] == EMPTY_VALUE || array[shift + i + 1] == EMPTY_VALUE)
            return false;
        
        if (array[shift + i] <= array[shift + i + 1])
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if array is decreasing                                    |
//+------------------------------------------------------------------+
static bool IndicatorUtils::IsArrayDecreasing(const double& array[], int period, int shift)
{
    if (!ValidateArray(array, ArraySize(array)) || period < 2)
        return false;
    
    if (shift + period > ArraySize(array))
        return false;
    
    for (int i = 0; i < period - 1; i++)
    {
        if (array[shift + i] == EMPTY_VALUE || array[shift + i + 1] == EMPTY_VALUE)
            return false;
        
        if (array[shift + i] >= array[shift + i + 1])
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Find array peak (max or min)                                    |
//+------------------------------------------------------------------+
static int IndicatorUtils::FindArrayPeak(const double& array[], int startShift, int endShift, bool findMax = true)
{
    if (!ValidateArray(array, ArraySize(array)) || startShift >= endShift)
        return -1;
    
    if (endShift > ArraySize(array))
        endShift = ArraySize(array);
    
    int peakIndex = startShift;
    double peakValue = array[startShift];
    
    for (int i = startShift + 1; i < endShift; i++)
    {
        if (array[i] == EMPTY_VALUE)
            continue;
        
        if (findMax)
        {
            if (array[i] > peakValue)
            {
                peakValue = array[i];
                peakIndex = i;
            }
        }
        else
        {
            if (array[i] < peakValue)
            {
                peakValue = array[i];
                peakIndex = i;
            }
        }
    }
    
    return peakIndex;
}

//+------------------------------------------------------------------+
//| Validate array                                                  |
//+------------------------------------------------------------------+
static bool IndicatorUtils::ValidateArray(const double& array[], int size)
{
    return (size > 0);
}

//+------------------------------------------------------------------+
//| Validate shift                                                   |
//+------------------------------------------------------------------+
static bool IndicatorUtils::ValidateShift(int shift, int maxBars)
{
    return (shift >= 0 && shift < maxBars);
}

//+------------------------------------------------------------------+
//| Validate period                                                  |
//+------------------------------------------------------------------+
static bool IndicatorUtils::ValidatePeriod(int period, int minPeriod = 1, int maxPeriod = 1000)
{
    return (period >= minPeriod && period <= maxPeriod);
}

//+------------------------------------------------------------------+
//| Validate price                                                   |
//+------------------------------------------------------------------+
static bool IndicatorUtils::ValidatePrice(double price)
{
    return (price != EMPTY_VALUE && price > 0.0);
}

//+------------------------------------------------------------------+
//| Convert timeframe to minutes                                     |
//+------------------------------------------------------------------+
static int IndicatorUtils::TimeframeToMinutes(ENUM_TIMEFRAMES timeframe)
{
    switch(timeframe)
    {
        case PERIOD_M1:  return 1;
        case PERIOD_M5:  return 5;
        case PERIOD_M15: return 15;
        case PERIOD_M30: return 30;
        case PERIOD_H1:  return 60;
        case PERIOD_H4:  return 240;
        case PERIOD_D1:  return 1440;
        case PERIOD_W1:  return 10080;
        case PERIOD_MN1: return 43200;
        default:         return Period();
    }
}

//+------------------------------------------------------------------+
//| Convert timeframe to string                                      |
//+------------------------------------------------------------------+
static string IndicatorUtils::TimeframeToString(ENUM_TIMEFRAMES timeframe)
{
    switch(timeframe)
    {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN1";
        default:         return "CURRENT";
    }
}

//+------------------------------------------------------------------+
//| Convert signal type to string                                   |
//+------------------------------------------------------------------+
static string IndicatorUtils::SignalTypeToString(int signalType)
{
    switch(signalType)
    {
        case 0:  return "NONE";
        case 1:  return "BUY";
        case -1: return "SELL";
        case 2:  return "NEUTRAL";
        default: return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Convert signal strength to string                               |
//+------------------------------------------------------------------+
static string IndicatorUtils::SignalStrengthToString(int strength)
{
    switch(strength)
    {
        case 1: return "WEAK";
        case 2: return "MEDIUM";
        case 3: return "STRONG";
        default: return "UNKNOWN";
    }
}

#endif // INDICATOR_UTILS_MQH
